#!/bin/sh
set -x -e

cd `dirname $0`

# change to repo root dir
cd ..

buildNumber=""
if [[ -f "build.json" ]];then
buildNumber="`cat build.json`"
fi

go version

nameFlag="-X github.com/AfterShip/library-heytea-go-common/whoami.name=MarketingBizMonitorAPI" \
&& versionFlag="-X github.com/AfterShip/library-heytea-go-common/whoami.version=latest" \
&& buildNumberFlag="-X github.com/AfterShip/library-heytea-go-common/whoami.buildNumber="${buildNumber}"" \
&& buildAtFlag="-X github.com/AfterShip/library-heytea-go-common/whoami.buildAt="`date '+%Y-%m-%dT%H:%M:%S%z'`"" \
&& commitHashFlag="-X github.com/AfterShip/library-heytea-go-common/whoami.commitHash="`git rev-parse HEAD`\
&& commitBranchFlag="-X github.com/AfterShip/library-heytea-go-common/whoami.commitBranch="`git name-rev --name-only HEAD`\
&& ldflags="${nameFlag} ${versionFlag} ${buildNumberFlag} ${buildAtFlag} ${commitHashFlag} ${commitBranchFlag}" \
&& go build  -ldflags "${ldflags}" ./cmd/apiserver
