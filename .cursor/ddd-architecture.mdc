---
globs: ["internal/adapters/*.go", "internal/bizscenarios/*.go", "internal/ports/*.go", "internal/version/*.go"]
---

# DDD Architecture Guidelines

## Core Principles
- **Team Consistency Over Individual Flexibility**: Follow established patterns to reduce cognitive load and decision fatigue
- **Dependency Inversion**: Domain layer should not depend on infrastructure concerns
- **Pure Functions**: Enable comprehensive unit testing, especially in domain layer
- **Transaction Management**: Handle at Application Service level

## Layer Responsibilities

### Application Service Layer
```go
// ✅ CORRECT: Application service manages transactions and orchestrates business processes
func (s *ApplicationService) SendEmail(ctx context.Context, args SendEmailArgs) (domain.Record, error) {
    // 1. Get external data
    template, err := s.ContentPort.GetEmailTemplate(ctx, args.EmailTemplateId)
    sender, err := s.BusinessPort.GetEmailSender(ctx, args.EmailSenderId)
    
    // 2. Render content
    rendered, err := s.ContentPort.RenderEmailTemplate(ctx, template, args.RenderContext)
    
    // 3. Create domain entity
    notif := domain.NewNotificationRecord(rendered, sender, args.Recipient)
    
    // 4. Domain validation
    if ok := domain.IsEmailSendable(ctx, notif); !ok {
        return nil, errors.New("email not sendable")
    }
    
    // 5. External operations
    err := s.MessageInfraPort.SendEmail(ctx, notif)
    
    // 6. Persistence and events
    event := notif.SendEmail()
    err := s.notificationRecordRepository.Save(ctx, notif)
    err := s.MessagingPort.SendEvent(event)
    
    return notif, nil
}
```

**Rules:**
- ✅ Manage transactions at this level
- ✅ Orchestrate business processes (SOP-style workflows)
- ✅ Use adapter ports to call external services
- ✅ Handle external I/O operations
- ✅ Call multiple domain packages
- ❌ Application services should NOT call other application services
- ✅ Use integration tests for this layer

### Domain Entity Layer
```go
// ✅ CORRECT: Entity contains business logic, no external dependencies
type Flow struct {
    ID     string
    Status FlowStatus
    // ... other fields
}

func (f *Flow) Publish() error {
    if f.Status != FlowStatusDraft {
        return errors.New("only draft flows can be published")
    }
    f.Status = FlowStatusPublished
    return nil
}
```

**Rules:**
- ✅ Use pointer receivers and modify entity state directly
- ✅ Contains business logic, policy validation, lifecycle management
- ❌ NEVER include repository or ports in entity fields
- ✅ Must support pure unit testing
- ✅ Co-locate with repository interfaces

### Domain Value Objects
```go
// ✅ CORRECT: Value object with business logic but immutable
type Email struct {
    address string
}

func (e Email) IsValid() bool {
    return strings.Contains(e.address, "@")
}

func (e Email) GetDomain() string {
    parts := strings.Split(e.address, "@")
    return parts[1]
}
```

**Rules:**
- ✅ Contains business logic and validation
- ❌ Should NOT modify internal values
- ✅ Immutable operations only

### Domain Service Layer
```go
// ✅ CORRECT: Domain service for cross-entity business logic
type NotificationDomainService struct {
    riskRepo RiskRepository // Only for read operations
}

func (s *NotificationDomainService) IsEmailSendable(ctx context.Context, notif *Notification) bool {
    // Business policy validation that spans multiple concerns
    if !s.passesRiskCheck(ctx, notif) {
        return false
    }
    return s.checkSubscriptionStatus(ctx, notif)
}
```

**Rules:**
- ✅ Use for business logic that doesn't fit in a single entity
- ✅ Use for cross-entity business operations
- ✅ Can use repository/port READ methods for business policy validation
- ❌ Generally avoid WRITE methods (requires case-by-case review)
- ❌ Domain services should NOT call other domain services
- ✅ Use when business processes need to be reused (Exception 1)

## Repository and Port Patterns

### Repository Interfaces
```go
// ✅ CORRECT: Repository interface defined in domain layer
package domain

type FlowRepository interface {
    Save(ctx context.Context, flow *Flow) error
    FindByID(ctx context.Context, id string) (*Flow, error)
    Delete(ctx context.Context, id string) error
}
```

**Rules:**
- ✅ Define ALL repository interfaces in domain layer
- ✅ Co-locate with entities
- ✅ Include both read and write methods for completeness

### Port Interfaces
```go
// ✅ CORRECT: Ports defined in application layer or shared ports package
package ports

type MessagingPort interface {
    SendEmail(ctx context.Context, email Email) error
    SendEvent(ctx context.Context, event Event) error
}
```

**Rules:**
- ✅ Define in application layer or shared `/ports/` package
- ✅ Application layer owns and uses ports
- ❌ Domain layer should NOT use ports directly
- ✅ Domain constructs events, Application sends them

## Saga Pattern Implementation

### Saga Entity
```go
// ✅ CORRECT: Saga as domain entity with state management
type PlaceOrderSaga struct {
    ID             SagaID
    OrderID        OrderID
    Status         SagaStatus
    PaymentTxnID   string
    FailureReason  string
}

func (s *PlaceOrderSaga) Start(ctx context.Context) (interface{}, error) {
    s.Status = SagaStatusPendingPayment
    return ProcessPaymentCommand{SagaID: s.ID, OrderID: s.OrderID}, nil
}

func (s *PlaceOrderSaga) HandlePaymentProcessed(ctx context.Context, event PaymentProcessedEvent) (interface{}, error) {
    s.Status = SagaStatusPendingInventory
    s.PaymentTxnID = event.TransactionID
    return ReserveInventoryCommand{SagaID: s.ID, OrderID: s.OrderID}, nil
}
```

### Saga Application Service
```go
// ✅ CORRECT: Application service orchestrates saga
type SagaEventHandler struct {
    repo       PlaceOrderSagaRepository
    commandBus CommandBus
    eventBus   EventBus
}

func (h *SagaEventHandler) HandlePaymentResult(ctx context.Context, event PaymentProcessedEvent) error {
    saga, err := h.repo.FindByID(ctx, event.SagaID)
    if err != nil {
        return err
    }
    
    nextCommand, err := saga.HandlePaymentProcessed(ctx, event)
    if err != nil {
        return err
    }
    
    // Save state and dispatch next command
    return h.dispatchCommandAndSave(ctx, saga, nextCommand)
}
```

**Rules:**
- ✅ Use Saga pattern for multi-step processes with state
- ✅ Saga entity manages state transitions
- ✅ Application service handles infrastructure concerns
- ✅ Infrastructure layer listens to events and calls application handlers

## Exception Rules

### Exception 1: Reusable Business Processes
```go
// ✅ When business process is reusable and has business language
func (s *EmailDomainService) RenderAndValidateEmail(template Template, context RenderContext) (*Email, error) {
    // This process is used across multiple use cases
    // and "render and validate email" is business terminology
}
```

### Exception 2: Multi-Step State Management
```go
// ✅ When process needs to maintain state across steps, use Saga
type EmailCampaignSaga struct {
    ID       string
    Status   CampaignStatus
    Progress CampaignProgress
}
```

## Testing Strategy
- **Domain Layer**: Pure unit tests
- **Application Layer**: Integration tests
- **Saga Entities**: Unit tests for state transitions
- **Saga Handlers**: Integration tests for full workflows

## Code Generation Prompts

When generating code, follow these patterns:
1. **For business logic**: Place in domain entities/value objects first
2. **For workflows**: Start with application service
3. **For cross-entity logic**: Consider domain service
4. **For external I/O**: Use ports defined in application layer
5. **For data access**: Use repository interfaces from domain layer
6. **For complex multi-step processes**: Consider Saga pattern