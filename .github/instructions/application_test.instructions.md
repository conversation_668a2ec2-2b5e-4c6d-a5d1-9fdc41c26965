---
applyTo: "internal/**/application/*_test.go"
---

# Guidelines for Writing Application Layer Tests


The test cases in the application layer should be a integration tests for the use cases, ensuring that the application logic interacts correctly with the ports and adapters. The tests should focus on the orchestration of business processes and the correctness of the application logic.

## Rules

- **Use fx to populate dependencies**: Use `fx` to set up the application context and inject dependencies. This allows you to test the application logic in isolation from the infrastructure.
  Example:
  ```go
    var app *versionapplication.ApplicationService
	err := internal.NewApplication(true, fx.Populate(&app)).Start(context.Background())
	require.NoError(t, err)

    app.DoBiz(ctx, &versionapplication.DoBizArgs{})
    ```

- **Use fx.Replace to inject mocking ports if need**: If your application logic depends on external ports, use `fx.Replace` to substitute them with mocks during testing.
    Example:
    ```go
    fx.Replace(fx.Annotate(mockAgent, fx.As(new(bizscenarioapplication.RevisionPort)))),
    ```