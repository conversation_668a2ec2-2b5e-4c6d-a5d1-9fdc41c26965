---
applyTo: "**/*.go"
---


# Guidelines for Go 

## Rules
- **Store the task summary (If have) into the .spec directory **: 
    - Example: `.spec/version/task_summary.md`

- **Go build output must store into the tmp/ directory **: 

- **Use _test.go file in the same package to test instead of create a main file to test**: 

- **Use the `github.com/samber/lo` package to do collection manipulations:**
  - Example:
    ```go
    item,ok:=lo.Find(collection, func(item ItemType) bool {
        return item.ID == targetID
    })
    if !ok {
        return fmt.Errorf("item with ID %s not found", targetID)
    }
    ```

- **Use `github.com/samber/lo.ToPtr` to transform unaddressable values into pointers:**
  - Example:
    ```go
    ptr := lo.ToPtr(42)
  
    func returnValue() int {
        return 42
    }
    ptr := lo.ToPtr(returnValue())
    ```

- **Use `xerrors` for error handling:**
  - Use `xerrors.Wrap` to wrap errors with additional context.
  - Example:
    ```go
    if err != nil { 
        return xerrors.Wrap(err, "failed to process item", xerrors.Field("itemID", item.ID))
    }
    ```
    
- **Use `github.com/AfterShip/library-heytea-go-common/utils/uuid.GenerateUUIDV4()` to generate unique IDs:**
  - Example:
    ```go
    import "github.com/AfterShip/library-heytea-go-common/utils/uuid"
    // Generate a unique ID
    id := uuid.GenerateUUIDV4()
    ```

- **Use mockery for generating mocks:**
  - Use `//go:generate mockery --name InterfaceName --with-expecter --inpackage --structname MockInterfaceName --filename interface_name.mock.gen.go` to generate mocks.
  - Example:
    ```go
    //go:generate mockery --name RevisionPort --with-expecter --inpackage --structname MockRevisionPort --filename revision_port.mock.gen.go
    type RevisionPort interface {
        CreateScenario(ctx context.Context, args CreateScenarioPortArgs) (VersionManagedBusinessScenario, error)
    }
    ```

    