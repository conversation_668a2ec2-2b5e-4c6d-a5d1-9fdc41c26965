---
applyTo: "internal/**/*.go"
---


# Guidelines for how to use uber.go/fx to do dependency injection in Go projects


## Rules

- ** The fx must use in the fx_module.go file:**
  - All fx-related code should be placed in a dedicated `fx_module.go` file.
  - This file should contain the `fx.Module` definition and any necessary constructor functions.

- **Use fx.Module for Dependency Injection:**
  - Define your dependencies in a module using `fx.Module`.
  - Example:
    ```go
    fx.Module("my_module",
      fx.Provide(NewMyService),
      fx.Invoke(MyHandler),
    )
    ```
    
- **Use fx.Provide for Constructor Functions:**
  - Use `fx.Provide` to register constructor functions that create instances of your services.
  - Example:    
    ```go
    func NewMyService() *MyService {
      return &MyService{}
    }
    ```
