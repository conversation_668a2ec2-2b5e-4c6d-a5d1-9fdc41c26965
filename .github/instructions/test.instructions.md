---
applyTo: "**/*_test.go"
---

# Guidelines for Writing test cases for Golang


## Rules

- **The test package must be named `*_test`**: This is a Go convention for test files, ensuring they are recognized by the Go testing framework.

- **Use require.XXX method to do assert**: Use `require` methods from the `testify` package to perform assertions in your tests. This ensures that if an assertion fails, the test will stop immediately, providing clearer feedback on what went wrong.
  Example:
  ```go
  require.NoError(t, err)
  require.Equal(t, expectedValue, actualValue)
  ```

- **Use go test -timeout=300s to run test with timeout**: 
 Example:
 ```
 go test -timeout=300s ./...
 ```