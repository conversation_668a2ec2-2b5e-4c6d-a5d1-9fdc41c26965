---
applyTo: "**/*.go"
---

## Project Layout

This project follows a DDD-based modular monolith structure with clear layer separation:

```
pltf-nf-infra/
├── cmd/                           # Application entry points
│   └── apiserver/
│       └── main.go               # HTTP server bootstrap
├── internal/                      # Private application code  
│   ├── fx_app.go                 # Dependency injection wiring
│   ├── adapters/                 # Infrastructure Layer
│   ├── bizscenarios/             # Business scenarios exension bounded context
│   ├── ports/                    # Shared port interfaces
│   ├── userinterface/            # User Interface Layer
│   │   └── apiserver/            # HTTP API implementation
│   │       ├── handlers/         # HTTP handlers
│   │       ├── conf/             # Configuration files
│   │       └── apierrs/          # API error handling
│   └── version/                  # Version bounded context (DDD module)
│       └── adapters/             # Adapters for version context
├── pkg/                         # Public shared libraries
├── docs/                        # Documentation
│   ├── spanner/                 # Database documentation
│   └── pubsub/                  # Messaging documentation
├── scripts/                     # Build and deployment scripts
├── test/                        # Test utilities
└── tools/                       # Development tools
```

### Key Architectural Decisions

#### 1. Bounded Context Organization
Each business domain (like `version/`) is organized as a self-contained module with:
- **Domain Layer**: Entities, value objects, domain services, repository interfaces
- **Application Layer**: Use cases, application services, command/query handlers
- **Infrastructure Adapters**: Database, messaging, external service implementations

#### 2. Dependency Direction
```
User Interface → Application → Domain ← Infrastructure
```
- **Domain** has no dependencies on other layers
- **Application** depends only on Domain and Port interfaces
- **Infrastructure** implements ports and depends on Application/Domain
- **User Interface** orchestrates Application services

#### 3. Shared Components
- **`internal/adapters/`**: Reusable infrastructure components (logging, auth, persistence)
- **`internal/ports/`**: Shared port interfaces used across contexts
- **`pkg/`**: Public utilities that can be imported by external services


### Migration Guidelines

When adding new bounded contexts, follow the `version/` module structure:
1. Create context directory under `internal/`
2. Implement Domain layer first (entities, repositories)
3. Add Application services for use cases
4. Create Infrastructure adapters in `adapters/`
5. Wire dependencies with fx modules