---
applyTo: "pkg/sdk/**/*.go"
---

# Guidelines for how to write a Go SDK

## Rules

- **Use config to get base url:**
  - Use the `config` package to retrieve the base URL for API requests.
  - Example:
    ```go
    baseURL := config.GetBaseURL()
    ```

- **Use apiClient for API Requests:**
  - Use the `apiClient` package to make API requests.
  - Use request.LoadParamsFrom(args) to load parameters from the provided arguments.
  - The args struct fields shoule be tagged
    1. query parameters with `query:"field_name"`
    2. body parameters with `body:"field_name"`
    3. path parameters with `path:"field_name"`
    4. header parameters with `header:"field_name"`
  - Example:
    ```go   
    type ListWorkingRevisionsArgs struct {
        HostProductCode string `body:"host_product_code" validate:"required"`
        Identifier      revisiondomain.ExtensionIdentifier `body:"identifier" validate:"required"`
        APIVersion      string `header:"api-version" validate:"required"`
    }
    request := client.APIRequest{}
    response := client.ListWorkingRevisionsResponse{}
    if err := request.LoadParamsFrom(args); err != nil {
      return response, handleErr(err, args)
    }
    request.Path="/api/v1/revisions/working"
    err := c.apiClient.Get(ctx, request, &response)
    if err != nil {
        return response, handleErr(err, args)
    }
    ```

