name: CI Actions  # don't edit while the badge was depend on this

on:
  push:
    branches:
      - "master"
  pull_request:
    branches:
      - "master"
      - "feature/**"
      - "hotfix/**"
      - "release/**"
jobs:
  lint-build-test:
    name: Lint/Test
    env:
      GOPRIVATE: "github.com/AfterShip"
      SPANNER_EMULATOR_HOST: localhost:9010
      IS_GITHUB_ACTION: true
    runs-on: ubuntu-latest

    services:
      emulator:
        image: gcr.io/cloud-spanner-emulator/emulator:latest
        ports:
          - 9010:9010
          - 9020:9020

    steps:
      - name: Configure git for private modules
        env:
          TOKEN: ${{ secrets.RESTRICTED_REPO_TOKEN }}
        run: git config --global url."https://${TOKEN}:<EMAIL>/".insteadOf "https://github.com/"

      - name: Install Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23.x

      - name: Checkout Code Base
        uses: actions/checkout@v4

      - name: GolangCI Lint
        uses: golangci/golangci-lint-action@v8
        with:
          version: v2.1
          args: --timeout=10m
          skip-pkg-cache: true
          skip-build-cache: true

      - name: Create Spanner instance
        uses: google-github-actions/setup-gcloud@v2
      - run: gcloud info
      - run: make gcloud_setup
      - run: make init_db

      - name: Install golang-migrate
        run: |
          curl -L https://github.com/golang-migrate/migrate/releases/download/v4.18.2/migrate.linux-amd64.tar.gz | tar xvz
          sudo mv migrate /usr/bin/migrate
          which migrate

      - name: Run migrations
        run: make migrate_up

      - name: Run tests
        run: make test

      - name: SonarQube Scan On PR
        if: ${{ github.event_name == 'pull_request' }}
        uses: sonarsource/sonarqube-scan-action@master
        with:
          projectBaseDir: .
          args: >
            -Dsonar.go.coverage.reportPaths=coverage.out
            -Dsonar.exclusions=**/*_test.go,**/*.gen.go,**/*.py,**/mock/*,**/*_mock.go,**/*_def.go
            -Dsonar.projectKey=pltf-nf-flow
            -Dsonar.pullrequest.provider=github
            -Dsonar.pullrequest.github.repository=${{ github.repository }}
            -Dsonar.pullrequest.key=${{ github.event.number }}
            -Dsonar.pullrequest.branch=${{ github.head_ref }} 
            -Dsonar.pullrequest.base=${{ github.base_ref }}
            -Dsonar.qualitygate.wait=true
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}