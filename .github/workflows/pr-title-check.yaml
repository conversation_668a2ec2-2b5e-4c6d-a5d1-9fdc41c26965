name: 'PR title check'

on:
  pull_request:
    branches:
      - feature/*
      - testing/*
      - release/*
      - staging
      - master
    types: [edited, opened, synchronize, reopened]

jobs:
  title-check:
    runs-on: ubuntu-latest
    steps:
      - name: PR title check
        run: |
          echo "${{github.event.pull_request.title}}" | egrep -i "((^[A-Z]+-[0-9]+)( [A-Z]+-[0-9]+)*|hotfix|release|post-release): "