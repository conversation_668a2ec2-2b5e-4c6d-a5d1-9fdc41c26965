#!/bin/sh
set -x -e

#下载go mod 依赖
#go mod download

cd `dirname $0`

# please install go-swagger first.
# https://goswagger.io/
# brew tap go-swagger/go-swagger
# brew install go-swagger
# brew upgrade go-swagger

# turn off go mod,otherwise swagger tool can't work
#export GO111MODULE=off

swagger generate spec -m  -w ../cmd/apiserver -o ../docs/swagger/conversions.yaml

# turn on go mod finally
#export GO111MODULE=on

# run swagger ui
# swagger serve -F=swagger ./docs/swagger/*.yaml
