# -*- coding:utf-8 -*-
import os


def run():
    for roots, _, files in os.walk(os.getcwd()):
        if "vendor" in roots:
            continue
        for fl in files:
            ext = os.path.splitext(fl)
            if ext.__len__() > 1:
                if ext[1] != ".go":
                    continue
            print("go file caught: root {root}, file {file}".format(root=roots, file=fl))
            remove_empty_line(roots + "/" + fl)


def remove_empty_line(fl: str):
    import_section_start = False
    ori = open(fl, mode="r")
    all_lines = ori.readlines()
    ori.close()
    fh = open(fl, mode="w")
    try:
        for line in all_lines:
            if "import (" in line:
                print("import section caught, line: {line}".format(line=line))
                import_section_start = True
                write_line(fh, line)
                continue
            if import_section_start:
                tmp = line.strip()
                if tmp.__len__() == 0:
                    print("empty line caught")
                    continue
                if ")" in line:
                    import_section_start = False
                write_line(fh, line)
            else:
                write_line(fh, line)
    except Exception as e:
        print(e)
    finally:
        fh.close()


def write_line(fh, line: str):
    lines = [line]
    if not line.endswith("\n"):
        lines.append("\n")
    fh.writelines(lines)


if __name__ == "__main__":
    run()
