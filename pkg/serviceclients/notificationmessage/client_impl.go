package notificationmessage

import (
	"context"

	"github.com/AfterShip/library-heytea-go-common/http/client"
	"github.com/AfterShip/library-heytea-go-common/validator"
	"github.com/AfterShip/library-heytea-go-common/xerrors"
)

const (
	requestPathRenderAndSendEmail = "api/v1/private/email/render-and-send"
)

type clientImpl struct {
	apiClient client.AutomizelyAPIClient
}

type ClientParams struct {
	PlatformNotificationMessageHost string `json:"platform_notification_message_host"`
}

func NewClient(params ClientParams) Client {
	return &clientImpl{
		apiClient: client.NewAutomizelyAPIClient(client.APIClientConfig{
			ServiceURI:         params.PlatformNotificationMessageHost,
			BeforeRequestHooks: []client.BeforeRequestHook{client.AMAPIKeyHook},
		}),
	}
}

func (c *clientImpl) RenderAndSendEmail(ctx context.Context, args SendEmailArgs) error {
	if err := validator.V().Struct(args); err != nil {
		return xerrors.Wrap(err, xerrors.Field("args", args))
	}

	request := client.APIRequest{}
	if err := request.LoadParamsFrom(args); err != nil {
		return xerrors.Wrap(err, xerrors.Field("args", args))
	}
	request.Path = requestPathRenderAndSendEmail

	if err := c.apiClient.Post(ctx, request, nil); err != nil {
		return xerrors.Wrap(err, xerrors.Field("args", args))
	}
	return nil
}
