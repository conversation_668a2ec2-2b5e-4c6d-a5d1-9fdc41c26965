package notificationmessage

import "context"

type SendEmailArgs struct {
	OrganizationID  string         `json:"organization_id"   header:"am-organization-id"`
	HostProductCode string         `json:"host_product_code" header:"am-host-product-code"`
	AppPlatform     string         `json:"app_platform"      header:"am-app-platform"`
	AppKey          string         `json:"app_key"           header:"am-app-key"`
	Unique<PERSON>ey       string         `json:"unique_key"        header:"am-idempotency-key"`
	TemplateID      string         `json:"template_group_id"                               body:"template_group_id"`
	LanguageTag     *string        `json:"language_tag"                                    body:"language_tag"`
	Data            map[string]any `json:"data"                                            body:"data"`
	Recipient       Recipient      `json:"recipient"                                       body:"recipient"`
}

type Recipient struct {
	ID    string `json:"id"    body:"id"`
	Name  string `json:"name"  body:"name"`
	Email string `json:"email" body:"email"`
	Type  string `json:"type"  body:"type"`
}

type Client interface {
	RenderAndSendEmail(ctx context.Context, args SendEmailArgs) error
}
