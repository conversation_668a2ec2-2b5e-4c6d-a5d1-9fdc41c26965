all: start_db init_db

.PHONY: gcloud_setup start_db init_db migrate_up test teardown_db start_pubsub stop_pubsub

gcloud_setup:
	gcloud config configurations create emulator
	gcloud config set auth/disable_credentials true
	gcloud config set project heytea-dev
	gcloud config set api_endpoint_overrides/spanner http://localhost:9020/

start_db:
	gcloud emulators spanner start &

init_db:
	gcloud config configurations activate emulator
	gcloud spanner instances create dev \
       --config=emulator-config --description="Local dev instance" --nodes=1
	gcloud spanner databases create cv-d-core --instance=dev
	./tools/exec_ddl.sh
	export SPANNER_EMULATOR_HOST=localhost:9010

migrate_up: export SPANNER_EMULATOR_HOST=localhost:9010

test:
	go test -v -cover ./... -v -covermode=count -coverprofile=coverage.out -count=1

teardown_db:
	docker ps --filter "expose=9020" --format "{{ .Names }}" | xargs -L 1 docker stop

start_pubsub: stop_pubsub
	@gcloud beta emulators pubsub start --project=heytea-dev &

stop_pubsub:
	pkill -f cloud-pubsub-emulator || true
