package main

import (
	"context"

	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-nf-message/internal"
)

func main() {
	var rootCtx = context.Background()
	// global panic recover
	defer func() {
		if err := recover(); err != nil {
			log.GlobalLogger().
				ErrorCtx(rootCtx, "global recover in main function", zap.Any("err", err), zap.Stack("stacktrace"))
		}
		_ = log.GlobalLogger().Sync() // error is ignored
	}()

	app := internal.NewApplication(false)
	app.Run()
}
