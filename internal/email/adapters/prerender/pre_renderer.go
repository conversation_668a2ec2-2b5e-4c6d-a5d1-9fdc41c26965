package prerender

import (
	"context"
	"encoding/json"

	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/library-heytea-go-common/http/client"
	"github.com/AfterShip/library-heytea-go-common/xerrors"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/template"
)

var preRenderServiceHosts = map[string]string{
	"local":        "http://prod-mt-email-renderer.as-in.io/v1",
	"testing-kiwi": "http://prod-mt-email-renderer.as-in.io/v1",
	"testing-nike": "http://prod-mt-email-renderer.as-in.io/v1",
	"testing-pear": "http://prod-mt-email-renderer.as-in.io/v1",
	"development":  "http://prod-mt-email-renderer.as-in.io/v1",
	"staging":      "http://staging-prod-mt-email-renderer.as-in.com/v1",
	"production":   "http://prod-mt-email-renderer.as-in.com/v1",
}

type preRenderer struct {
	apiClient client.AutomizelyAPIClient
}

func NewPreRenderer() template.PreRenderPort {
	return &preRenderer{
		apiClient: client.NewAutomizelyAPIClient(client.APIClientConfig{
			ServiceURI:         preRenderServiceHosts[cfg.GetCurrentStage()],
			BeforeRequestHooks: []client.BeforeRequestHook{client.AMAPIKeyHook},
		}),
	}
}

type renderedEmail struct {
	HTML string `json:"html"`
}

type RenderEmailBody struct {
	EmailTemplate    json.RawMessage `body:"email_template"`
	ContentExtension struct {
		AppleWallet bool `body:"apple_wallet"`
		PayPal      bool `body:"paypal"`
	} `body:"content_extension"`
	Options RenderOptions `body:"options"`
}

type RenderEmailWithSectionsArgs struct {
	EmailTemplateWithDeclaredSections
	// TODO(Samuel): check the logic
	ContentExtensionConfig map[string]bool
	Options                RenderOptions
}

type RenderOptions struct {
	IsPreview bool `body:"is_preview"`
}

func (p *preRenderer) PreRender(ctx context.Context, args template.PreRenderParams) (string, error) {
	var request client.APIRequest
	var requestBody RenderEmailBody

	templateWithSections := EmailTemplateWithDeclaredSections{
		EmailTemplate: args.EmailTemplate,
		Sections:      deriveDeclaredSectionsInEmailTemplate(args.EmailTemplate, args.EmailSectionDeclarations),
	}
	r := RenderEmailWithSectionsArgs{
		EmailTemplateWithDeclaredSections: templateWithSections,
	}
	//nolint:musttag // Legacy API.
	raw, err := json.Marshal(r)
	if err != nil {
		return "", xerrors.Wrap(err, xerrors.Field("args", args))
	}

	requestBody.EmailTemplate = raw
	requestBody.Options = RenderOptions{
		IsPreview: false,
	}

	if err = request.LoadParamsFrom(requestBody); err != nil {
		return "", xerrors.Wrap(err, xerrors.Field("args", args))
	}

	request.Path = `/platform-email-render`

	var rendered renderedEmail
	if err := p.apiClient.Post(ctx, request, &rendered); err != nil {
		return "", xerrors.Wrap(err, xerrors.Field("args", args))
	}
	return rendered.HTML, nil
}

type EmailTemplateWithDeclaredSections struct {
	template.EmailTemplate `                   body:",promote"`
	Sections               []DeclaredSections `body:"sections" json:"sections"`
}

type DeclaredSections struct {
	// Type a content section.
	// It is used to identify a content section by the user.
	// Usually it should be snaked, lowercase, and length not exceed 128
	// example. image_and_text, product_recommendation, etc.
	Type string `json:"type"`

	// SectionTemplateSetting is the setting of a content section.
	// The user can customize the style and logic of the block.
	SectionTemplateSetting json.RawMessage `json:"section_template_setting"`
}

func deriveDeclaredSectionsInEmailTemplate(
	emailTemplate template.EmailTemplate,
	emailSectionDeclarations []sectiondeclaration.EmailSectionDeclaration,
) []DeclaredSections {
	if len(emailTemplate.Sections) == 0 {
		return []DeclaredSections{}
	}
	declaredSectionMap := make(map[string]DeclaredSections, len(emailSectionDeclarations))
	for _, declaration := range emailSectionDeclarations {
		declaredSectionMap[declaration.Type] = DeclaredSections{
			Type:                   declaration.Type,
			SectionTemplateSetting: declaration.SectionTemplateSetting,
		}
	}

	var declaredSectionsInTemplate []DeclaredSections
	for _, section := range emailTemplate.Sections {
		if declaredSection, ok := declaredSectionMap[section.Type]; ok {
			declaredSectionsInTemplate = append(declaredSectionsInTemplate, declaredSection)
		}
	}

	return declaredSectionsInTemplate
}
