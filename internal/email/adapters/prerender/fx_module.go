package prerender

import (
	"go.uber.org/fx"

	"github.com/AfterShip/pltf-nf-message/internal/email/domain/template"
)

type Params struct {
	fx.In
}

type Result struct {
	fx.Out

	PreRenderer template.PreRenderPort
}

func New(params Params) Result {
	return Result{
		PreRenderer: NewPreRenderer(),
	}
}

const moduleName = "pre_renderer_adapter"

var Module = fx.Module(moduleName,
	fx.Provide(New))
