package spanner

import (
	"go.uber.org/fx"

	"github.com/AfterShip/pltf-nf-message/internal/email/adapters/persistence/spanner/emailmessage"
	"github.com/AfterShip/pltf-nf-message/internal/email/adapters/persistence/spanner/recipientprofile"
	"github.com/AfterShip/pltf-nf-message/internal/email/adapters/persistence/spanner/subscriptioncompliancerule"
)

const ModuleName = "email_spanner"

var Module = fx.Module(ModuleName,
	recipientprofile.Module,
	subscriptioncompliancerule.Module,
	emailmessage.Module,
)
