package recipientprofile

import (
	"go.uber.org/fx"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
)

type Params struct {
	fx.In

	Spanner spannerx.Client
}

type Result struct {
	fx.Out

	RecipientProfileRepository recipientprofile.RecipientProfileRepository
}

func New(p Params) Result {
	return Result{
		RecipientProfileRepository: NewRepositoryImpl(p.Spanner),
	}
}

const moduleName = "recipient_profile_repository"

var Module = fx.Module(moduleName,
	fx.Provide(New),
)
