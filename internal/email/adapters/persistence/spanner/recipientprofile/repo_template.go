package recipientprofile

import (
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx/repositorytpl"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
)

type RecipientProfileQuery struct {
	Email          string `json:"email"           sql:"column=email,op=eq"`
	OrganizationID string `json:"organization_id" sql:"column=organization_id,op=eq"`
	AppPlatform    string `json:"app_platform"    sql:"column=app_platform,op=eq"`
	AppKey         string `json:"app_key"         sql:"column=app_key,op=eq"`
}

type repositoryTemplate repositorytpl.Repository[recipientprofile.RecipientProfile, RecipientProfileQuery]

func newRepoTemplate(cli spannerx.Client) repositoryTemplate {
	return repositorytpl.New[RecipientProfile, recipientprofile.RecipientProfile, RecipientProfileQuery](
		repositorytpl.Params[RecipientProfile, recipientprofile.RecipientProfile]{
			Spanner:    cli,
			SoftDelete: false,
			Converter:  &ConvertImpl{},
		},
	)
}
