// Code generated by https://github.com/AfterShip/go-sql-relay; DO NOT EDIT.
package recipientprofile

import (
	"time"

	"cloud.google.com/go/spanner"
)

type RecipientProfile struct {
	RecipientProfileID string             `spanner:"recipient_profile_id" json:"id"`
	OrganizationID     string             `spanner:"organization_id" json:"organization_id"`
	AppPlatform        string             `spanner:"app_platform" json:"app_platform"`
	AppKey             string             `spanner:"app_key" json:"app_key"`
	Email              string             `spanner:"email" json:"email"`
	EmailHash          string             `spanner:"email_hash" json:"email_hash"`
	Subscription       spanner.NullString `spanner:"subscription" json:"subscription"`
	Suppressions       spanner.NullString `spanner:"suppressions" json:"suppressions"`
	CreatedAt          time.Time          `spanner:"created_at" json:"created_at"`
	UpdatedAt          spanner.NullTime   `spanner:"updated_at" json:"updated_at"`
}
