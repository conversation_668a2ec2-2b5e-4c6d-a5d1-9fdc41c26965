package recipientprofile

import (
	"encoding/json"

	"cloud.google.com/go/spanner"
	"github.com/samber/lo"

	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
)

type ConvertImpl struct{}

func (c *ConvertImpl) ConvertToDBModel(source recipientprofile.RecipientProfile) (RecipientProfile, error) {
	res := RecipientProfile{
		RecipientProfileID: source.ID,
		OrganizationID:     source.OrganizationID,
		AppPlatform:        source.AppPlatform,
		AppKey:             source.AppKey,
		Email:              source.Email,
		CreatedAt:          source.CreatedAt,
	}
	if source.UpdatedAt != nil {
		res.UpdatedAt = spanner.NullTime{Time: *source.UpdatedAt, Valid: true}
	}
	subscription, err := convertSubscriptionToNullString(source.Subscription)
	if err != nil {
		return RecipientProfile{}, err
	}
	res.Subscription = subscription
	if len(source.Suppressions) > 0 {
		suppressions, err := convertSuppressionsToNullString(source.Suppressions)
		if err != nil {
			return RecipientProfile{}, err
		}
		res.Suppressions = suppressions
	}
	return res, nil
}

func (c *ConvertImpl) ConvertToDomainModel(source RecipientProfile) (recipientprofile.RecipientProfile, error) {
	res := recipientprofile.RecipientProfile{
		ID:             source.RecipientProfileID,
		OrganizationID: source.OrganizationID,
		AppPlatform:    source.AppPlatform,
		AppKey:         source.AppKey,
		Email:          source.Email,
		CreatedAt:      source.CreatedAt,
		UpdatedAt:      lo.ToPtr(source.UpdatedAt.Time),
	}
	subscription, err := convertNullStringToSubscription(source.Subscription)
	if err != nil {
		return recipientprofile.RecipientProfile{}, err
	}
	res.Subscription = subscription
	suppressions, err := convertNullStringToSuppressions(source.Suppressions)
	if err != nil {
		return recipientprofile.RecipientProfile{}, err
	}
	res.Suppressions = suppressions

	return res, nil
}

func convertSubscriptionToNullString(source recipientprofile.Subscription) (spanner.NullString, error) {
	subscriptionBytes, err := json.Marshal(source)
	if err != nil {
		return spanner.NullString{}, err
	}
	return spanner.NullString{
		StringVal: string(subscriptionBytes),
		Valid:     true,
	}, nil
}

func convertNullStringToSubscription(source spanner.NullString) (recipientprofile.Subscription, error) {
	if !source.Valid {
		return recipientprofile.Subscription{}, nil
	}
	var subscription recipientprofile.Subscription
	err := json.Unmarshal([]byte(source.StringVal), &subscription)
	if err != nil {
		return recipientprofile.Subscription{}, err
	}
	return subscription, nil
}

func convertSuppressionsToNullString(source []recipientprofile.Suppression) (spanner.NullString, error) {
	if len(source) == 0 {
		return spanner.NullString{}, nil
	}
	suppressionsBytes, err := json.Marshal(source)
	if err != nil {
		return spanner.NullString{}, err
	}
	return spanner.NullString{
		StringVal: string(suppressionsBytes),
		Valid:     true,
	}, nil
}

func convertNullStringToSuppressions(source spanner.NullString) ([]recipientprofile.Suppression, error) {
	if !source.Valid {
		return []recipientprofile.Suppression{}, nil
	}
	var suppressions []recipientprofile.Suppression
	err := json.Unmarshal([]byte(source.StringVal), &suppressions)
	if err != nil {
		return []recipientprofile.Suppression{}, err
	}
	return suppressions, nil
}
