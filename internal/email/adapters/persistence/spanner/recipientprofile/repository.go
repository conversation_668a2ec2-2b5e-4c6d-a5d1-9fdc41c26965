package recipientprofile

import (
	"context"
	"time"

	"github.com/samber/lo"

	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/library-heytea-go-common/xerrors"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
)

type repositoryImpl struct {
	repositoryTemplate
}

func NewRepositoryImpl(cli spannerx.Client) recipientprofile.RecipientProfileRepository {
	return &repositoryImpl{
		repositoryTemplate: newRepoTemplate(cli),
	}
}

func (r *repositoryImpl) RunInTransaction(ctx context.Context, fn func(ctx context.Context) error) error {
	return r.repositoryTemplate.RunInTransaction(ctx, fn)
}

func (r *repositoryImpl) NextID(ctx context.Context) string {
	return uuid.GenerateUUIDV4()
}

func (r *repositoryImpl) Find(
	ctx context.Context,
	params recipientprofile.FindParams,
) (recipientprofile.RecipientProfile, error) {
	res, err := r.repositoryTemplate.Find(ctx, RecipientProfileQuery{
		Email:          params.Email,
		OrganizationID: params.OrganizationID,
		AppPlatform:    params.AppPlatform,
		AppKey:         params.AppKey,
	})
	if err != nil {
		return recipientprofile.RecipientProfile{}, err
	}
	if len(res) == 0 {
		return recipientprofile.RecipientProfile{}, xerrors.ErrBusinessRecordNotFound
	}
	return res[0], nil
}

func (r *repositoryImpl) Create(ctx context.Context, profile *recipientprofile.RecipientProfile) error {
	profile.CreatedAt = time.Now()
	_, err := r.repositoryTemplate.Create(ctx, *profile)
	return err
}

func (r *repositoryImpl) Update(ctx context.Context, profile *recipientprofile.RecipientProfile) error {
	profile.UpdatedAt = lo.ToPtr(time.Now())
	_, err := r.repositoryTemplate.Update(ctx, *profile)
	return err
}
