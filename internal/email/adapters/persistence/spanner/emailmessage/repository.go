package emailmessage

import (
	"context"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/message"
)

type repositoryImpl struct {
	repositoryTemplate
}

func NewRepositoryImpl(cli spannerx.Client) message.EmailMessageRepository {
	return &repositoryImpl{
		repositoryTemplate: newRepoTemplate(cli),
	}
}

func (r *repositoryImpl) Create(ctx context.Context, message message.EmailMessage) error {
	_, err := r.repositoryTemplate.Create(ctx, message)
	return err
}

func (r *repositoryImpl) FindByID(ctx context.Context, id string) (message.EmailMessage, error) {
	return r.repositoryTemplate.FindByID(ctx, id)
}
