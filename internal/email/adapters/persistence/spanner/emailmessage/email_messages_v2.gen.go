// Code generated by https://github.com/AfterShip/go-sql-relay; DO NOT EDIT.
package emailmessage

import (
	"time"

	"cloud.google.com/go/spanner"
)

type EmailMessage struct {
	EmailMessageID  string             `spanner:"email_message_id" json:"email_message_id"`
	OrganizationID  string             `spanner:"organization_id" json:"organization_id"`
	HostProductCode string             `spanner:"host_product_code" json:"host_product_code"`
	Sender          string             `spanner:"sender" json:"sender"`
	ReplyTo         string             `spanner:"reply_to" json:"reply_to"`
	SendTo          string             `spanner:"send_to" json:"send_to"`
	UniqueKey       string             `spanner:"unique_key" json:"unique_key"`
	EmailContent    string             `spanner:"email_content" json:"email_content"`
	DataTracking    string             `spanner:"data_tracking" json:"data_tracking"`
	MessageID       spanner.NullString `spanner:"message_id" json:"message_id"`
	Status          string             `spanner:"status" json:"status"`
	CreatedAt       time.Time          `spanner:"created_at" json:"created_at"`
	UpdatedAt       spanner.NullTime   `spanner:"updated_at" json:"updated_at"`
}
