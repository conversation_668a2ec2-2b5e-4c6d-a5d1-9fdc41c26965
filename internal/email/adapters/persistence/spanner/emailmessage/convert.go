package emailmessage

import (
	"encoding/json"

	"cloud.google.com/go/spanner"
	"github.com/samber/lo"

	"github.com/AfterShip/pltf-nf-message/internal/email/domain/message"
)

type ConvertImpl struct{}

func (c *ConvertImpl) ConvertToDBModel(source message.EmailMessage) (EmailMessage, error) {
	res := EmailMessage{
		EmailMessageID:  source.ID,
		OrganizationID:  source.OrganizationID,
		HostProductCode: source.HostProductCode,
		UniqueKey:       source.UniqueKey,
		MessageID: spanner.NullString{
			StringVal: source.MessageID,
			Valid:     source.MessageID != "",
		},
		Status:    source.Status,
		CreatedAt: source.CreatedAt,
	}
	if source.UpdatedAt != nil {
		res.UpdatedAt = spanner.NullTime{Time: *source.UpdatedAt, Valid: true}
	}
	sender, err := convertEmailSenderToString(source.Sender)
	if err != nil {
		return EmailMessage{}, err
	}
	res.Sender = sender
	replyTo, err := convertEmailSenderToString(source.ReplyTo)
	if err != nil {
		return EmailMessage{}, err
	}
	res.ReplyTo = replyTo
	sendTo, err := convertRecipientToString(source.SendTo)
	if err != nil {
		return EmailMessage{}, err
	}
	res.SendTo = sendTo
	emailContent, err := convertEmailContentToString(source.EmailContent)
	if err != nil {
		return EmailMessage{}, err
	}
	res.EmailContent = emailContent
	return res, nil
}

func convertEmailSenderToString(sender message.EmailSender) (string, error) {
	bytes, err := json.Marshal(sender)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

func convertRecipientToString(recipient message.EmailRecipient) (string, error) {
	bytes, err := json.Marshal(recipient)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

func convertEmailContentToString(content message.EmailContent) (string, error) {
	bytes, err := json.Marshal(content)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

func (c *ConvertImpl) ConvertToDomainModel(source EmailMessage) (message.EmailMessage, error) {
	sender, err := convertStringToEmailSender(source.Sender)
	if err != nil {
		return message.EmailMessage{}, err
	}
	replyTo, err := convertStringToEmailSender(source.ReplyTo)
	if err != nil {
		return message.EmailMessage{}, err
	}
	sendTo, err := convertStringToRecipient(source.SendTo)
	if err != nil {
		return message.EmailMessage{}, err
	}
	emailContent, err := convertStringToEmailContent(source.EmailContent)
	if err != nil {
		return message.EmailMessage{}, err
	}
	return message.EmailMessage{
		ID:              source.EmailMessageID,
		OrganizationID:  source.OrganizationID,
		HostProductCode: source.HostProductCode,
		Sender:          sender,
		ReplyTo:         replyTo,
		SendTo:          sendTo,
		EmailContent:    emailContent,
		UniqueKey:       source.UniqueKey,
		MessageID:       source.MessageID.StringVal,
		Status:          source.Status,
		CreatedAt:       source.CreatedAt,
		UpdatedAt:       lo.ToPtr(source.UpdatedAt.Time),
	}, nil
}

func convertStringToEmailSender(source string) (message.EmailSender, error) {
	var sender message.EmailSender
	err := json.Unmarshal([]byte(source), &sender)
	if err != nil {
		return message.EmailSender{}, err
	}
	return sender, nil
}

func convertStringToRecipient(source string) (message.EmailRecipient, error) {
	var recipient message.EmailRecipient
	err := json.Unmarshal([]byte(source), &recipient)
	if err != nil {
		return message.EmailRecipient{}, err
	}
	return recipient, nil
}

func convertStringToEmailContent(source string) (message.EmailContent, error) {
	var content message.EmailContent
	err := json.Unmarshal([]byte(source), &content)
	if err != nil {
		return message.EmailContent{}, err
	}
	return content, nil
}
