package emailmessage

import (
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx/repositorytpl"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/message"
)

type EmailMessageQuery struct {
	UniqueKey string `json:"unique_key" sql:"column=unique_key,op=eq"`
}

type repositoryTemplate repositorytpl.Repository[message.EmailMessage, EmailMessageQuery]

func newRepoTemplate(cli spannerx.Client) repositoryTemplate {
	return repositorytpl.New[EmailMessage, message.EmailMessage, EmailMessageQuery](
		repositorytpl.Params[EmailMessage, message.EmailMessage]{
			Spanner:    cli,
			SoftDelete: false,
			Converter:  &ConvertImpl{},
		},
	)
}
