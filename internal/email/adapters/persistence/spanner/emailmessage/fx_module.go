package emailmessage

import (
	"go.uber.org/fx"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/message"
)

type Params struct {
	fx.In

	Spanner spannerx.Client
}

type Result struct {
	fx.Out

	EmailMessageRepository message.EmailMessageRepository
}

func New(p Params) Result {
	return Result{
		EmailMessageRepository: NewRepositoryImpl(p.Spanner),
	}
}

const moduleName = "email_message_repository"

var Module = fx.Module(moduleName,
	fx.Provide(New),
)
