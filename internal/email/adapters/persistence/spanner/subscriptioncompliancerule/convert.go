package subscriptioncompliancerule

import (
	"encoding/json"
	"errors"

	"cloud.google.com/go/spanner"
	"github.com/samber/lo"

	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
)

var (
	ErrInvalidAllowStatus = errors.New("invalid allow status")
	ErrInvalidDenyStatus  = errors.New("invalid deny status")
)

type ConvertImpl struct{}

func (c *ConvertImpl) ConvertToDBModel(
	source recipientprofile.SubscriptionComplianceRule,
) (SubscriptionComplianceRule, error) {
	res := SubscriptionComplianceRule{
		SubscriptionComplianceRuleID: source.ID,
		HostProductCode:              source.HostProductCode,
		BusinessScenario:             source.BusinessScenario,
		RuleType:                     string(source.RuleType),
		Description: spanner.NullString{
			StringVal: source.Description,
			Valid:     source.Description != "",
		},
		CreatedAt: source.CreatedAt,
	}
	if source.UpdatedAt != nil {
		res.UpdatedAt = spanner.NullTime{Time: *source.UpdatedAt, Valid: true}
	}
	allowStatus, err := convertStatusToNullString(source.AllowStatus)
	if err != nil {
		return SubscriptionComplianceRule{}, err
	}
	res.AllowStatus = allowStatus
	denyStatus, err := convertStatusToNullString(source.DenyStatus)
	if err != nil {
		return SubscriptionComplianceRule{}, err
	}
	res.DenyStatus = denyStatus
	return res, nil
}

func (c *ConvertImpl) ConvertToDomainModel(
	source SubscriptionComplianceRule,
) (recipientprofile.SubscriptionComplianceRule, error) {
	res := recipientprofile.SubscriptionComplianceRule{
		ID:               source.SubscriptionComplianceRuleID,
		HostProductCode:  source.HostProductCode,
		BusinessScenario: source.BusinessScenario,
		RuleType:         recipientprofile.RuleType(source.RuleType),
		Description:      source.Description.StringVal,
		CreatedAt:        source.CreatedAt,
		UpdatedAt:        lo.ToPtr(source.UpdatedAt.Time),
	}
	allowStatus, err := convertNullStringToStatus(source.AllowStatus)
	if err != nil {
		return recipientprofile.SubscriptionComplianceRule{}, err
	}
	res.AllowStatus = allowStatus
	denyStatus, err := convertNullStringToStatus(source.DenyStatus)
	if err != nil {
		return recipientprofile.SubscriptionComplianceRule{}, err
	}
	res.DenyStatus = denyStatus
	return res, nil
}

func convertStatusToNullString(status []recipientprofile.SubscriptionStatus) (spanner.NullString, error) {
	if len(status) == 0 {
		return spanner.NullString{}, nil
	}
	statusBytes, err := json.Marshal(status)
	if err != nil {
		return spanner.NullString{}, err
	}
	return spanner.NullString{
		StringVal: string(statusBytes),
		Valid:     true,
	}, nil
}

func convertNullStringToStatus(source spanner.NullString) ([]recipientprofile.SubscriptionStatus, error) {
	if !source.Valid {
		return []recipientprofile.SubscriptionStatus{}, nil
	}
	var status []recipientprofile.SubscriptionStatus
	err := json.Unmarshal([]byte(source.StringVal), &status)
	if err != nil {
		return []recipientprofile.SubscriptionStatus{}, err
	}
	return status, nil
}
