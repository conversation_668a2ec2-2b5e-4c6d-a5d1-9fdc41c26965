package subscriptioncompliancerule

import (
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx/repositorytpl"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
)

type SubscriptionComplianceRuleQuery struct {
	HostProductCode string `json:"host_product_code" sql:"column=host_product_code,op=eq"`
}

type repositoryTemplate repositorytpl.Repository[recipientprofile.SubscriptionComplianceRule, SubscriptionComplianceRuleQuery]

func newRepoTemplate(cli spannerx.Client) repositoryTemplate {
	return repositorytpl.New[SubscriptionComplianceRule, recipientprofile.SubscriptionComplianceRule, SubscriptionComplianceRuleQuery](
		repositorytpl.Params[SubscriptionComplianceRule, recipientprofile.SubscriptionComplianceRule]{
			Spanner:    cli,
			SoftDelete: false,
			Converter:  &ConvertImpl{},
		},
	)
}
