// Code generated by https://github.com/AfterShip/go-sql-relay; DO NOT EDIT.
package subscriptioncompliancerule

import (
	"time"

	"cloud.google.com/go/spanner"
)

type SubscriptionComplianceRule struct {
	SubscriptionComplianceRuleID string             `spanner:"subscription_compliance_rule_id" json:"id"`
	HostProductCode              string             `spanner:"host_product_code" json:"host_product_code"`
	BusinessScenario             string             `spanner:"business_scenario" json:"business_scenario"`
	RuleType                     string             `spanner:"rule_type" json:"rule_type"`
	AllowStatus                  spanner.NullString `spanner:"allow_status" json:"allow_status"`
	DenyStatus                   spanner.NullString `spanner:"deny_status" json:"deny_status"`
	Description                  spanner.NullString `spanner:"description" json:"description"`
	CreatedAt                    time.Time          `spanner:"created_at" json:"created_at"`
	UpdatedAt                    spanner.NullTime   `spanner:"updated_at" json:"updated_at"`
}
