package subscriptioncompliancerule

import (
	"context"
	"time"

	"github.com/samber/lo"

	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
)

type repositoryImpl struct {
	repositoryTemplate
}

func NewRepositoryImpl(cli spannerx.Client) recipientprofile.SubscriptionComplianceRuleRepository {
	return &repositoryImpl{
		repositoryTemplate: newRepoTemplate(cli),
	}
}

func (r *repositoryImpl) NextID(ctx context.Context) string {
	return uuid.GenerateUUIDV4()
}

func (r *repositoryImpl) FindByHostProductCode(
	ctx context.Context,
	hostProductCode string,
) ([]recipientprofile.SubscriptionComplianceRule, error) {
	return r.repositoryTemplate.Find(ctx, SubscriptionComplianceRuleQuery{
		HostProductCode: hostProductCode,
	})
}

func (r *repositoryImpl) Create(ctx context.Context, rule *recipientprofile.SubscriptionComplianceRule) error {
	rule.CreatedAt = time.Now()
	_, err := r.repositoryTemplate.Create(ctx, *rule)
	return err
}

func (r *repositoryImpl) Update(ctx context.Context, rule *recipientprofile.SubscriptionComplianceRule) error {
	rule.UpdatedAt = lo.ToPtr(time.Now())
	_, err := r.repositoryTemplate.Update(ctx, *rule)
	return err
}
