package subscriptioncompliancerule

import (
	"go.uber.org/fx"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
)

type Params struct {
	fx.In

	Spanner spannerx.Client
}

type Result struct {
	fx.Out

	SubscriptionComplianceRuleRepository recipientprofile.SubscriptionComplianceRuleRepository
}

func New(p Params) Result {
	return Result{
		SubscriptionComplianceRuleRepository: NewRepositoryImpl(p.Spanner),
	}
}

const moduleName = "subscription_compliance_rule_repository"

var Module = fx.Module(moduleName,
	fx.Provide(New),
)
