package adapters

import (
	"go.uber.org/fx"

	"github.com/AfterShip/pltf-nf-infra/pkg/revision"
	"github.com/AfterShip/pltf-nf-infra/pkg/version"
	"github.com/AfterShip/pltf-nf-message/internal/email/adapters/emailsender"
	"github.com/AfterShip/pltf-nf-message/internal/email/adapters/encryptor"
	"github.com/AfterShip/pltf-nf-message/internal/email/adapters/persistence/spanner"
	"github.com/AfterShip/pltf-nf-message/internal/email/adapters/prerender"
	"github.com/AfterShip/pltf-nf-message/internal/email/adapters/sendagent"
	emailapplication "github.com/AfterShip/pltf-nf-message/internal/email/application"
	sectiondeclarationdomain "github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"
	"github.com/AfterShip/pltf-nf-message/internal/ports"
)

var Module = fx.Module("email_adapters",
	sendagent.Module,
	encryptor.Module,
	emailsender.Module,
	prerender.Module,
	spanner.Module,
	fx.Provide(
		NewEmailSectionDeclarationRevisionAgent,
		fx.Annotate(
			NewSectionDeclarationRevisionPortAdapter,
			fx.As(new(emailapplication.SectionDeclarationRevisionPort)),
		),
		NewEmailSectionDeclarationExtensionProvider,
		NewEmailSectionDeclarationVersionAgent,
	),
)

func NewSectionDeclarationRevisionPortAdapterFromAgent(
	revisionAgent revision.RevisionAgent[sectiondeclarationdomain.EmailSectionDeclaration],
) emailapplication.SectionDeclarationRevisionPort {
	return NewSectionDeclarationRevisionPortAdapter(revisionAgent)
}

func NewEmailSectionDeclarationExtensionProviderFromPorts(
	repository sectiondeclarationdomain.EmailSectionDeclarationRepository,
	transactionPort ports.TransactionPort,
) version.ExtensionProvider[sectiondeclarationdomain.EmailSectionDeclarationValue] {
	return NewEmailSectionDeclarationExtensionProvider(repository, transactionPort)
}

func NewEmailSectionDeclarationVersionAgentFromProvider(
	provider version.ExtensionProvider[sectiondeclarationdomain.EmailSectionDeclarationValue],
) version.VersionAgent[sectiondeclarationdomain.EmailSectionDeclarationValue] {
	return version.NewVersionAgent(
		provider,
		sectiondeclarationdomain.EmailSectionExtensionType,
	)
}
