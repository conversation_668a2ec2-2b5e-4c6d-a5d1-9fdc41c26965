package emailsender

import (
	"context"

	"github.com/samber/lo"

	"github.com/AfterShip/library-marketing-go/serviceclients/businessesv2"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/message"
)

type emailSenderGetter struct {
	bizClient businessesv2.Client
}

func NewEmailSenderGetter(bizClient businessesv2.Client) message.OrganizationSenderGetter {
	return &emailSenderGetter{
		bizClient: bizClient,
	}
}

func (getter *emailSenderGetter) GetOrganizationSenders(
	ctx context.Context,
	organizationID string,
) ([]message.EmailSender, error) {
	var page int64 = 1
	var allSenders []businessesv2.Sender
	for {
		senders, err := getter.bizClient.GetSenders(ctx, businessesv2.GetSendersArgs{
			OrganizationId:     organizationID,
			ExpandSenderDomain: true,
			Page:               page,
			Limit:              10, //nolint:mnd // This is a valid check.
		})
		if err != nil {
			return nil, err
		}
		allSenders = append(allSenders, senders.ToSlice()...)
		if !senders.Pagination.HasNextPage {
			break
		}
		page++
	}
	return lo.Map(allSenders, func(item businessesv2.Sender, _ int) message.EmailSender {
		return message.EmailSender{
			ID:             item.Id,
			Email:          item.Email,
			Name:           "",
			DomainVerified: item.SenderDomain.Valid,
		}
	}), nil
}
