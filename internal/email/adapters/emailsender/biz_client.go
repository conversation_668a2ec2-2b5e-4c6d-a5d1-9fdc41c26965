package emailsender

import (
	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/library-marketing-go/serviceclients/businessesv2"
)

var businessesPlatformHost = map[string]string{
	"local":        "http://pltf-account-business.as-in.io/business",
	"development":  "http://pltf-account-business.as-in.io/business",
	"testing-kiwi": "http://pltf-account-business.as-in.io/business",
	"testing-nike": "http://pltf-account-business.as-in.io/business",
	"testing-pear": "http://pltf-account-business.as-in.io/business",
	"staging":      "http://pltf-account-business.as-in.com/business",
	"production":   "http://pltf-account-business.as-in.com/business",
}

func NewBizClient() businessesv2.Client {
	return businessesv2.NewClient(businessesv2.ClientParams{
		BusinessPlatformHostV2: businessesPlatformHost[cfg.GetCurrentStage()],
	})
}
