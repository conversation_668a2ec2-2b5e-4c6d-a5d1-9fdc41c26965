package emailsender

import (
	"go.uber.org/fx"

	"github.com/AfterShip/library-marketing-go/serviceclients/businessesv2"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/message"
)

type Params struct {
	fx.In

	BizClient businessesv2.Client
}

type Result struct {
	fx.Out
	EmailSenderGetter message.OrganizationSenderGetter
}

func New(p Params) Result {
	return Result{
		EmailSenderGetter: NewEmailSenderGetter(p.BizClient),
	}
}

const moduleName = "email_sender_adapter"

var Module = fx.Module(moduleName,
	fx.Provide(NewBizClient),
	fx.Provide(New))
