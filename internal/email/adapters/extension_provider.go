package adapters

import (
	"context"
	"time"

	"github.com/samber/lo"

	"github.com/AfterShip/pltf-nf-infra/pkg/version"
	sectiondeclarationdomain "github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"
	"github.com/AfterShip/pltf-nf-message/internal/ports"
)

// emailSectionDeclarationExtensionProvider implements the version.ExtensionProvider interface
// for email section declarations. It handles the actual persistence operations during version releases.
type emailSectionDeclarationExtensionProvider struct {
	repository      sectiondeclarationdomain.EmailSectionDeclarationRepository
	transactionPort ports.TransactionPort
}

// NewEmailSectionDeclarationExtensionProvider creates a new ExtensionProvider for email section declarations.
func NewEmailSectionDeclarationExtensionProvider(
	repository sectiondeclarationdomain.EmailSectionDeclarationRepository,
	transactionPort ports.TransactionPort,
) version.ExtensionProvider[sectiondeclarationdomain.EmailSectionDeclarationValue] {
	return &emailSectionDeclarationExtensionProvider{
		repository:      repository,
		transactionPort: transactionPort,
	}
}

// RunInTransaction runs the given function in a transaction.
func (p *emailSectionDeclarationExtensionProvider) RunInTransaction(
	ctx context.Context,
	fn func(context.Context) error,
) error {
	return p.transactionPort.RunInTransaction(ctx, fn)
}

// CreateExtensions creates new email section declaration extensions.
func (p *emailSectionDeclarationExtensionProvider) CreateExtensions(
	ctx context.Context,
	extensions []version.ExtensionRevision[sectiondeclarationdomain.EmailSectionDeclarationValue],
) error {
	if len(extensions) == 0 {
		return nil
	}

	// Convert extensions to EmailSectionDeclaration domain objects
	sections := make([]sectiondeclarationdomain.EmailSectionDeclaration, 0, len(extensions))
	now := time.Now()

	for _, ext := range extensions {
		// Validate email section declaration data
		section := sectiondeclarationdomain.EmailSectionDeclaration{
			ID:                     ext.Identifier.ID,
			HostProductCode:        ext.HostProductCode,
			Handle:                 ext.Identifier.Handle,
			Type:                   ext.Value.Type,
			Name:                   ext.Value.Name,
			Description:            ext.Value.Description,
			Category:               ext.Value.Category,
			OrderingWeight:         ext.Value.OrderingWeight,
			IconURL:                ext.Value.IconURL,
			ApplicableSetting:      ext.Value.ApplicableSetting,
			LimitationSetting:      ext.Value.LimitationSetting,
			InputSetting:           ext.Value.InputSetting,
			SectionTemplateSetting: ext.Value.SectionTemplateSetting,
			PresentationSettings:   ext.Value.PresentationSettings,
			Hidden:                 ext.Value.Hidden,
			CreatedAt:              now,
		}

		// Validate before creating
		if err := section.CanBeCreated(); err != nil {
			return err
		}

		sections = append(sections, section)
	}

	// Use batch create for better performance
	_, err := p.repository.BatchCreate(ctx, sections)
	return err
}

// UpdateExtensions updates existing email section declaration extensions.
func (p *emailSectionDeclarationExtensionProvider) UpdateExtensions(
	ctx context.Context,
	extensions []version.ExtensionRevision[sectiondeclarationdomain.EmailSectionDeclarationValue],
) error {
	if len(extensions) == 0 {
		return nil
	}

	// Extract all identifiers for batch query
	identifierIDs := lo.Map(
		extensions,
		func(ext version.ExtensionRevision[sectiondeclarationdomain.EmailSectionDeclarationValue], _ int) string {
			return ext.Identifier.ID
		},
	)

	// Find existing sections in batch
	existingSections, err := p.repository.Find(ctx, sectiondeclarationdomain.Query{
		HostProductCode: extensions[0].HostProductCode, // All extensions should have the same host product code
		Ids:             identifierIDs,
	})
	if err != nil {
		return err
	}

	// Create a map for quick lookup
	existingMap := lo.KeyBy(existingSections, func(section sectiondeclarationdomain.EmailSectionDeclaration) string {
		return section.ID
	})

	// Update sections
	updatedSections := make([]sectiondeclarationdomain.EmailSectionDeclaration, 0, len(extensions))
	now := time.Now()

	for _, ext := range extensions {
		existing, found := existingMap[ext.Identifier.ID]
		if !found {
			// Skip if section not found - this shouldn't happen in normal flow
			continue
		}

		// Update the section with new values and timestamp
		existing.Type = ext.Value.Type
		existing.Name = ext.Value.Name
		existing.Description = ext.Value.Description
		existing.Category = ext.Value.Category
		existing.OrderingWeight = ext.Value.OrderingWeight
		existing.IconURL = ext.Value.IconURL
		existing.ApplicableSetting = ext.Value.ApplicableSetting
		existing.LimitationSetting = ext.Value.LimitationSetting
		existing.InputSetting = ext.Value.InputSetting
		existing.SectionTemplateSetting = ext.Value.SectionTemplateSetting
		existing.PresentationSettings = ext.Value.PresentationSettings
		existing.Hidden = ext.Value.Hidden
		existing.UpdatedAt = &now

		updatedSections = append(updatedSections, existing)
	}

	if len(updatedSections) == 0 {
		return nil
	}

	// Use batch update for better performance
	_, err = p.repository.BatchUpdate(ctx, updatedSections)
	return err
}

// DeleteExtensions deletes email section declaration extensions by their identifiers.
func (p *emailSectionDeclarationExtensionProvider) DeleteExtensions(
	ctx context.Context,
	identifiers []version.ExtensionIdentifier,
) error {
	if len(identifiers) == 0 {
		return nil
	}

	// Extract all identifiers for batch query
	identifierIDs := lo.Map(identifiers, func(identifier version.ExtensionIdentifier, _ int) string {
		return identifier.ID
	})

	// Find existing sections in batch by ID only (since ID should be globally unique)
	// We don't have HostProductCode here, but ID should be sufficient for identification
	existingSections, err := p.repository.Find(ctx, sectiondeclarationdomain.Query{
		Ids: identifierIDs,
	})
	if err != nil {
		return err
	}

	if len(existingSections) == 0 {
		return nil
	}

	// Delete sections in batch by marking them as deleted (soft delete)
	now := time.Now()
	for i := range existingSections {
		existingSections[i].DeletedAt = &now
	}

	// Use batch update to mark as deleted (soft delete)
	_, err = p.repository.BatchUpdate(ctx, existingSections)
	return err
}

// NewEmailSectionDeclarationVersionAgent creates a new VersionAgent for email section declarations.
func NewEmailSectionDeclarationVersionAgent(
	repository sectiondeclarationdomain.EmailSectionDeclarationRepository,
	transactionPort ports.TransactionPort,
) version.VersionAgent[sectiondeclarationdomain.EmailSectionDeclarationValue] {
	provider := NewEmailSectionDeclarationExtensionProvider(repository, transactionPort)
	return version.NewVersionAgent(
		provider,
		sectiondeclarationdomain.EmailSectionExtensionType,
	)
}
