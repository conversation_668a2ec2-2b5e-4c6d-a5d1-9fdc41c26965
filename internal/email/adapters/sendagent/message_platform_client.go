package sendagent

import (
	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/library-marketing-go/serviceclients/messages"
)

var messagesPlatformHosts = map[string]string{
	"local":        "http://pltf-messages.as-in.io/messages/v2",
	"development":  "http://pltf-messages.as-in.io/messages/v2",
	"testing-kiwi": "http://pltf-messages.as-in.io/messages/v2",
	"testing-nike": "http://pltf-messages.as-in.io/messages/v2",
	"testing-pear": "http://pltf-messages.as-in.io/messages/v2",
	"staging":      "http://staging-pltf-messages.as-in.com/messages/v2",
	"production":   "http://pltf-messages.as-in.com/messages/v2",
}

func NewMessageClient() messages.Client {
	return messages.NewClient(messages.ClientParams{
		MessagesPlatformHost: messagesPlatformHosts[cfg.GetCurrentStage()],
	})
}
