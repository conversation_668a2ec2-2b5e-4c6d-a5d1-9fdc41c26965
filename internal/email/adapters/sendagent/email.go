package sendagent

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/samber/lo"

	"github.com/AfterShip/library-heytea-go-common/lang/types"
	"github.com/AfterShip/library-heytea-go-common/xerrors"
	"github.com/AfterShip/library-marketing-go/constants"
	"github.com/AfterShip/library-marketing-go/objects"
	"github.com/AfterShip/library-marketing-go/serviceclients/messages"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/message"
)

var ipPoolNameMapping = map[string]string{
	"feed": "feed_affiliate",
}

type emailSenderAgent struct {
	messagesClient messages.Client
}

func NewSendAgent(messagesClient messages.Client) message.SendAgentPort {
	return &emailSenderAgent{
		messagesClient: messagesClient,
	}
}

func (e *emailSenderAgent) SendEmail(ctx context.Context, message message.EmailMessage) (string, error) {
	sendEmailArgs, err := deriveSendEmailArgs(message)
	if err != nil {
		return "", err
	}
	res, err := e.messagesClient.SendEmailReturnResult(ctx, sendEmailArgs)
	if err != nil {
		return "", handlerSendEmailErr(err, message)
	}
	return res.MessageID, nil
}

func handlerSendEmailErr(err error, email message.EmailMessage) error {
	if err == nil {
		return nil
	}
	switch {
	case errors.Is(err, messages.ErrEmailHasAlreadyBeenSent):
		return xerrors.Wrap(xerrors.CauseBy(message.ErrNotificationAlreadySent, err),
			xerrors.Field("unique_key", email.UniqueKey),
		)
	default:
		return err
	}
}

func deriveSendEmailArgs(email message.EmailMessage) (messages.SendEmailArgs, error) {
	dataTrace := deriveDataTrace(email)
	customFields, err := json.Marshal(dataTrace)
	if err != nil {
		return messages.SendEmailArgs{}, err
	}
	recipient := deriveEmailRecipient(email.SendTo)
	sendEmailArgs := messages.SendEmailArgs{
		IdempotencyKey: types.NewString(fmt.Sprintf("%s-%s-%s",
			email.OrganizationID,
			email.HostProductCode,
			email.UniqueKey,
		)),
		To:      []messages.EmailContact{recipient},
		From:    deriveEmailContact(email.Sender),
		ReplyTo: lo.ToPtr(deriveEmailContact(email.ReplyTo)),
		Subject: email.EmailContent.Subject,
		Contents: []messages.EmailContent{
			{
				Type:  "text/html",
				Value: email.EmailContent.HTML,
			},
		},
		Organization: messages.Organization{Id: email.OrganizationID},
		ProductCode:  constants.ProductCodeEmail,
		AccountType:  "marketing",
		Personalizations: []messages.Personalization{
			{
				To: []messages.EmailContact{recipient},
			},
		},
		CustomFields: customFields,
		Attachments: lo.Map(
			email.EmailContent.Attachments,
			func(attachment message.Attachment, _ int) messages.Attachment {
				return deriveEmailAttachment(attachment)
			},
		),
	}

	ipPoolName := ipPoolNameMapping[email.HostProductCode]
	if ipPoolName != "" {
		sendEmailArgs.IPPoolName = ipPoolName
	}
	return sendEmailArgs, nil
}

func deriveDataTrace(email message.EmailMessage) objects.DataTraceModel {
	dataTraceRecipient := deriveDataTraceRecipient(email.SendTo)
	return objects.DataTraceModel{
		AppKey:                  email.DataTracking.AppKey,
		AppPlatform:             email.DataTracking.AppPlatform,
		HostProductCode:         email.HostProductCode,
		MessageBizType:          objects.MessageBizType(email.DataTracking.BizType),
		MessageBizSubType:       email.DataTracking.BizSubType,
		MessageBizId:            email.DataTracking.BizID,
		MessageContentGroupId:   email.DataTracking.Content.ContentGroupID,
		MessageContentVariantId: email.DataTracking.Content.ContentVariantID,
		Recipient:               dataTraceRecipient,
		Experiment:              nil,
		Order:                   nil,
		Tracking:                nil,
		NotificationId:          email.DataTracking.BizMessageID,
	}
}

func deriveDataTraceRecipient(recipient message.EmailRecipient) *objects.DataTraceRecipient {
	_, firstName, lastName := extractNames(recipient.Name)
	return &objects.DataTraceRecipient{
		Id:        recipient.ID,
		Type:      objects.DataTraceRecipientType(recipient.Type),
		FirstName: firstName,
		LastName:  lastName,
	}
}

//nolint:nonamedreturns // It is a valid check.
func extractNames(name string) (fullName, firstName, lastName string) {
	if name == "" {
		return "", "", ""
	}

	trimName := strings.TrimSpace(name)
	splitedNames := strings.SplitN(trimName, " ", 2) //nolint:mnd // This is a valid check.
	if len(splitedNames) == 2 {                      //nolint:mnd // This is a valid check.
		return trimName, splitedNames[0], splitedNames[1]
	} else {
		return trimName, trimName, ""
	}
}

func deriveEmailAttachment(attachment message.Attachment) messages.Attachment {
	return messages.Attachment{
		Content:     attachment.Content,
		Disposition: attachment.Disposition,
		Filename:    attachment.Filename,
		Type:        attachment.Type,
	}
}

func deriveEmailContact(sender message.EmailSender) messages.EmailContact {
	return messages.EmailContact{
		ContactId: sender.ID,
		Email:     sender.Email,
		Name:      lo.Ternary(sender.Name != "", types.NewString(sender.Name), types.String{}),
	}
}

func deriveEmailRecipient(recipient message.EmailRecipient) messages.EmailContact {
	return messages.EmailContact{
		ContactId: recipient.ID,
		Email:     recipient.Email,
		Name:      lo.Ternary(recipient.Name != "", types.NewString(recipient.Name), types.String{}),
	}
}
