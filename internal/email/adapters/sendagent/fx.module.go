package sendagent

import (
	"go.uber.org/fx"

	"github.com/AfterShip/library-marketing-go/serviceclients/messages"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/message"
)

type Params struct {
	fx.In

	MessageClient messages.Client
}

type Result struct {
	fx.Out

	EmailSenderAgent message.SendAgentPort
}

func New(p Params) Result {
	return Result{
		EmailSenderAgent: NewSendAgent(p.MessageClient),
	}
}

const moduleName = "message_adapter"

var Module = fx.Module(moduleName,
	fx.Provide(NewMessageClient),
	fx.Provide(New))
