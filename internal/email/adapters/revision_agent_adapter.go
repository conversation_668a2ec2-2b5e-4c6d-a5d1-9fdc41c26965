package adapters

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/AfterShip/pltf-nf-infra/pkg/revision"
	emailapplication "github.com/AfterShip/pltf-nf-message/internal/email/application"
	sectiondeclarationdomain "github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"
)

// sectionDeclarationRevisionPortAdapter implements the SectionDeclarationRevisionPort interface using the RevisionAgent.
type sectionDeclarationRevisionPortAdapter struct {
	revisionAgent revision.RevisionAgent[sectiondeclarationdomain.EmailSectionDeclaration]
}

// NewSectionDeclarationRevisionPortAdapter creates a new adapter that implements SectionDeclarationRevisionPort.
func NewSectionDeclarationRevisionPortAdapter(
	revisionAgent revision.RevisionAgent[sectiondeclarationdomain.EmailSectionDeclaration],
) emailapplication.SectionDeclarationRevisionPort {
	return &sectionDeclarationRevisionPortAdapter{
		revisionAgent: revisionAgent,
	}
}

// CreateSectionDeclaration implements SectionDeclarationRevisionPort.CreateSectionDeclaration.
func (a *sectionDeclarationRevisionPortAdapter) CreateSectionDeclaration(
	ctx context.Context,
	args emailapplication.CreateSectionDeclarationPortArgs,
) (emailapplication.VersionManagedEmailSectionDeclaration, error) {
	revisionArgs := revision.CreateExtensionArgs[sectiondeclarationdomain.EmailSectionDeclaration]{
		HostProductCode: args.HostProductCode,
		Extension:       args.SectionDeclaration,
		CreatedBy:       args.CreatedBy,
	}

	return a.revisionAgent.CreateExtension(ctx, revisionArgs)
}

// GetSectionDeclaration implements SectionDeclarationRevisionPort.GetSectionDeclaration.
func (a *sectionDeclarationRevisionPortAdapter) GetSectionDeclaration(
	ctx context.Context,
	args emailapplication.GetSectionDeclarationPortArgs,
) (emailapplication.VersionManagedEmailSectionDeclaration, error) {
	revisionArgs := revision.GetExtensionArgs{
		HostProductCode: args.HostProductCode,
		ID:              args.ID,
	}

	return a.revisionAgent.GetExtension(ctx, revisionArgs)
}

// ListSectionDeclarations implements SectionDeclarationRevisionPort.ListSectionDeclarations.
func (a *sectionDeclarationRevisionPortAdapter) ListSectionDeclarations(
	ctx context.Context,
	args emailapplication.ListSectionDeclarationsPortArgs,
) ([]emailapplication.VersionManagedEmailSectionDeclaration, error) {
	revisionArgs := revision.ListExtensionsArgs{
		HostProductCode: args.HostProductCode,
	}

	return a.revisionAgent.ListWorkingExtensions(ctx, revisionArgs)
}

// UpdateSectionDeclaration implements SectionDeclarationRevisionPort.UpdateSectionDeclaration.
func (a *sectionDeclarationRevisionPortAdapter) UpdateSectionDeclaration(
	ctx context.Context,
	args emailapplication.UpdateSectionDeclarationPortArgs,
) (emailapplication.VersionManagedEmailSectionDeclaration, error) {
	revisionArgs := revision.UpdateExtensionArgs[sectiondeclarationdomain.EmailSectionDeclaration]{
		HostProductCode: args.HostProductCode,
		Extension:       args.SectionDeclaration,
		UpdatedBy:       args.UpdatedBy,
	}

	return a.revisionAgent.UpdateExtension(ctx, revisionArgs)
}

// ResetSectionDeclaration implements SectionDeclarationRevisionPort.ResetSectionDeclaration.
func (a *sectionDeclarationRevisionPortAdapter) ResetSectionDeclaration(
	ctx context.Context,
	args emailapplication.ResetSectionDeclarationPortArgs,
) error {
	// First get the extension to retrieve the identifier
	extension, err := a.revisionAgent.GetExtension(ctx, revision.GetExtensionArgs{
		HostProductCode: args.HostProductCode,
		ID:              args.ID,
	})
	if err != nil {
		return fmt.Errorf("failed to get extension for reset: %w", err)
	}

	revisionArgs := revision.ResetExtensionArgs{
		HostProductCode: args.HostProductCode,
		Identifier:      extension.Extension.Identifier(),
		ResetBy:         args.ResetBy,
	}

	return a.revisionAgent.ResetExtension(ctx, revisionArgs)
}

// EmailSectionDeclarationBuilder builds an EmailSectionDeclaration from an ExtensionRevision.
func EmailSectionDeclarationBuilder(
	rev revision.ExtensionRevision,
) (sectiondeclarationdomain.EmailSectionDeclaration, error) {
	var sectionValue struct {
		HostProductCode        string                                     `json:"host_product_code"`
		Type                   string                                     `json:"type"`
		Name                   string                                     `json:"name"`
		Description            string                                     `json:"description"`
		Category               string                                     `json:"category"`
		OrderingWeight         int64                                      `json:"ordering_weight"`
		IconURL                string                                     `json:"icon_url"`
		ApplicableSetting      sectiondeclarationdomain.ApplicableSetting `json:"applicable_setting"`
		LimitationSetting      sectiondeclarationdomain.LimitationSetting `json:"limitation_setting"`
		InputSetting           json.RawMessage                            `json:"input_setting"`
		SectionTemplateSetting json.RawMessage                            `json:"section_template_setting"`
		PresentationSettings   json.RawMessage                            `json:"presentation_settings"`
		Hidden                 bool                                       `json:"hidden"`
	}

	if err := json.Unmarshal(rev.Value, &sectionValue); err != nil {
		return sectiondeclarationdomain.EmailSectionDeclaration{}, fmt.Errorf(
			"failed to unmarshal section declaration value: %w",
			err,
		)
	}

	section := sectiondeclarationdomain.EmailSectionDeclaration{
		ID:                     rev.ExtensionIdentifier.ID,
		HostProductCode:        sectionValue.HostProductCode,
		Handle:                 rev.ExtensionIdentifier.Handle,
		Type:                   sectionValue.Type,
		Name:                   sectionValue.Name,
		Description:            sectionValue.Description,
		Category:               sectionValue.Category,
		OrderingWeight:         sectionValue.OrderingWeight,
		IconURL:                sectionValue.IconURL,
		ApplicableSetting:      sectionValue.ApplicableSetting,
		LimitationSetting:      sectionValue.LimitationSetting,
		InputSetting:           sectionValue.InputSetting,
		SectionTemplateSetting: sectionValue.SectionTemplateSetting,
		PresentationSettings:   sectionValue.PresentationSettings,
		Hidden:                 sectionValue.Hidden,
		CreatedAt:              rev.CreatedAt,
	}

	// Set UpdatedAt only if it exists and is different from CreatedAt
	if rev.UpdatedAt != nil && !rev.UpdatedAt.Equal(rev.CreatedAt) {
		section.UpdatedAt = rev.UpdatedAt
	}

	return section, nil
}

// NewEmailSectionDeclarationRevisionAgent creates a new RevisionAgent for EmailSectionDeclaration.
func NewEmailSectionDeclarationRevisionAgent() revision.RevisionAgent[sectiondeclarationdomain.EmailSectionDeclaration] {
	return revision.NewRevisionAgent(
		sectiondeclarationdomain.EmailSectionExtensionType,
		EmailSectionDeclarationBuilder,
	)
}
