package encryptor

import (
	"encoding/json"

	"github.com/AfterShip/library-marketing-go/env"
	"github.com/AfterShip/library-marketing-go/msgcipher"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
)

var (
	testAESKey = "Bh0dRo2XAWs1exVAkObnKsVbFk5WxQ3PhKOngwjO0+Q"
)

type subscriptionMsgCipher struct {
	cipher *msgcipher.MsgCipher
}

func NewSubscriptionMsgCipher(aesKey string) recipientprofile.SubscriptionEncryptor {
	if env.IsLocalEnv() && aesKey == "" {
		aesKey = testAESKey
	}
	return &subscriptionMsgCipher{
		cipher: msgcipher.NewMsgCipher(aesKey),
	}
}

func (c *subscriptionMsgCipher) Encrypt(info recipientprofile.SubscriptionInfo) (string, error) {
	subscriptionInfoJson, err := json.Marshal(info)
	if err != nil {
		return "", err
	}
	encryptedToken, err := c.cipher.EncryptMsg(string(subscriptionInfoJson))
	if err != nil {
		return "", err
	}
	return encryptedToken, nil
}

func (c *subscriptionMsgCipher) Decrypt(token string) (recipientprofile.SubscriptionInfo, error) {
	decryptedToken, err := c.cipher.DecryptMsg(token)
	if err != nil {
		return recipientprofile.SubscriptionInfo{}, err
	}
	var subscriptionInfo recipientprofile.SubscriptionInfo
	err = json.Unmarshal([]byte(decryptedToken), &subscriptionInfo)
	if err != nil {
		return recipientprofile.SubscriptionInfo{}, err
	}
	return subscriptionInfo, nil
}

func (c *subscriptionMsgCipher) Version() string {
	return "v1"
}
