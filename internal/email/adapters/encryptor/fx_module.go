package encryptor

import (
	"go.uber.org/fx"

	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
)

type Params struct {
	fx.In

	AESEncryptionKey string
}

type Result struct {
	fx.Out

	EncryptorRouter recipientprofile.SubscriptionEncryptorRouter
}

func New(params Params) Result {
	router := NewEncryptorRouter()
	_ = router.RegisterEncryptor(NewSubscriptionMsgCipher(params.AESEncryptionKey))
	return Result{
		EncryptorRouter: router,
	}
}

const moduleName = "encryptor_adapter"

var Module = fx.Module(moduleName,
	fx.Provide(New),
)
