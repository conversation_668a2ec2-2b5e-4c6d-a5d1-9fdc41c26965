package encryptor

import (
	"errors"

	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
)

var (
	ErrEncryptorNotFound = errors.New("encryptor not found")
	ErrEncryptorExists   = errors.New("encryptor already exists")
)

type encryptorRouter struct {
	encryptors    map[string]recipientprofile.SubscriptionEncryptor
	latestVersion string
}

func NewEncryptorRouter() recipientprofile.SubscriptionEncryptorRouter {
	return &encryptorRouter{
		encryptors: make(map[string]recipientprofile.SubscriptionEncryptor),
	}
}

func (r *encryptorRouter) GetEncryptor(version string) (recipientprofile.SubscriptionEncryptor, error) {
	encryptor, ok := r.encryptors[version]
	if !ok {
		return nil, ErrEncryptorNotFound
	}
	return encryptor, nil
}

func (r *encryptorRouter) RegisterEncryptor(encryptor recipientprofile.SubscriptionEncryptor) error {
	version := encryptor.Version()
	if _, ok := r.encryptors[version]; ok {
		return ErrEncryptorExists
	}
	r.encryptors[version] = encryptor
	// string compare
	if version > r.latestVersion {
		r.latestVersion = version
	}
	return nil
}

func (r *encryptorRouter) GetLatestEncryptor() (recipientprofile.SubscriptionEncryptor, error) {
	if r.latestVersion == "" {
		return nil, ErrEncryptorNotFound
	}
	return r.encryptors[r.latestVersion], nil
}
