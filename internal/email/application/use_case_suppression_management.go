package emailapplication

import "context"

type UseCaseSuppressionManagement interface {
	AddSuppression(ctx context.Context, params AddSuppressionParams) error
	RemoveSuppression(ctx context.Context, params RemoveSuppressionParams) error
}

type AddSuppressionParams struct {
	OrganizationID  string `json:"organization_id"`
	HostProductCode string `json:"host_product_code"`
	Email           string `json:"email"`
	Type            string `json:"type"`
	Reason          string `json:"reason"`
}

type RemoveSuppressionParams struct {
	OrganizationID  string `json:"organization_id"`
	HostProductCode string `json:"host_product_code"`
	Email           string `json:"email"`
	Type            string `json:"type"`
}
