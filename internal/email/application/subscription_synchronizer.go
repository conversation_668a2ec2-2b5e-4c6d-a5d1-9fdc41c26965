package emailapplication

import (
	"context"
	"errors"
	"time"

	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/library-heytea-go-common/xerrors"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
)

// SubscriptionSynchronizer synchronizes subscription status between contact and recipient profile.
type SubscriptionSynchronizer interface {
	// SynchronizeToContact synchronizes subscription status from recipient profile to contact when recipient subscription status is updated.
	SynchronizeToContact(ctx context.Context, params SynchronizeToContactParams) error

	// SynchronizeFromContact synchronizes subscription status from contact to recipient profile when contact is updated.
	SynchronizeFromContact(ctx context.Context, params SynchronizeFromContactParams) error
}

type SynchronizeToContactParams struct {
	OrganizationID     string    `json:"organization_id"`
	HostProductCode    string    `json:"host_product_code"`
	AppPlatform        string    `json:"app_platform"`
	AppKey             string    `json:"app_key"`
	Email              string    `json:"email"`
	SubscriptionStatus string    `json:"subscription_status"`
	EffectiveAt        time.Time `json:"effective_at"`
}

type SynchronizeFromContactParams struct {
	OrganizationID  string `json:"organization_id"`
	HostProductCode string `json:"host_product_code"`
	AppPlatform     string `json:"app_platform"`
	AppKey          string `json:"app_key"`
	ContactID       string `json:"contact_id"`
}

type subscriptionSynchronizer struct {
	acrmRecipientProfileProvider ACRMRecipientProfileProvider
	recipientProfileRepository   recipientprofile.RecipientProfileRepository
}

func (s *subscriptionSynchronizer) SynchronizeToContact(ctx context.Context, params SynchronizeToContactParams) error {
	contactRecipient, err := s.acrmRecipientProfileProvider.GetRecipientProfileByEmail(
		ctx,
		GetRecipientProfileByEmailParams{
			Email:          params.Email,
			OrganizationID: params.OrganizationID,
			AppPlatform:    params.AppPlatform,
			AppKey:         params.AppKey,
		},
	)
	if err != nil {
		if errors.Is(err, xerrors.ErrBusinessRecordNotFound) {
			return nil
		}
		return err
	}
	_, contactChangedAt := contactRecipient.GetAMPreferenceStatusAndEffectiveAt()
	if contactChangedAt.After(params.EffectiveAt) {
		return nil
	}
	return s.acrmRecipientProfileProvider.UpdateRecipientSubscriptionStatus(
		ctx,
		UpdateRecipientSubscriptionStatusParams{
			RecipientID:        contactRecipient.ID,
			OrganizationID:     params.OrganizationID,
			AppPlatform:        params.AppPlatform,
			AppKey:             params.AppKey,
			SubscriptionStatus: params.SubscriptionStatus,
			ChangedAt:          params.EffectiveAt,
		},
	)
}

func (s *subscriptionSynchronizer) SynchronizeFromContact(
	ctx context.Context,
	params SynchronizeFromContactParams,
) error {
	contactRecipient, err := s.acrmRecipientProfileProvider.GetRecipientProfileByID(ctx, GetRecipientProfileByIDParams{
		RecipientID:    params.ContactID,
		OrganizationID: params.OrganizationID,
		AppPlatform:    params.AppPlatform,
		AppKey:         params.AppKey,
	})
	if err != nil {
		if errors.Is(err, xerrors.ErrBusinessRecordNotFound) {
			return nil
		}
		return err
	}
	return s.recipientProfileRepository.RunInTransaction(ctx, func(ctx context.Context) error {
		pnRecipient, getErr := s.acrmRecipientProfileProvider.GetRecipientProfileByEmail(
			ctx,
			GetRecipientProfileByEmailParams{
				Email:          contactRecipient.Email,
				OrganizationID: params.OrganizationID,
				AppPlatform:    params.AppPlatform,
				AppKey:         params.AppKey,
			},
		)
		if getErr != nil {
			if errors.Is(getErr, xerrors.ErrBusinessRecordNotFound) {
				newRecipientProfile := recipientprofile.RecipientProfile{
					ID:             s.recipientProfileRepository.NextID(ctx),
					OrganizationID: params.OrganizationID,
					AppPlatform:    params.AppPlatform,
					AppKey:         params.AppKey,
					Email:          contactRecipient.Email,
					Subscription: recipientprofile.Subscription{
						SubscriptionID: uuid.GenerateUUIDV4(),
						GlobalUnsubscribe: recipientprofile.GlobalUnsubscribe{
							IsUnsubscribed: contactRecipient.Subscription.GlobalUnsubscribe.IsUnsubscribed,
							UnsubscribedAt: contactRecipient.Subscription.GlobalUnsubscribe.UnsubscribedAt,
							Method:         recipientprofile.UpdateTriggerContactEvent,
						},
						NotificationPreferences: contactRecipient.Subscription.NotificationPreferences,
					},
				}
				return s.recipientProfileRepository.Create(ctx, &newRecipientProfile)
			}
			return getErr
		}
		_, pnEffectiveAt := pnRecipient.GetAMPreferenceStatusAndEffectiveAt()
		contactSubscriptionStatus, contactEffectiveAt := contactRecipient.GetAMPreferenceStatusAndEffectiveAt()
		if pnEffectiveAt.After(contactEffectiveAt) {
			return nil
		}
		if contactSubscriptionStatus == recipientprofile.SubscriptionStatusUnsubscribed {
			pnRecipient.Unsubscribe(contactEffectiveAt, recipientprofile.UpdateTriggerContactEvent)
		} else {
			pnRecipient.UpdateAMPreferenceStatus(contactSubscriptionStatus, contactEffectiveAt, recipientprofile.UpdateTriggerContactEvent)
		}
		return nil
	})
}

// NewSubscriptionSynchronizer creates a new subscription synchronizer instance.
func NewSubscriptionSynchronizer(
	provider ACRMRecipientProfileProvider,
	recipientProfileRepository recipientprofile.RecipientProfileRepository,
) SubscriptionSynchronizer {
	return &subscriptionSynchronizer{
		acrmRecipientProfileProvider: provider,
		recipientProfileRepository:   recipientProfileRepository,
	}
}
