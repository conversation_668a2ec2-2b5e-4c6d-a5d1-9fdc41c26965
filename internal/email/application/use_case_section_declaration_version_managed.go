package emailapplication

import (
	"context"
	"encoding/json"

	"github.com/AfterShip/pltf-nf-infra/pkg/revision"
	"github.com/AfterShip/pltf-nf-infra/pkg/version"
	sectiondeclarationdomain "github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"
)

type CreateSectionDeclarationVersionManagedArgs struct {
	HostProductCode        string                                     `json:"host_product_code"        body:"host_product_code"        validate:"required"`
	Handle                 string                                     `json:"handle"                   body:"handle"                   validate:"required"`
	Type                   string                                     `json:"type"                     body:"type"                     validate:"required"`
	Name                   string                                     `json:"name"                     body:"name"                     validate:"required"`
	Description            string                                     `json:"description"              body:"description"`
	Category               string                                     `json:"category"                 body:"category"`
	OrderingWeight         int64                                      `json:"ordering_weight"          body:"ordering_weight"`
	IconURL                string                                     `json:"icon_url"                 body:"icon_url"`
	ApplicableSetting      sectiondeclarationdomain.ApplicableSetting `json:"applicable_setting"       body:"applicable_setting"`
	LimitationSetting      sectiondeclarationdomain.LimitationSetting `json:"limitation_setting"       body:"limitation_setting"`
	InputSetting           json.RawMessage                            `json:"input_setting"            body:"input_setting"`
	SectionTemplateSetting json.RawMessage                            `json:"section_template_setting" body:"section_template_setting"`
	PresentationSettings   json.RawMessage                            `json:"presentation_settings"    body:"presentation_settings"`
	OperatorEmail          string                                     `json:"operator_email"           body:"operator_email"           validate:"required"`
}

type UpdateSectionDeclarationArgs struct {
	HostProductCode        string                                     `json:"host_product_code"        body:"host_product_code"        validate:"required"`
	ID                     string                                     `json:"id"                                                       validate:"required" path:"id"`
	Name                   string                                     `json:"name"                     body:"name"                     validate:"required"`
	Description            string                                     `json:"description"              body:"description"`
	Category               string                                     `json:"category"                 body:"category"`
	OrderingWeight         int64                                      `json:"ordering_weight"          body:"ordering_weight"`
	IconURL                string                                     `json:"icon_url"                 body:"icon_url"`
	ApplicableSetting      sectiondeclarationdomain.ApplicableSetting `json:"applicable_setting"       body:"applicable_setting"`
	LimitationSetting      sectiondeclarationdomain.LimitationSetting `json:"limitation_setting"       body:"limitation_setting"`
	InputSetting           json.RawMessage                            `json:"input_setting"            body:"input_setting"`
	SectionTemplateSetting json.RawMessage                            `json:"section_template_setting" body:"section_template_setting"`
	PresentationSettings   json.RawMessage                            `json:"presentation_settings"    body:"presentation_settings"`
	UpdatedBy              string                                     `json:"updated_by"               body:"updated_by"               validate:"required"`
}

type ListSectionDeclarationsArgs struct {
	// HostProductCode the product code of the host product
	HostProductCode string `json:"host_product_code" query:"host_product_code"`
}

type ResetSectionDeclarationArgs struct {
	HostProductCode string `json:"host_product_code" query:"host_product_code"`
	ID              string `json:"id"                                          path:"id" validate:"required"`
	ResetBy         string `json:"reset_by"                                              validate:"required" body:"reset_by"`
}

type GetSectionDeclarationArgs struct {
	HostProductCode string `json:"host_product_code" query:"host_product_code"`
	ID              string `json:"id"                                          path:"id" validate:"required"`
}

type VersionManagedEmailSectionDeclaration = revision.VersionManagedExtension[sectiondeclarationdomain.EmailSectionDeclaration]

// SectionDeclarationReleaseArgs defines the arguments for releasing email section declarations.
type SectionDeclarationReleaseArgs = version.ReleaseArgs[sectiondeclarationdomain.EmailSectionDeclarationValue]

// SectionDeclarationExtensionIdentifier identifies an email section declaration extension.
type SectionDeclarationExtensionIdentifier = revision.ExtensionIdentifier

// ucVersionManagedSectionDeclaration defines the contract for managing email section declarations in the message console.
//
//go:generate mockery --with-expecter --name ucVersionManagedSectionDeclaration --filename use_case_section_declaration_manager.mock.gen.go --output ./ --inpackage --structname MockUseCaseSectionDeclarationManager
type ucVersionManagedSectionDeclaration interface {
	GetSectionDeclaration(
		ctx context.Context,
		args GetSectionDeclarationArgs,
	) (VersionManagedEmailSectionDeclaration, error)
	ListSectionDeclarations(
		ctx context.Context,
		args ListSectionDeclarationsArgs,
	) ([]VersionManagedEmailSectionDeclaration, error)
	CreateSectionDeclaration(
		ctx context.Context,
		args CreateSectionDeclarationVersionManagedArgs,
	) (VersionManagedEmailSectionDeclaration, error)
	UpdateSectionDeclaration(
		ctx context.Context,
		args UpdateSectionDeclarationArgs,
	) (VersionManagedEmailSectionDeclaration, error)
	ResetSectionDeclaration(ctx context.Context, args ResetSectionDeclarationArgs) error

	// Release releases a version of email section declarations
	Release(ctx context.Context, args SectionDeclarationReleaseArgs) error
}
