package emailapplication

import (
	"context"
	"errors"
	"fmt"

	"github.com/samber/lo"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/library-heytea-go-common/sync/errgroup"
	"github.com/AfterShip/library-heytea-go-common/xerrors"
	"github.com/AfterShip/library-marketing-go/objects"
	"github.com/AfterShip/library-marketing-go/tenantscope"
	"github.com/AfterShip/pltf-nf-message/internal/commoncontent/sharedkernel"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/message"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/template"
)

var (
	ErrRecipientSubscriptionNotCompliant = errors.New("recipient subscription is not compliant")
)

type UseCaseRenderAndSendEmail interface {
	SendEmailByTemplateGroupID(ctx context.Context, params SendEmailByTemplateGroupIDParams) (string, error)
}

type SendEmailByTemplateGroupIDParams struct {
	OrganizationID  string
	HostProductCode string
	Version         string
	AppPlatform     string
	AppKey          string
	TemplateGroupID string
	LanguageTag     *string
	Data            map[string]any
	Recipient       Recipient
	UniqueKey       string
}

type Recipient struct {
	ID    string `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
	Type  string `json:"type"`
}

type useCaseEmailDelivery struct {
	mergeTagDeclarationRepository     sharedkernel.MergeTagDeclarationRepository
	templateGroupRepository           template.TemplateGroupRepository
	renderSettingRepository           sharedkernel.RenderSettingRepository
	templateVariantRepository         template.TemplateVariantRepository
	emailSectionDeclarationRepository sectiondeclaration.EmailSectionDeclarationRepository
	templateDomainService             template.DomainService
	messageDomainService              message.DomainService
	recipientProfileDomainService     recipientprofile.DomainService
	organizationSenderGetter          message.OrganizationSenderGetter
	deliveryPort                      message.SendAgentPort
	emailMessageRepository            message.EmailMessageRepository
	recipientProfileRepository        recipientprofile.RecipientProfileRepository
	subscriptionComplianceRuleRepo    recipientprofile.SubscriptionComplianceRuleRepository
}

type UseCaseEmailDeliveryDependencies struct {
	MergeTagDeclarationRepository     sharedkernel.MergeTagDeclarationRepository
	TemplateGroupRepository           template.TemplateGroupRepository
	RenderSettingRepository           sharedkernel.RenderSettingRepository
	TemplateVariantRepository         template.TemplateVariantRepository
	EmailSectionDeclarationRepository sectiondeclaration.EmailSectionDeclarationRepository
	TemplateDomainService             template.DomainService
	MessageDomainService              message.DomainService
	OrganizationSenderGetter          message.OrganizationSenderGetter
	DeliveryPort                      message.SendAgentPort
	EmailMessageRepository            message.EmailMessageRepository
	RecipientProfileRepository        recipientprofile.RecipientProfileRepository
	RecipientProfileDomainService     recipientprofile.DomainService
	SubscriptionComplianceRuleRepo    recipientprofile.SubscriptionComplianceRuleRepository
}

func NewUseCaseEmailDelivery(d UseCaseEmailDeliveryDependencies) UseCaseRenderAndSendEmail {
	return &useCaseEmailDelivery{
		mergeTagDeclarationRepository:     d.MergeTagDeclarationRepository,
		templateGroupRepository:           d.TemplateGroupRepository,
		renderSettingRepository:           d.RenderSettingRepository,
		templateVariantRepository:         d.TemplateVariantRepository,
		emailSectionDeclarationRepository: d.EmailSectionDeclarationRepository,
		templateDomainService:             d.TemplateDomainService,
		messageDomainService:              d.MessageDomainService,
		organizationSenderGetter:          d.OrganizationSenderGetter,
		deliveryPort:                      d.DeliveryPort,
		emailMessageRepository:            d.EmailMessageRepository,
		recipientProfileRepository:        d.RecipientProfileRepository,
		recipientProfileDomainService:     d.RecipientProfileDomainService,
		subscriptionComplianceRuleRepo:    d.SubscriptionComplianceRuleRepo,
	}
}

//
//nolint:funlen // This is a complex use case, so it's ok to have a long function.
func (uc *useCaseEmailDelivery) SendEmailByTemplateGroupID(
	ctx context.Context,
	params SendEmailByTemplateGroupIDParams,
) (string, error) {
	emailMessage := message.NewEmailMessage(message.CreateEmailMessageParams{
		OrganizationID:  params.OrganizationID,
		HostProductCode: params.HostProductCode,
		Recipient: message.EmailRecipient{
			ID:    params.Recipient.ID,
			Name:  params.Recipient.Name,
			Email: params.Recipient.Email,
			Type:  params.Recipient.Type,
		},
		UniqueKey: params.UniqueKey,
		Data:      params.Data,
	})

	// load template resources
	mergeTagDeclarations, templateGroup, renderSettingAggregate, emailSectionDeclarations, err := uc.loadTemplateResources(
		ctx,
		params,
	)
	if err != nil {
		return "", err
	}

	// pick template variant based on factors
	selectedVariant, err := uc.selectTemplateVariant(ctx, templateGroup, params.LanguageTag)
	if err != nil {
		return "", err
	}

	unsubscribeURL, err := uc.recipientProfileDomainService.GenerateUnsubscribeURL(
		ctx,
		recipientprofile.GenerateUnsubscribeURLParams{
			Email:           params.Recipient.Email,
			OrganizationID:  params.OrganizationID,
			HostProductCode: params.HostProductCode,
			AppPlatform:     params.AppPlatform,
			AppKey:          params.AppKey,
		},
	)
	if err != nil {
		return "", err
	}

	// render template to content
	renderResult, err := uc.templateDomainService.RenderTemplateToContent(ctx, template.RenderTemplateToContentParams{
		OrganizationID:           params.OrganizationID,
		HostProductCode:          params.HostProductCode,
		AppPlatform:              params.AppPlatform,
		AppKey:                   params.AppKey,
		EmailTemplate:            selectedVariant.EmailTemplate,
		LanguageTag:              params.LanguageTag,
		Data:                     params.Data,
		RenderSettings:           renderSettingAggregate,
		EmailSectionDeclarations: emailSectionDeclarations,
		MergeTagDeclarations:     mergeTagDeclarations,
		UnsubscribeURL:           unsubscribeURL,
	})
	if err != nil {
		return "", err
	}
	emailMessage.MarkAsRendered(renderResult.Subject, renderResult.Body)

	emailSender, err := uc.selectEmailSender(ctx, selectEmailSenderParams{
		organizationID:         params.OrganizationID,
		hostProductCode:        params.HostProductCode,
		renderSettingAggregate: renderSettingAggregate,
		variant:                selectedVariant,
	})
	if err != nil {
		return "", err
	}

	recipientProfile, err := uc.recipientProfileRepository.Find(ctx, recipientprofile.FindParams{
		Email:          params.Recipient.Email,
		OrganizationID: params.OrganizationID,
		AppPlatform:    params.AppPlatform,
		AppKey:         params.AppKey,
	})
	if err != nil && !errors.Is(err, xerrors.ErrBusinessRecordNotFound) {
		return "", xerrors.Wrap(err)
	}
	if isSuppressed, reason := recipientProfile.IsSuppressed(); isSuppressed {
		return "", fmt.Errorf("recipient is suppressed, reason: %s", reason)
	}
	subscriptionComplianceRules, err := uc.subscriptionComplianceRuleRepo.FindByHostProductCode(
		ctx,
		params.HostProductCode,
	)
	if err != nil {
		return "", xerrors.Wrap(err)
	}
	isCompliant, err := uc.recipientProfileDomainService.CheckSubscriptionCompliance(
		ctx,
		recipientprofile.SubscriptionCheckParams{
			BusinessScenario:                   "email",
			HostProductCode:                    params.HostProductCode,
			RecipientProfile:                   recipientProfile,
			ProductSubscriptionComplianceRules: subscriptionComplianceRules,
		},
	)
	if err != nil {
		return "", err
	}
	if !isCompliant {
		return "", ErrRecipientSubscriptionNotCompliant
	}

	messageID, err := uc.deliveryPort.SendEmail(ctx, message.EmailMessage{
		OrganizationID:  params.OrganizationID,
		HostProductCode: params.HostProductCode,
		Sender:          emailSender,
		ReplyTo: message.EmailSender{
			ID:   selectedVariant.EmailTemplate.ReplyTo.Pick(emailSender.Email),
			Name: lo.Ternary(selectedVariant.EmailTemplate.ReplyTo.Type != "custom", emailSender.Name, ""),
		},
		SendTo: message.EmailRecipient{
			ID:    params.Recipient.ID,
			Type:  params.Recipient.Type,
			Email: params.Recipient.Email,
			Name:  params.Recipient.Name,
		},
		EmailContent: message.EmailContent{
			Subject: renderResult.Subject,
			HTML:    renderResult.Body,
		},
		UniqueKey: params.UniqueKey,
		DataTracking: message.DataTracking{
			DataTrackingInput: message.DataTrackingInput{}, // TODO: fill in the data tracking input
			Content: message.DataTrackingContent{
				ContentGroupID:   params.TemplateGroupID,
				ContentVariantID: selectedVariant.Id,
			},
		},
	})

	if err != nil {
		return "", err
	}

	emailMessage.MarkAsAttempted(messageID)

	// TODO(Samuel): handle error
	_ = uc.emailMessageRepository.Create(ctx, *emailMessage)
	return messageID, nil
}

func (uc *useCaseEmailDelivery) loadTemplateResources(ctx context.Context, params SendEmailByTemplateGroupIDParams) (
	[]sharedkernel.MergeTagDeclaration,
	template.TemplateGroup,
	sharedkernel.RenderSettingAggregate,
	[]sectiondeclaration.EmailSectionDeclaration,
	error,
) {
	var (
		mergeTagDeclarations     []sharedkernel.MergeTagDeclaration
		templateGroup            template.TemplateGroup
		renderSettingAggregate   sharedkernel.RenderSettingAggregate
		emailSectionDeclarations []sectiondeclaration.EmailSectionDeclaration
		err                      error
	)

	// 并行加载资源
	eg := errgroup.WithContext(ctx)
	eg.Go(func(ctx context.Context) error {
		mergeTagDeclarations, err = uc.mergeTagDeclarationRepository.FindByHostProductCode(ctx, params.HostProductCode)
		return err
	})
	eg.Go(func(ctx context.Context) error {
		templateGroup, err = uc.templateGroupRepository.FindByID(ctx, params.TemplateGroupID)
		return err
	})
	eg.Go(func(ctx context.Context) error {
		renderSettingAggregate, err = uc.renderSettingRepository.FindByTenantAndHostProduct(ctx,
			tenantscope.NewOrgConfigScope(objects.Organization{Id: params.OrganizationID}), params.HostProductCode)
		return err
	})
	eg.Go(func(ctx context.Context) error {
		emailSectionDeclarations, err = uc.emailSectionDeclarationRepository.FindByHostProductCode(
			ctx,
			params.HostProductCode,
		)
		if err != nil && errors.Is(err, spannerx.ErrRecordNotFound) {
			return nil // 允许 section declarations 不存在
		}
		return err
	})

	if err := eg.Wait(); err != nil {
		return nil, template.TemplateGroup{}, sharedkernel.RenderSettingAggregate{}, nil, err
	}

	return mergeTagDeclarations, templateGroup, renderSettingAggregate, emailSectionDeclarations, nil
}

func (uc *useCaseEmailDelivery) selectTemplateVariant(
	ctx context.Context,
	templateGroup template.TemplateGroup,
	languageTag *string,
) (template.TemplateVariant, error) {
	// 确定语言标签
	lang := "en"
	if languageTag != nil {
		lang = *languageTag
	}

	// 根据因子选择变体
	variantID, err := templateGroup.PickVariantIdByFactors(template.VariantActivationFactors{
		LanguageCodes: []string{lang},
	})
	if err != nil {
		return template.TemplateVariant{}, err
	}

	return uc.templateVariantRepository.FindByID(ctx, variantID)
}

type selectEmailSenderParams struct {
	organizationID         string
	hostProductCode        string
	renderSettingAggregate sharedkernel.RenderSettingAggregate
	variant                template.TemplateVariant
}

func (uc *useCaseEmailDelivery) selectEmailSender(
	ctx context.Context,
	params selectEmailSenderParams,
) (message.EmailSender, error) {
	orgSenders, err := uc.organizationSenderGetter.GetOrganizationSenders(ctx, params.organizationID)
	if err != nil {
		return message.EmailSender{}, err
	}

	globalSender, err := params.renderSettingAggregate.GetGlobalSenderInfo()
	if err != nil {
		return message.EmailSender{}, err
	}

	emailSender, err := uc.messageDomainService.ChooseEmailSender(ctx, message.ChooseEmailSenderParams{
		Mode:                params.variant.EmailTemplate.Sender.Mode,
		SenderSource:        params.variant.EmailTemplate.Sender.SenderSource,
		OrganizationSenders: orgSenders,
		TemplateSender: message.EmailSender{
			ID:   params.variant.EmailTemplate.Sender.Id,
			Name: params.variant.EmailTemplate.Sender.FromName,
		},
		GlobalSender: message.EmailSender{
			ID:   globalSender.SenderId,
			Name: globalSender.Name,
		},
	})
	if err != nil {
		return message.EmailSender{}, err
	}

	return emailSender, nil
}
