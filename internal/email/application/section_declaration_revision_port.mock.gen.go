// Code generated by mockery v2.52.3. DO NOT EDIT.

package emailapplication

import (
	context "context"

	revision "github.com/AfterShip/pltf-nf-infra/pkg/revision"
	mock "github.com/stretchr/testify/mock"

	sectiondeclaration "github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"
)

// MockSectionDeclarationRevisionPort is an autogenerated mock type for the SectionDeclarationRevisionPort type
type MockSectionDeclarationRevisionPort struct {
	mock.Mock
}

type MockSectionDeclarationRevisionPort_Expecter struct {
	mock *mock.Mock
}

func (_m *MockSectionDeclarationRevisionPort) EXPECT() *MockSectionDeclarationRevisionPort_Expecter {
	return &MockSectionDeclarationRevisionPort_Expecter{mock: &_m.Mock}
}

// CreateSectionDeclaration provides a mock function with given fields: ctx, args
func (_m *MockSectionDeclarationRevisionPort) CreateSectionDeclaration(ctx context.Context, args CreateSectionDeclarationPortArgs) (revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CreateSectionDeclaration")
	}

	var r0 revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, CreateSectionDeclarationPortArgs) (revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, CreateSectionDeclarationPortArgs) revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration]); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration])
	}

	if rf, ok := ret.Get(1).(func(context.Context, CreateSectionDeclarationPortArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSectionDeclarationRevisionPort_CreateSectionDeclaration_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateSectionDeclaration'
type MockSectionDeclarationRevisionPort_CreateSectionDeclaration_Call struct {
	*mock.Call
}

// CreateSectionDeclaration is a helper method to define mock.On call
//   - ctx context.Context
//   - args CreateSectionDeclarationPortArgs
func (_e *MockSectionDeclarationRevisionPort_Expecter) CreateSectionDeclaration(ctx interface{}, args interface{}) *MockSectionDeclarationRevisionPort_CreateSectionDeclaration_Call {
	return &MockSectionDeclarationRevisionPort_CreateSectionDeclaration_Call{Call: _e.mock.On("CreateSectionDeclaration", ctx, args)}
}

func (_c *MockSectionDeclarationRevisionPort_CreateSectionDeclaration_Call) Run(run func(ctx context.Context, args CreateSectionDeclarationPortArgs)) *MockSectionDeclarationRevisionPort_CreateSectionDeclaration_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(CreateSectionDeclarationPortArgs))
	})
	return _c
}

func (_c *MockSectionDeclarationRevisionPort_CreateSectionDeclaration_Call) Return(_a0 revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], _a1 error) *MockSectionDeclarationRevisionPort_CreateSectionDeclaration_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSectionDeclarationRevisionPort_CreateSectionDeclaration_Call) RunAndReturn(run func(context.Context, CreateSectionDeclarationPortArgs) (revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error)) *MockSectionDeclarationRevisionPort_CreateSectionDeclaration_Call {
	_c.Call.Return(run)
	return _c
}

// GetSectionDeclaration provides a mock function with given fields: ctx, args
func (_m *MockSectionDeclarationRevisionPort) GetSectionDeclaration(ctx context.Context, args GetSectionDeclarationPortArgs) (revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetSectionDeclaration")
	}

	var r0 revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, GetSectionDeclarationPortArgs) (revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, GetSectionDeclarationPortArgs) revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration]); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration])
	}

	if rf, ok := ret.Get(1).(func(context.Context, GetSectionDeclarationPortArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSectionDeclarationRevisionPort_GetSectionDeclaration_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSectionDeclaration'
type MockSectionDeclarationRevisionPort_GetSectionDeclaration_Call struct {
	*mock.Call
}

// GetSectionDeclaration is a helper method to define mock.On call
//   - ctx context.Context
//   - args GetSectionDeclarationPortArgs
func (_e *MockSectionDeclarationRevisionPort_Expecter) GetSectionDeclaration(ctx interface{}, args interface{}) *MockSectionDeclarationRevisionPort_GetSectionDeclaration_Call {
	return &MockSectionDeclarationRevisionPort_GetSectionDeclaration_Call{Call: _e.mock.On("GetSectionDeclaration", ctx, args)}
}

func (_c *MockSectionDeclarationRevisionPort_GetSectionDeclaration_Call) Run(run func(ctx context.Context, args GetSectionDeclarationPortArgs)) *MockSectionDeclarationRevisionPort_GetSectionDeclaration_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(GetSectionDeclarationPortArgs))
	})
	return _c
}

func (_c *MockSectionDeclarationRevisionPort_GetSectionDeclaration_Call) Return(_a0 revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], _a1 error) *MockSectionDeclarationRevisionPort_GetSectionDeclaration_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSectionDeclarationRevisionPort_GetSectionDeclaration_Call) RunAndReturn(run func(context.Context, GetSectionDeclarationPortArgs) (revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error)) *MockSectionDeclarationRevisionPort_GetSectionDeclaration_Call {
	_c.Call.Return(run)
	return _c
}

// ListSectionDeclarations provides a mock function with given fields: ctx, args
func (_m *MockSectionDeclarationRevisionPort) ListSectionDeclarations(ctx context.Context, args ListSectionDeclarationsPortArgs) ([]revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for ListSectionDeclarations")
	}

	var r0 []revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ListSectionDeclarationsPortArgs) ([]revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ListSectionDeclarationsPortArgs) []revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration]); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ListSectionDeclarationsPortArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSectionDeclarationRevisionPort_ListSectionDeclarations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListSectionDeclarations'
type MockSectionDeclarationRevisionPort_ListSectionDeclarations_Call struct {
	*mock.Call
}

// ListSectionDeclarations is a helper method to define mock.On call
//   - ctx context.Context
//   - args ListSectionDeclarationsPortArgs
func (_e *MockSectionDeclarationRevisionPort_Expecter) ListSectionDeclarations(ctx interface{}, args interface{}) *MockSectionDeclarationRevisionPort_ListSectionDeclarations_Call {
	return &MockSectionDeclarationRevisionPort_ListSectionDeclarations_Call{Call: _e.mock.On("ListSectionDeclarations", ctx, args)}
}

func (_c *MockSectionDeclarationRevisionPort_ListSectionDeclarations_Call) Run(run func(ctx context.Context, args ListSectionDeclarationsPortArgs)) *MockSectionDeclarationRevisionPort_ListSectionDeclarations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ListSectionDeclarationsPortArgs))
	})
	return _c
}

func (_c *MockSectionDeclarationRevisionPort_ListSectionDeclarations_Call) Return(_a0 []revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], _a1 error) *MockSectionDeclarationRevisionPort_ListSectionDeclarations_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSectionDeclarationRevisionPort_ListSectionDeclarations_Call) RunAndReturn(run func(context.Context, ListSectionDeclarationsPortArgs) ([]revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error)) *MockSectionDeclarationRevisionPort_ListSectionDeclarations_Call {
	_c.Call.Return(run)
	return _c
}

// ResetSectionDeclaration provides a mock function with given fields: ctx, args
func (_m *MockSectionDeclarationRevisionPort) ResetSectionDeclaration(ctx context.Context, args ResetSectionDeclarationPortArgs) error {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for ResetSectionDeclaration")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, ResetSectionDeclarationPortArgs) error); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockSectionDeclarationRevisionPort_ResetSectionDeclaration_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ResetSectionDeclaration'
type MockSectionDeclarationRevisionPort_ResetSectionDeclaration_Call struct {
	*mock.Call
}

// ResetSectionDeclaration is a helper method to define mock.On call
//   - ctx context.Context
//   - args ResetSectionDeclarationPortArgs
func (_e *MockSectionDeclarationRevisionPort_Expecter) ResetSectionDeclaration(ctx interface{}, args interface{}) *MockSectionDeclarationRevisionPort_ResetSectionDeclaration_Call {
	return &MockSectionDeclarationRevisionPort_ResetSectionDeclaration_Call{Call: _e.mock.On("ResetSectionDeclaration", ctx, args)}
}

func (_c *MockSectionDeclarationRevisionPort_ResetSectionDeclaration_Call) Run(run func(ctx context.Context, args ResetSectionDeclarationPortArgs)) *MockSectionDeclarationRevisionPort_ResetSectionDeclaration_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ResetSectionDeclarationPortArgs))
	})
	return _c
}

func (_c *MockSectionDeclarationRevisionPort_ResetSectionDeclaration_Call) Return(_a0 error) *MockSectionDeclarationRevisionPort_ResetSectionDeclaration_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockSectionDeclarationRevisionPort_ResetSectionDeclaration_Call) RunAndReturn(run func(context.Context, ResetSectionDeclarationPortArgs) error) *MockSectionDeclarationRevisionPort_ResetSectionDeclaration_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateSectionDeclaration provides a mock function with given fields: ctx, args
func (_m *MockSectionDeclarationRevisionPort) UpdateSectionDeclaration(ctx context.Context, args UpdateSectionDeclarationPortArgs) (revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for UpdateSectionDeclaration")
	}

	var r0 revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, UpdateSectionDeclarationPortArgs) (revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, UpdateSectionDeclarationPortArgs) revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration]); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration])
	}

	if rf, ok := ret.Get(1).(func(context.Context, UpdateSectionDeclarationPortArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockSectionDeclarationRevisionPort_UpdateSectionDeclaration_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateSectionDeclaration'
type MockSectionDeclarationRevisionPort_UpdateSectionDeclaration_Call struct {
	*mock.Call
}

// UpdateSectionDeclaration is a helper method to define mock.On call
//   - ctx context.Context
//   - args UpdateSectionDeclarationPortArgs
func (_e *MockSectionDeclarationRevisionPort_Expecter) UpdateSectionDeclaration(ctx interface{}, args interface{}) *MockSectionDeclarationRevisionPort_UpdateSectionDeclaration_Call {
	return &MockSectionDeclarationRevisionPort_UpdateSectionDeclaration_Call{Call: _e.mock.On("UpdateSectionDeclaration", ctx, args)}
}

func (_c *MockSectionDeclarationRevisionPort_UpdateSectionDeclaration_Call) Run(run func(ctx context.Context, args UpdateSectionDeclarationPortArgs)) *MockSectionDeclarationRevisionPort_UpdateSectionDeclaration_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(UpdateSectionDeclarationPortArgs))
	})
	return _c
}

func (_c *MockSectionDeclarationRevisionPort_UpdateSectionDeclaration_Call) Return(_a0 revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], _a1 error) *MockSectionDeclarationRevisionPort_UpdateSectionDeclaration_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockSectionDeclarationRevisionPort_UpdateSectionDeclaration_Call) RunAndReturn(run func(context.Context, UpdateSectionDeclarationPortArgs) (revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error)) *MockSectionDeclarationRevisionPort_UpdateSectionDeclaration_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockSectionDeclarationRevisionPort creates a new instance of MockSectionDeclarationRevisionPort. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockSectionDeclarationRevisionPort(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockSectionDeclarationRevisionPort {
	mock := &MockSectionDeclarationRevisionPort{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
