// Code generated by mockery v2.52.3. DO NOT EDIT.

package emailapplication

import (
	context "context"

	revision "github.com/AfterShip/pltf-nf-infra/pkg/revision"
	mock "github.com/stretchr/testify/mock"

	sectiondeclaration "github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"

	version "github.com/AfterShip/pltf-nf-infra/pkg/version"
)

// MockUseCaseSectionDeclarationManager is an autogenerated mock type for the ucVersionManagedSectionDeclaration type
type MockUseCaseSectionDeclarationManager struct {
	mock.Mock
}

type MockUseCaseSectionDeclarationManager_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUseCaseSectionDeclarationManager) EXPECT() *MockUseCaseSectionDeclarationManager_Expecter {
	return &MockUseCaseSectionDeclarationManager_Expecter{mock: &_m.<PERSON>}
}

// CreateSectionDeclaration provides a mock function with given fields: ctx, args
func (_m *MockUseCaseSectionDeclarationManager) CreateSectionDeclaration(ctx context.Context, args CreateSectionDeclarationVersionManagedArgs) (revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CreateSectionDeclaration")
	}

	var r0 revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, CreateSectionDeclarationVersionManagedArgs) (revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, CreateSectionDeclarationVersionManagedArgs) revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration]); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration])
	}

	if rf, ok := ret.Get(1).(func(context.Context, CreateSectionDeclarationVersionManagedArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUseCaseSectionDeclarationManager_CreateSectionDeclaration_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateSectionDeclaration'
type MockUseCaseSectionDeclarationManager_CreateSectionDeclaration_Call struct {
	*mock.Call
}

// CreateSectionDeclaration is a helper method to define mock.On call
//   - ctx context.Context
//   - args CreateSectionDeclarationVersionManagedArgs
func (_e *MockUseCaseSectionDeclarationManager_Expecter) CreateSectionDeclaration(ctx interface{}, args interface{}) *MockUseCaseSectionDeclarationManager_CreateSectionDeclaration_Call {
	return &MockUseCaseSectionDeclarationManager_CreateSectionDeclaration_Call{Call: _e.mock.On("CreateSectionDeclaration", ctx, args)}
}

func (_c *MockUseCaseSectionDeclarationManager_CreateSectionDeclaration_Call) Run(run func(ctx context.Context, args CreateSectionDeclarationVersionManagedArgs)) *MockUseCaseSectionDeclarationManager_CreateSectionDeclaration_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(CreateSectionDeclarationVersionManagedArgs))
	})
	return _c
}

func (_c *MockUseCaseSectionDeclarationManager_CreateSectionDeclaration_Call) Return(_a0 revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], _a1 error) *MockUseCaseSectionDeclarationManager_CreateSectionDeclaration_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUseCaseSectionDeclarationManager_CreateSectionDeclaration_Call) RunAndReturn(run func(context.Context, CreateSectionDeclarationVersionManagedArgs) (revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error)) *MockUseCaseSectionDeclarationManager_CreateSectionDeclaration_Call {
	_c.Call.Return(run)
	return _c
}

// GetSectionDeclaration provides a mock function with given fields: ctx, args
func (_m *MockUseCaseSectionDeclarationManager) GetSectionDeclaration(ctx context.Context, args GetSectionDeclarationArgs) (revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetSectionDeclaration")
	}

	var r0 revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, GetSectionDeclarationArgs) (revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, GetSectionDeclarationArgs) revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration]); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration])
	}

	if rf, ok := ret.Get(1).(func(context.Context, GetSectionDeclarationArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUseCaseSectionDeclarationManager_GetSectionDeclaration_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSectionDeclaration'
type MockUseCaseSectionDeclarationManager_GetSectionDeclaration_Call struct {
	*mock.Call
}

// GetSectionDeclaration is a helper method to define mock.On call
//   - ctx context.Context
//   - args GetSectionDeclarationArgs
func (_e *MockUseCaseSectionDeclarationManager_Expecter) GetSectionDeclaration(ctx interface{}, args interface{}) *MockUseCaseSectionDeclarationManager_GetSectionDeclaration_Call {
	return &MockUseCaseSectionDeclarationManager_GetSectionDeclaration_Call{Call: _e.mock.On("GetSectionDeclaration", ctx, args)}
}

func (_c *MockUseCaseSectionDeclarationManager_GetSectionDeclaration_Call) Run(run func(ctx context.Context, args GetSectionDeclarationArgs)) *MockUseCaseSectionDeclarationManager_GetSectionDeclaration_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(GetSectionDeclarationArgs))
	})
	return _c
}

func (_c *MockUseCaseSectionDeclarationManager_GetSectionDeclaration_Call) Return(_a0 revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], _a1 error) *MockUseCaseSectionDeclarationManager_GetSectionDeclaration_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUseCaseSectionDeclarationManager_GetSectionDeclaration_Call) RunAndReturn(run func(context.Context, GetSectionDeclarationArgs) (revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error)) *MockUseCaseSectionDeclarationManager_GetSectionDeclaration_Call {
	_c.Call.Return(run)
	return _c
}

// ListSectionDeclarations provides a mock function with given fields: ctx, args
func (_m *MockUseCaseSectionDeclarationManager) ListSectionDeclarations(ctx context.Context, args ListSectionDeclarationsArgs) ([]revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for ListSectionDeclarations")
	}

	var r0 []revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ListSectionDeclarationsArgs) ([]revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ListSectionDeclarationsArgs) []revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration]); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ListSectionDeclarationsArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUseCaseSectionDeclarationManager_ListSectionDeclarations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListSectionDeclarations'
type MockUseCaseSectionDeclarationManager_ListSectionDeclarations_Call struct {
	*mock.Call
}

// ListSectionDeclarations is a helper method to define mock.On call
//   - ctx context.Context
//   - args ListSectionDeclarationsArgs
func (_e *MockUseCaseSectionDeclarationManager_Expecter) ListSectionDeclarations(ctx interface{}, args interface{}) *MockUseCaseSectionDeclarationManager_ListSectionDeclarations_Call {
	return &MockUseCaseSectionDeclarationManager_ListSectionDeclarations_Call{Call: _e.mock.On("ListSectionDeclarations", ctx, args)}
}

func (_c *MockUseCaseSectionDeclarationManager_ListSectionDeclarations_Call) Run(run func(ctx context.Context, args ListSectionDeclarationsArgs)) *MockUseCaseSectionDeclarationManager_ListSectionDeclarations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ListSectionDeclarationsArgs))
	})
	return _c
}

func (_c *MockUseCaseSectionDeclarationManager_ListSectionDeclarations_Call) Return(_a0 []revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], _a1 error) *MockUseCaseSectionDeclarationManager_ListSectionDeclarations_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUseCaseSectionDeclarationManager_ListSectionDeclarations_Call) RunAndReturn(run func(context.Context, ListSectionDeclarationsArgs) ([]revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error)) *MockUseCaseSectionDeclarationManager_ListSectionDeclarations_Call {
	_c.Call.Return(run)
	return _c
}

// Release provides a mock function with given fields: ctx, args
func (_m *MockUseCaseSectionDeclarationManager) Release(ctx context.Context, args version.ReleaseArgs[sectiondeclaration.EmailSectionDeclarationValue]) error {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for Release")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, version.ReleaseArgs[sectiondeclaration.EmailSectionDeclarationValue]) error); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUseCaseSectionDeclarationManager_Release_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Release'
type MockUseCaseSectionDeclarationManager_Release_Call struct {
	*mock.Call
}

// Release is a helper method to define mock.On call
//   - ctx context.Context
//   - args version.ReleaseArgs[sectiondeclaration.EmailSectionDeclarationValue]
func (_e *MockUseCaseSectionDeclarationManager_Expecter) Release(ctx interface{}, args interface{}) *MockUseCaseSectionDeclarationManager_Release_Call {
	return &MockUseCaseSectionDeclarationManager_Release_Call{Call: _e.mock.On("Release", ctx, args)}
}

func (_c *MockUseCaseSectionDeclarationManager_Release_Call) Run(run func(ctx context.Context, args version.ReleaseArgs[sectiondeclaration.EmailSectionDeclarationValue])) *MockUseCaseSectionDeclarationManager_Release_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(version.ReleaseArgs[sectiondeclaration.EmailSectionDeclarationValue]))
	})
	return _c
}

func (_c *MockUseCaseSectionDeclarationManager_Release_Call) Return(_a0 error) *MockUseCaseSectionDeclarationManager_Release_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUseCaseSectionDeclarationManager_Release_Call) RunAndReturn(run func(context.Context, version.ReleaseArgs[sectiondeclaration.EmailSectionDeclarationValue]) error) *MockUseCaseSectionDeclarationManager_Release_Call {
	_c.Call.Return(run)
	return _c
}

// ResetSectionDeclaration provides a mock function with given fields: ctx, args
func (_m *MockUseCaseSectionDeclarationManager) ResetSectionDeclaration(ctx context.Context, args ResetSectionDeclarationArgs) error {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for ResetSectionDeclaration")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, ResetSectionDeclarationArgs) error); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUseCaseSectionDeclarationManager_ResetSectionDeclaration_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ResetSectionDeclaration'
type MockUseCaseSectionDeclarationManager_ResetSectionDeclaration_Call struct {
	*mock.Call
}

// ResetSectionDeclaration is a helper method to define mock.On call
//   - ctx context.Context
//   - args ResetSectionDeclarationArgs
func (_e *MockUseCaseSectionDeclarationManager_Expecter) ResetSectionDeclaration(ctx interface{}, args interface{}) *MockUseCaseSectionDeclarationManager_ResetSectionDeclaration_Call {
	return &MockUseCaseSectionDeclarationManager_ResetSectionDeclaration_Call{Call: _e.mock.On("ResetSectionDeclaration", ctx, args)}
}

func (_c *MockUseCaseSectionDeclarationManager_ResetSectionDeclaration_Call) Run(run func(ctx context.Context, args ResetSectionDeclarationArgs)) *MockUseCaseSectionDeclarationManager_ResetSectionDeclaration_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ResetSectionDeclarationArgs))
	})
	return _c
}

func (_c *MockUseCaseSectionDeclarationManager_ResetSectionDeclaration_Call) Return(_a0 error) *MockUseCaseSectionDeclarationManager_ResetSectionDeclaration_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUseCaseSectionDeclarationManager_ResetSectionDeclaration_Call) RunAndReturn(run func(context.Context, ResetSectionDeclarationArgs) error) *MockUseCaseSectionDeclarationManager_ResetSectionDeclaration_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateSectionDeclaration provides a mock function with given fields: ctx, args
func (_m *MockUseCaseSectionDeclarationManager) UpdateSectionDeclaration(ctx context.Context, args UpdateSectionDeclarationArgs) (revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for UpdateSectionDeclaration")
	}

	var r0 revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, UpdateSectionDeclarationArgs) (revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, UpdateSectionDeclarationArgs) revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration]); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration])
	}

	if rf, ok := ret.Get(1).(func(context.Context, UpdateSectionDeclarationArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUseCaseSectionDeclarationManager_UpdateSectionDeclaration_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateSectionDeclaration'
type MockUseCaseSectionDeclarationManager_UpdateSectionDeclaration_Call struct {
	*mock.Call
}

// UpdateSectionDeclaration is a helper method to define mock.On call
//   - ctx context.Context
//   - args UpdateSectionDeclarationArgs
func (_e *MockUseCaseSectionDeclarationManager_Expecter) UpdateSectionDeclaration(ctx interface{}, args interface{}) *MockUseCaseSectionDeclarationManager_UpdateSectionDeclaration_Call {
	return &MockUseCaseSectionDeclarationManager_UpdateSectionDeclaration_Call{Call: _e.mock.On("UpdateSectionDeclaration", ctx, args)}
}

func (_c *MockUseCaseSectionDeclarationManager_UpdateSectionDeclaration_Call) Run(run func(ctx context.Context, args UpdateSectionDeclarationArgs)) *MockUseCaseSectionDeclarationManager_UpdateSectionDeclaration_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(UpdateSectionDeclarationArgs))
	})
	return _c
}

func (_c *MockUseCaseSectionDeclarationManager_UpdateSectionDeclaration_Call) Return(_a0 revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], _a1 error) *MockUseCaseSectionDeclarationManager_UpdateSectionDeclaration_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUseCaseSectionDeclarationManager_UpdateSectionDeclaration_Call) RunAndReturn(run func(context.Context, UpdateSectionDeclarationArgs) (revision.VersionManagedExtension[sectiondeclaration.EmailSectionDeclaration], error)) *MockUseCaseSectionDeclarationManager_UpdateSectionDeclaration_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockUseCaseSectionDeclarationManager creates a new instance of MockUseCaseSectionDeclarationManager. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUseCaseSectionDeclarationManager(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUseCaseSectionDeclarationManager {
	mock := &MockUseCaseSectionDeclarationManager{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
