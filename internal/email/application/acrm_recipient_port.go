package emailapplication

import (
	"context"
	"time"

	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
)

// ACRMRecipientProfileProvider provides access to ACRM recipient profile information.
// This interface abstracts the external contact management system and focuses on domain concepts.
// The implementation handles the conversion between domain RecipientProfile and external contact data.
type ACRMRecipientProfileProvider interface {
	// GetRecipientProfileByID retrieves a recipient profile by its unique identifier.
	// The RecipientID represents the recipient's identifier in the external system, but the interface
	// abstracts this detail and returns domain-specific RecipientProfile.
	GetRecipientProfileByID(
		ctx context.Context,
		params GetRecipientProfileByIDParams,
	) (recipientprofile.RecipientProfile, error)

	// GetRecipientProfileByEmail retrieves a recipient profile by email address.
	// This method searches for the recipient using their email and returns the complete profile
	// including subscription preferences and notification settings.
	GetRecipientProfileByEmail(
		ctx context.Context,
		params GetRecipientProfileByEmailParams,
	) (recipientprofile.RecipientProfile, error)

	// UpdateRecipientSubscriptionStatus updates the subscription status of a recipient.
	// The timestamp is used for concurrency control to ensure updates are applied in the correct order.
	// If the provided timestamp is earlier than the existing one, the update will be rejected.
	UpdateRecipientSubscriptionStatus(ctx context.Context, params UpdateRecipientSubscriptionStatusParams) error
}

type GetRecipientProfileByIDParams struct {
	RecipientID    string `json:"recipient_id"`
	OrganizationID string `json:"organization_id"`
	AppPlatform    string `json:"app_platform"`
	AppKey         string `json:"app_key"`
}

type GetRecipientProfileByEmailParams struct {
	Email          string `json:"email"`
	OrganizationID string `json:"organization_id"`
	AppPlatform    string `json:"app_platform"`
	AppKey         string `json:"app_key"`
}

type UpdateRecipientSubscriptionStatusParams struct {
	RecipientID        string    `json:"recipient_id"`
	OrganizationID     string    `json:"organization_id"`
	AppPlatform        string    `json:"app_platform"`
	AppKey             string    `json:"app_key"`
	SubscriptionStatus string    `json:"subscription_status"`
	ChangedAt          time.Time `json:"changed_at"`
}
