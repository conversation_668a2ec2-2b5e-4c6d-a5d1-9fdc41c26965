package emailapplication

import (
	"go.uber.org/fx"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-nf-infra/pkg/version"
	"github.com/AfterShip/pltf-nf-message/internal/commoncontent/sharedkernel"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/message"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
	sectiondeclarationdomain "github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/template"
)

type Params struct {
	fx.In

	MergeTagDeclarationRepository     sharedkernel.MergeTagDeclarationRepository
	TemplateGroupRepository           template.TemplateGroupRepository
	RenderSettingRepository           sharedkernel.RenderSettingRepository
	TemplateVariantRepository         template.TemplateVariantRepository
	EmailSectionDeclarationRepository sectiondeclarationdomain.EmailSectionDeclarationRepository
	TemplateDomainService             template.DomainService
	MessageDomainService              message.DomainService
	OrganizationSenderGetter          message.OrganizationSenderGetter
	DeliveryPort                      message.SendAgentPort
	RecipientProfileRepository        recipientprofile.RecipientProfileRepository
	RecipientProfileDomainService     recipientprofile.DomainService
	SubscriptionComplianceRuleRepo    recipientprofile.SubscriptionComplianceRuleRepository
	EmailMessageRepository            message.EmailMessageRepository

	// Section Declaration dependencies
	SectionDeclarationRevisionPort SectionDeclarationRevisionPort
	SectionDeclarationVersionAgent version.VersionAgent[sectiondeclarationdomain.EmailSectionDeclarationValue]
}

type Result struct {
	fx.Out

	ApplicationService *ApplicationService
}

func New(p Params) Result {
	return Result{
		ApplicationService: NewApplicationService(ApplicationServiceDependencies{
			UseCaseRenderAndSendEmail: NewUseCaseEmailDelivery(UseCaseEmailDeliveryDependencies{
				MergeTagDeclarationRepository:     p.MergeTagDeclarationRepository,
				TemplateGroupRepository:           p.TemplateGroupRepository,
				RenderSettingRepository:           p.RenderSettingRepository,
				TemplateVariantRepository:         p.TemplateVariantRepository,
				EmailSectionDeclarationRepository: p.EmailSectionDeclarationRepository,
				TemplateDomainService:             p.TemplateDomainService,
				MessageDomainService:              p.MessageDomainService,
				OrganizationSenderGetter:          p.OrganizationSenderGetter,
				DeliveryPort:                      p.DeliveryPort,
				RecipientProfileRepository:        p.RecipientProfileRepository,
				SubscriptionComplianceRuleRepo:    p.SubscriptionComplianceRuleRepo,
				RecipientProfileDomainService:     p.RecipientProfileDomainService,
				EmailMessageRepository:            p.EmailMessageRepository,
			}),
			SectionDeclarationVersionManaged: NewVersionManagedSectionDeclaration(
				p.SectionDeclarationRevisionPort,
				p.SectionDeclarationVersionAgent,
			),
			UseCaseSubscriptionManagement: NewUseCaseSubscriptionManagement(UseCaseSubscriptionManagementDependencies{
				RecipientProfileRepository:    p.RecipientProfileRepository,
				RecipientProfileDomainService: p.RecipientProfileDomainService,
				SubscriptionSynchronizer: NewSubscriptionSynchronizer(
					newEmptyACRMRecipientProfileProvider(),
					p.RecipientProfileRepository,
				),
			}),
			UseCaseSubscriptionComplianceRuleManagement: NewUseCaseSubscriptionComplianceRuleManagement(
				p.SubscriptionComplianceRuleRepo,
			),
		}),
	}
}

const moduleName = "email_application"

var Module = fx.Module(moduleName,
	fx.Decorate(func(logger *log.Logger) *log.Logger { return logger.Named(moduleName) }),
	fx.Provide(New),
	fx.Provide(NewVersionManagedSectionDeclaration),
)
