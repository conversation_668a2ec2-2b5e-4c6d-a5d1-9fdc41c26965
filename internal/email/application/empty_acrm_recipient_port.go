package emailapplication

import (
	"context"

	"github.com/AfterShip/library-heytea-go-common/xerrors"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
)

// emptyACRMRecipientProfileProvider is a mock implementation of ACRMRecipientProfileProvider that always returns empty data.
type emptyACRMRecipientProfileProvider struct{}

func newEmptyACRMRecipientProfileProvider() ACRMRecipientProfileProvider {
	return &emptyACRMRecipientProfileProvider{}
}

func (p *emptyACRMRecipientProfileProvider) GetRecipientProfileByID(
	ctx context.Context,
	params GetRecipientProfileByIDParams,
) (recipientprofile.RecipientProfile, error) {
	return recipientprofile.RecipientProfile{}, xerrors.ErrBusinessRecordNotFound
}

func (p *emptyACRMRecipientProfileProvider) GetRecipientProfileByEmail(
	ctx context.Context,
	params GetRecipientProfileByEmailParams,
) (recipientprofile.RecipientProfile, error) {
	return recipientprofile.RecipientProfile{}, xerrors.ErrBusinessRecordNotFound
}

func (p *emptyACRMRecipientProfileProvider) UpdateRecipientSubscriptionStatus(
	ctx context.Context,
	params UpdateRecipientSubscriptionStatusParams,
) error {
	return nil
}
