package emailapplication

type ApplicationService struct {
	UseCaseRenderAndSendEmail
	// SectionDeclarationVersionManaged provides methods to manage email section declarations.
	SectionDeclarationVersionManaged ucVersionManagedSectionDeclaration
	UseCaseSubscriptionManagement
	UseCaseSubscriptionComplianceRuleManagement
}

type ApplicationServiceDependencies struct {
	UseCaseRenderAndSendEmail
	SectionDeclarationVersionManaged ucVersionManagedSectionDeclaration
	UseCaseSubscriptionManagement
	UseCaseSubscriptionComplianceRuleManagement
}

func NewApplicationService(d ApplicationServiceDependencies) *ApplicationService {
	return &ApplicationService{
		UseCaseRenderAndSendEmail:                   d.UseCaseRenderAndSendEmail,
		SectionDeclarationVersionManaged:            d.SectionDeclarationVersionManaged,
		UseCaseSubscriptionManagement:               d.UseCaseSubscriptionManagement,
		UseCaseSubscriptionComplianceRuleManagement: d.UseCaseSubscriptionComplianceRuleManagement,
	}
}
