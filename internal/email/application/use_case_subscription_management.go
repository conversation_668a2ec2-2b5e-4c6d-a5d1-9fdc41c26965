package emailapplication

import (
	"context"
	"errors"
	"time"

	"github.com/AfterShip/library-heytea-go-common/xerrors"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
)

type UseCaseSubscriptionManagement interface {
	// GlobalUnsubscribeByToken unsubscribes a recipient email from all notifications.
	// If the recipient is not found, a new recipient profile will be created.
	GlobalUnsubscribeByToken(ctx context.Context, params GlobalUnsubscribeByTokenParams) error

	// CancelGlobalUnsubscribed will remove the global unsubscribed status. And the preferences status will be restored to the previous status.
	CancelGlobalUnsubscribed(ctx context.Context, params CancelGlobalUnsubscribedParams) error

	// UpdatePreferenceStatus updates the preference status of a recipient.
	// In the early stages, this method is only used for internal testing.
	UpdatePreferenceStatus(ctx context.Context, params UpdatePreferenceStatusParams) error
}

type GlobalUnsubscribeByTokenParams struct {
	// Token is the encrypted info of the unsubscribe request.
	Token string `json:"token"`

	// Version is the version of encryption method.
	Version string `json:"version"`

	UnsubscribedAt time.Time `json:"unsubscribed_at"`
}

type CancelGlobalUnsubscribedParams struct {
	// Token is the encrypted info of the cancel unsubscribe request.
	Token string `json:"token"`

	// Version is the version of encryption method.
	Version string `json:"version"`

	CancelledAt time.Time `json:"canceled_at"`
}

type UpdatePreferenceStatusParams struct {
	OrganizationID     string    `json:"organization_id"`
	Email              string    `json:"email"`
	AppPlatform        string    `json:"app_platform"`
	AppKey             string    `json:"app_key"`
	HostProductCode    string    `json:"host_product_code"`
	BusinessScenario   string    `json:"business_scenario"`
	SubscriptionStatus string    `json:"subscription_status"`
	EffectiveAt        time.Time `json:"effective_at"`
	UpdateTrigger      string    `json:"update_trigger"`
}

type useCaseSubscriptionManagement struct {
	recipientProfileRepository    recipientprofile.RecipientProfileRepository
	recipientProfileDomainService recipientprofile.DomainService
	subscriptionSynchronizer      SubscriptionSynchronizer
}

type UseCaseSubscriptionManagementDependencies struct {
	RecipientProfileRepository    recipientprofile.RecipientProfileRepository
	RecipientProfileDomainService recipientprofile.DomainService
	SubscriptionSynchronizer      SubscriptionSynchronizer
}

func NewUseCaseSubscriptionManagement(d UseCaseSubscriptionManagementDependencies) UseCaseSubscriptionManagement {
	return &useCaseSubscriptionManagement{
		recipientProfileRepository:    d.RecipientProfileRepository,
		recipientProfileDomainService: d.RecipientProfileDomainService,
		subscriptionSynchronizer:      d.SubscriptionSynchronizer,
	}
}

func (uc *useCaseSubscriptionManagement) GlobalUnsubscribeByToken(
	ctx context.Context,
	params GlobalUnsubscribeByTokenParams,
) error {
	subscriptionInfo, err := uc.recipientProfileDomainService.DecryptToken(ctx, recipientprofile.DecryptTokenParams{
		Token:   params.Token,
		Version: params.Version,
	})
	if err != nil {
		return err
	}

	var (
		isChanged bool
		recipient recipientprofile.RecipientProfile
	)

	err = uc.recipientProfileRepository.RunInTransaction(ctx, func(ctx context.Context) error {
		recipient, err = uc.recipientProfileRepository.Find(ctx, recipientprofile.FindParams{
			Email:          subscriptionInfo.Email,
			OrganizationID: subscriptionInfo.OrganizationID,
			AppPlatform:    subscriptionInfo.AppPlatform,
			AppKey:         subscriptionInfo.AppKey,
		})
		if err != nil {
			if errors.Is(err, xerrors.ErrBusinessRecordNotFound) {
				newRecipient := recipientprofile.RecipientProfile{
					ID:             uc.recipientProfileRepository.NextID(ctx),
					OrganizationID: subscriptionInfo.OrganizationID,
					AppPlatform:    subscriptionInfo.AppPlatform,
					AppKey:         subscriptionInfo.AppKey,
					Email:          subscriptionInfo.Email,
				}
				isChanged = newRecipient.Unsubscribe(params.UnsubscribedAt, recipientprofile.UpdateTriggerUserAction)
				return uc.recipientProfileRepository.Create(ctx, &newRecipient)
			}
			return err
		}
		isChanged = recipient.Unsubscribe(params.UnsubscribedAt, recipientprofile.UpdateTriggerUserAction)
		if !isChanged {
			return nil
		}
		return uc.recipientProfileRepository.Update(ctx, &recipient)
	})

	if err != nil {
		return xerrors.Wrap(err)
	}

	if isChanged {
		// TODO: should refractor to outbox pattern to ensure consistency
		_ = uc.subscriptionSynchronizer.SynchronizeToContact(ctx, SynchronizeToContactParams{
			OrganizationID:     subscriptionInfo.OrganizationID,
			AppPlatform:        subscriptionInfo.AppPlatform,
			AppKey:             subscriptionInfo.AppKey,
			Email:              subscriptionInfo.Email,
			SubscriptionStatus: string(recipientprofile.SubscriptionStatusUnsubscribed),
			EffectiveAt:        params.UnsubscribedAt,
		})
	}

	return nil
}

func (uc *useCaseSubscriptionManagement) CancelGlobalUnsubscribed(
	ctx context.Context,
	params CancelGlobalUnsubscribedParams,
) error {
	subscriptionInfo, err := uc.recipientProfileDomainService.DecryptToken(ctx, recipientprofile.DecryptTokenParams{
		Token:   params.Token,
		Version: params.Version,
	})
	if err != nil {
		return err
	}
	var (
		isChanged bool
		recipient recipientprofile.RecipientProfile
	)

	err = uc.recipientProfileRepository.RunInTransaction(ctx, func(ctx context.Context) error {
		recipient, err = uc.recipientProfileRepository.Find(ctx, recipientprofile.FindParams{
			Email:          subscriptionInfo.Email,
			OrganizationID: subscriptionInfo.OrganizationID,
			AppPlatform:    subscriptionInfo.AppPlatform,
			AppKey:         subscriptionInfo.AppKey,
		})
		if err != nil {
			// In this case, we won't create a new recipient profile if the recipient is not found.
			// We don't think this is a valid operation if the user is not unsubscribed.
			return err
		}
		isChanged = recipient.CancelGlobalUnsubscribed(params.CancelledAt, recipientprofile.UpdateTriggerUserAction)
		if !isChanged {
			return nil
		}
		return uc.recipientProfileRepository.Update(ctx, &recipient)
	})

	if err != nil {
		return err
	}

	return nil
}

func (uc *useCaseSubscriptionManagement) UpdatePreferenceStatus(
	ctx context.Context,
	params UpdatePreferenceStatusParams,
) error {
	var isChanged bool
	err := uc.recipientProfileRepository.RunInTransaction(ctx, func(ctx context.Context) error {
		recipient, err := uc.recipientProfileRepository.Find(ctx, recipientprofile.FindParams{
			Email:          params.Email,
			OrganizationID: params.OrganizationID,
			AppPlatform:    params.AppPlatform,
			AppKey:         params.AppKey,
		})
		if err != nil {
			if errors.Is(err, xerrors.ErrBusinessRecordNotFound) {
				recipient = recipientprofile.RecipientProfile{
					ID:             uc.recipientProfileRepository.NextID(ctx),
					OrganizationID: params.OrganizationID,
					AppPlatform:    params.AppPlatform,
					AppKey:         params.AppKey,
					Email:          params.Email,
				}
				recipient.UpdatePreferenceStatus(
					params.HostProductCode,
					params.BusinessScenario,
					recipientprofile.SubscriptionStatus(params.SubscriptionStatus),
					params.EffectiveAt,
					recipientprofile.UpdateTriggerUserAction,
				)
				isChanged = true
				return uc.recipientProfileRepository.Create(ctx, &recipient)
			}
			return err
		}
		isChanged = recipient.UpdatePreferenceStatus(
			params.HostProductCode,
			params.BusinessScenario,
			recipientprofile.SubscriptionStatus(params.SubscriptionStatus),
			params.EffectiveAt,
			recipientprofile.UpdateTriggerUserAction,
		)
		if !isChanged {
			return nil
		}
		return uc.recipientProfileRepository.Update(ctx, &recipient)
	})
	if err != nil {
		return err
	}

	// TODO: when preference management is opened to user, we should synchronize to ACRM if AM preference is changed

	return nil
}
