package emailapplication

import (
	"context"
	"time"
)

type UseCaseSubscriptionReconciliation interface {
	// ReconcileSubscription reconciles subscription status between contact and recipient profile.
	// It will compare the data updated in the given time range.
	// It is expected to be run as a scheduled job.
	ReconcileSubscription(ctx context.Context, params ReconcileSubscriptionParams) error
}

type ReconcileSubscriptionParams struct {
	// StartedAt is the start time of the time range.
	StartedAt time.Time `json:"started_at"`

	// EndedAt is the end time of the time range.
	EndedAt time.Time `json:"ended_at"`
}
