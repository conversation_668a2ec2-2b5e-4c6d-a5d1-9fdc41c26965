package emailapplication_test

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/pltf-nf-infra/pkg/version"
	emailapplication "github.com/AfterShip/pltf-nf-message/internal/email/application"
	sectiondeclarationdomain "github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"
)

func TestVersionManagedSectionDeclarationImpl_CreateSectionDeclaration(t *testing.T) {
	// Setup
	mockRevisionPort := emailapplication.NewMockSectionDeclarationRevisionPort(t)
	mockVersionAgent := &MockVersionAgent[sectiondeclarationdomain.EmailSectionDeclarationValue]{}

	uc := emailapplication.NewVersionManagedSectionDeclaration(mockRevisionPort, mockVersionAgent)

	// Test data
	inputSetting, _ := json.<PERSON>(map[string]interface{}{"test": "value"})
	templateSetting, _ := json.<PERSON>(map[string]interface{}{"template": "setting"})
	presentationSettings, _ := json.<PERSON>(map[string]interface{}{"presentation": "setting"})

	args := emailapplication.CreateSectionDeclarationVersionManagedArgs{
		HostProductCode: "test-product",
		Handle:          "test-handle",
		Type:            "image_and_text",
		Name:            "Test Section",
		Description:     "Test description",
		Category:        "basic",
		OrderingWeight:  100,
		IconURL:         "https://example.com/icon.png",
		ApplicableSetting: sectiondeclarationdomain.ApplicableSetting{
			MessageChannels: []string{"email"},
		},
		LimitationSetting: sectiondeclarationdomain.LimitationSetting{
			MaxCount: 5,
		},
		InputSetting:           inputSetting,
		SectionTemplateSetting: templateSetting,
		PresentationSettings:   presentationSettings,
		OperatorEmail:          "<EMAIL>",
	}

	expectedResult := emailapplication.VersionManagedEmailSectionDeclaration{
		Extension: sectiondeclarationdomain.EmailSectionDeclaration{
			ID:                     "test-id",
			HostProductCode:        args.HostProductCode,
			Handle:                 args.Handle,
			Type:                   args.Type,
			Name:                   args.Name,
			Description:            args.Description,
			Category:               args.Category,
			OrderingWeight:         args.OrderingWeight,
			IconURL:                args.IconURL,
			ApplicableSetting:      args.ApplicableSetting,
			LimitationSetting:      args.LimitationSetting,
			InputSetting:           args.InputSetting,
			SectionTemplateSetting: args.SectionTemplateSetting,
			PresentationSettings:   args.PresentationSettings,
			Hidden:                 false,
			CreatedAt:              time.Now(),
		},
	}

	// Mock expectations - need to match the actual SectionDeclaration that will be created
	mockRevisionPort.EXPECT().CreateSectionDeclaration(
		context.Background(),
		MatchCreateSectionDeclarationPortArgs(args),
	).Return(expectedResult, nil)

	// Execute
	result, err := uc.CreateSectionDeclaration(context.Background(), args)

	// Verify
	require.NoError(t, err)
	assert.Equal(t, expectedResult.Extension.ID, result.Extension.ID)
	assert.Equal(t, expectedResult.Extension.Name, result.Extension.Name)
	assert.Equal(t, expectedResult.Extension.Handle, result.Extension.Handle)
	assert.Equal(t, expectedResult.Extension.Type, result.Extension.Type)
}

func TestVersionManagedSectionDeclarationImpl_CreateSectionDeclaration_ValidationError(t *testing.T) {
	// Setup
	mockRevisionPort := emailapplication.NewMockSectionDeclarationRevisionPort(t)
	mockVersionAgent := &MockVersionAgent[sectiondeclarationdomain.EmailSectionDeclarationValue]{}

	uc := emailapplication.NewVersionManagedSectionDeclaration(mockRevisionPort, mockVersionAgent)

	// Test data with missing required fields
	args := emailapplication.CreateSectionDeclarationVersionManagedArgs{
		// Missing required fields to trigger validation error
		OperatorEmail: "invalid-email", // Invalid email format
	}

	// Execute
	_, err := uc.CreateSectionDeclaration(context.Background(), args)

	// Verify
	require.Error(t, err)
	assert.Contains(t, err.Error(), "invalid create section declaration arguments")
}

func TestVersionManagedSectionDeclarationImpl_UpdateSectionDeclaration(t *testing.T) {
	// Setup
	mockRevisionPort := emailapplication.NewMockSectionDeclarationRevisionPort(t)
	mockVersionAgent := &MockVersionAgent[sectiondeclarationdomain.EmailSectionDeclarationValue]{}

	uc := emailapplication.NewVersionManagedSectionDeclaration(mockRevisionPort, mockVersionAgent)

	// Test data
	sectionID := "test-section-id"
	hostProductCode := "test-product"

	existingSectionDeclaration := sectiondeclarationdomain.EmailSectionDeclaration{
		ID:              sectionID,
		HostProductCode: hostProductCode,
		Handle:          "test-handle",
		Type:            "image_and_text",
		Name:            "Old Name",
		Description:     "Old description",
		CreatedAt:       time.Now().Add(-time.Hour),
	}

	args := emailapplication.UpdateSectionDeclarationArgs{
		HostProductCode: hostProductCode,
		ID:              sectionID,
		Name:            "Updated Name",
		Description:     "Updated description",
		Category:        "updated",
		UpdatedBy:       "<EMAIL>",
	}

	// Mock expectations for getting existing section
	mockRevisionPort.EXPECT().GetSectionDeclaration(
		context.Background(),
		emailapplication.GetSectionDeclarationPortArgs{
			HostProductCode: hostProductCode,
			ID:              sectionID,
		},
	).Return(emailapplication.VersionManagedEmailSectionDeclaration{
		Extension: existingSectionDeclaration,
	}, nil)

	// Mock expectations for updating section - need to match the actual SectionDeclaration that will be updated
	mockRevisionPort.EXPECT().UpdateSectionDeclaration(
		context.Background(),
		MatchUpdateSectionDeclarationPortArgs(existingSectionDeclaration, args),
	).Return(emailapplication.VersionManagedEmailSectionDeclaration{
		Extension: sectiondeclarationdomain.EmailSectionDeclaration{
			ID:              sectionID,
			HostProductCode: hostProductCode,
			Handle:          "test-handle",
			Type:            "image_and_text",
			Name:            args.Name,
			Description:     args.Description,
			Category:        args.Category,
			CreatedAt:       existingSectionDeclaration.CreatedAt,
		},
	}, nil)

	// Execute
	result, err := uc.UpdateSectionDeclaration(context.Background(), args)

	// Verify
	require.NoError(t, err)
	assert.Equal(t, args.Name, result.Extension.Name)
	assert.Equal(t, args.Description, result.Extension.Description)
	assert.Equal(t, args.Category, result.Extension.Category)
}

// MockVersionAgent is a mock implementation for testing.
type MockVersionAgent[T any] struct{}

func (m *MockVersionAgent[T]) Release(ctx context.Context, args version.ReleaseArgs[T]) error {
	return nil
}

//
//nolint:gocognit // This is a test function, so it's ok to have a complex function.
func MatchCreateSectionDeclarationPortArgs(
	args emailapplication.CreateSectionDeclarationVersionManagedArgs,
) interface{} {
	return mock.MatchedBy(func(portArgs emailapplication.CreateSectionDeclarationPortArgs) bool {
		// Check basic fields
		if portArgs.HostProductCode != args.HostProductCode ||
			portArgs.CreatedBy != args.OperatorEmail {
			return false
		}

		// Check the SectionDeclaration domain entity fields
		sectionDeclaration := portArgs.SectionDeclaration
		if sectionDeclaration.HostProductCode != args.HostProductCode ||
			sectionDeclaration.Handle != args.Handle ||
			sectionDeclaration.Type != args.Type ||
			sectionDeclaration.Name != args.Name ||
			sectionDeclaration.Description != args.Description ||
			sectionDeclaration.Category != args.Category ||
			sectionDeclaration.OrderingWeight != args.OrderingWeight ||
			sectionDeclaration.IconURL != args.IconURL ||
			sectionDeclaration.Hidden != false {
			return false
		}

		// Check ApplicableSetting
		if len(sectionDeclaration.ApplicableSetting.MessageChannels) != len(args.ApplicableSetting.MessageChannels) {
			return false
		}
		for i, channel := range sectionDeclaration.ApplicableSetting.MessageChannels {
			if channel != args.ApplicableSetting.MessageChannels[i] {
				return false
			}
		}

		// Check LimitationSetting
		if sectionDeclaration.LimitationSetting.MaxCount != args.LimitationSetting.MaxCount {
			return false
		}

		// Check JSON fields (InputSetting, SectionTemplateSetting, PresentationSettings)
		if string(sectionDeclaration.InputSetting) != string(args.InputSetting) ||
			string(sectionDeclaration.SectionTemplateSetting) != string(args.SectionTemplateSetting) ||
			string(sectionDeclaration.PresentationSettings) != string(args.PresentationSettings) {
			return false
		}

		// Check that ID is generated (not empty) and CreatedAt is recent
		if sectionDeclaration.ID == "" {
			return false
		}

		// Allow some tolerance for CreatedAt (within the last minute)
		if time.Since(sectionDeclaration.CreatedAt) > time.Minute {
			return false
		}

		return true
	})
}

// MatchUpdateSectionDeclarationPortArgs creates a mock matcher for UpdateSectionDeclarationPortArgs.
func MatchUpdateSectionDeclarationPortArgs(
	existingSection sectiondeclarationdomain.EmailSectionDeclaration,
	args emailapplication.UpdateSectionDeclarationArgs,
) interface{} {
	return mock.MatchedBy(func(portArgs emailapplication.UpdateSectionDeclarationPortArgs) bool {
		// Check basic fields
		if portArgs.HostProductCode != existingSection.HostProductCode ||
			portArgs.UpdatedBy != args.UpdatedBy {
			return false
		}

		// Check the SectionDeclaration domain entity fields
		sectionDeclaration := portArgs.SectionDeclaration
		if sectionDeclaration.ID != existingSection.ID ||
			sectionDeclaration.HostProductCode != existingSection.HostProductCode ||
			sectionDeclaration.Handle != existingSection.Handle ||
			sectionDeclaration.Type != existingSection.Type ||
			sectionDeclaration.Name != args.Name ||
			sectionDeclaration.Description != args.Description ||
			sectionDeclaration.Category != args.Category {
			return false
		}

		// Check that CreatedAt is preserved and UpdatedAt is recent
		if !sectionDeclaration.CreatedAt.Equal(existingSection.CreatedAt) {
			return false
		}

		// Check that UpdatedAt is set and recent (within the last minute)
		if sectionDeclaration.UpdatedAt == nil || time.Since(*sectionDeclaration.UpdatedAt) > time.Minute {
			return false
		}

		return true
	})
}
