package emailapplication

import "context"

type UseCaseContactEventConsumption interface {
	// HandleContactEvent consumes contact events and synchronizes the subscription status to recipient profile.
	HandleContactEvent(ctx context.Context, params HandleContactEventParams) error
}

type HandleContactEventParams struct {
	ContactID string `json:"contact_id"`
}

type useCaseContactEventConsumption struct {
	subscriptionSynchronizer SubscriptionSynchronizer
}

func NewUseCaseContactEventConsumption(
	subscriptionSynchronizer SubscriptionSynchronizer,
) UseCaseContactEventConsumption {
	return &useCaseContactEventConsumption{
		subscriptionSynchronizer: subscriptionSynchronizer,
	}
}

func (uc *useCaseContactEventConsumption) HandleContactEvent(
	ctx context.Context,
	params HandleContactEventParams,
) error {
	return uc.subscriptionSynchronizer.SynchronizeFromContact(ctx, SynchronizeFromContactParams{
		ContactID: params.ContactID,
	})
}
