package emailapplication

import (
	"context"

	sectiondeclarationdomain "github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"
)

// SectionDeclarationRevisionPort defines the interface for managing email section declaration revisions
// This port abstracts the revision management functionality needed by the application layer
//
//go:generate mockery --name SectionDeclarationRevisionPort --with-expecter --inpackage --structname MockSectionDeclarationRevisionPort --filename section_declaration_revision_port.mock.gen.go
type SectionDeclarationRevisionPort interface {
	// CreateSectionDeclaration creates a new email section declaration revision
	CreateSectionDeclaration(
		ctx context.Context,
		args CreateSectionDeclarationPortArgs,
	) (VersionManagedEmailSectionDeclaration, error)

	// GetSectionDeclaration retrieves an email section declaration by ID
	GetSectionDeclaration(
		ctx context.Context,
		args GetSectionDeclarationPortArgs,
	) (VersionManagedEmailSectionDeclaration, error)

	// ListSectionDeclarations lists all working email section declarations for a host product
	ListSectionDeclarations(
		ctx context.Context,
		args ListSectionDeclarationsPortArgs,
	) ([]VersionManagedEmailSectionDeclaration, error)

	// UpdateSectionDeclaration updates an existing email section declaration
	UpdateSectionDeclaration(
		ctx context.Context,
		args UpdateSectionDeclarationPortArgs,
	) (VersionManagedEmailSectionDeclaration, error)

	// ResetSectionDeclaration resets an email section declaration to its previous released version
	ResetSectionDeclaration(ctx context.Context, args ResetSectionDeclarationPortArgs) error
}

// Port-specific argument types.
type CreateSectionDeclarationPortArgs struct {
	HostProductCode    string
	SectionDeclaration sectiondeclarationdomain.EmailSectionDeclaration
	CreatedBy          string
}

type GetSectionDeclarationPortArgs struct {
	HostProductCode string
	ID              string
}

type ListSectionDeclarationsPortArgs struct {
	HostProductCode string
}

type UpdateSectionDeclarationPortArgs struct {
	HostProductCode    string
	SectionDeclaration sectiondeclarationdomain.EmailSectionDeclaration
	UpdatedBy          string
}

type ResetSectionDeclarationPortArgs struct {
	HostProductCode string
	ID              string
	ResetBy         string
}
