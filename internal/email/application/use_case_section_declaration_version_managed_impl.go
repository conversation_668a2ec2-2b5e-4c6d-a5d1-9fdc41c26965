package emailapplication

import (
	"context"
	"fmt"
	"time"

	"github.com/AfterShip/library-heytea-go-common/utils/uuid"
	"github.com/AfterShip/library-heytea-go-common/validator"
	"github.com/AfterShip/library-heytea-go-common/xerrors"
	"github.com/AfterShip/pltf-nf-infra/pkg/version"
	sectiondeclarationdomain "github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"
)

// versionManagedSectionDeclarationImpl implements the ucVersionManagedSectionDeclaration interface.
type versionManagedSectionDeclarationImpl struct {
	revisionPort SectionDeclarationRevisionPort
	versionAgent version.VersionAgent[sectiondeclarationdomain.EmailSectionDeclarationValue]
}

// NewVersionManagedSectionDeclaration creates a new instance of the version managed email section declaration use case.
func NewVersionManagedSectionDeclaration(
	revisionPort SectionDeclarationRevisionPort,
	versionAgent version.VersionAgent[sectiondeclarationdomain.EmailSectionDeclarationValue],
) ucVersionManagedSectionDeclaration {
	return &versionManagedSectionDeclarationImpl{
		revisionPort: revisionPort,
		versionAgent: versionAgent,
	}
}

// GetSectionDeclaration retrieves a specific email section declaration by ID.
func (uc *versionManagedSectionDeclarationImpl) GetSectionDeclaration(
	ctx context.Context,
	args GetSectionDeclarationArgs,
) (VersionManagedEmailSectionDeclaration, error) {
	if err := validator.ValidateStruct(args); err != nil {
		return VersionManagedEmailSectionDeclaration{}, fmt.Errorf("invalid get section declaration arguments: %w", err)
	}

	// Use the RevisionPort to get the section declaration
	result, err := uc.revisionPort.GetSectionDeclaration(ctx, GetSectionDeclarationPortArgs(args))
	if err != nil {
		return VersionManagedEmailSectionDeclaration{}, xerrors.Wrap(err,
			xerrors.Field("sectionDeclarationID", args.ID))
	}

	return result, nil
}

// ListSectionDeclarations retrieves all email section declarations for a given host product code.
func (uc *versionManagedSectionDeclarationImpl) ListSectionDeclarations(
	ctx context.Context,
	args ListSectionDeclarationsArgs,
) ([]VersionManagedEmailSectionDeclaration, error) {
	if err := validator.ValidateStruct(args); err != nil {
		return nil, fmt.Errorf("invalid list section declarations arguments: %w", err)
	}

	// Use the RevisionPort to list section declarations
	result, err := uc.revisionPort.ListSectionDeclarations(ctx, ListSectionDeclarationsPortArgs(args))
	if err != nil {
		return nil, xerrors.Wrap(err,
			xerrors.Field("hostProductCode", args.HostProductCode))
	}

	return result, nil
}

// CreateSectionDeclaration creates a new email section declaration.
func (uc *versionManagedSectionDeclarationImpl) CreateSectionDeclaration(
	ctx context.Context,
	args CreateSectionDeclarationVersionManagedArgs,
) (VersionManagedEmailSectionDeclaration, error) {
	if err := validator.ValidateStruct(args); err != nil {
		return VersionManagedEmailSectionDeclaration{}, fmt.Errorf(
			"invalid create section declaration arguments: %w",
			err,
		)
	}

	// Create the email section declaration domain entity
	now := time.Now()
	sectionDeclaration := sectiondeclarationdomain.EmailSectionDeclaration{
		ID:                     uuid.GenerateUUIDV4(), // Generate a unique ID
		HostProductCode:        args.HostProductCode,
		Handle:                 args.Handle,
		Type:                   args.Type,
		Name:                   args.Name,
		Description:            args.Description,
		Category:               args.Category,
		OrderingWeight:         args.OrderingWeight,
		IconURL:                args.IconURL,
		ApplicableSetting:      args.ApplicableSetting,
		LimitationSetting:      args.LimitationSetting,
		InputSetting:           args.InputSetting,
		SectionTemplateSetting: args.SectionTemplateSetting,
		PresentationSettings:   args.PresentationSettings,
		Hidden:                 false,
		CreatedAt:              now,
	}

	// Validate the section declaration for creation
	if err := sectionDeclaration.ValidateForCreation(); err != nil {
		return VersionManagedEmailSectionDeclaration{}, fmt.Errorf("section declaration validation failed: %w", err)
	}

	// Use the RevisionPort to create the section declaration
	result, err := uc.revisionPort.CreateSectionDeclaration(ctx, CreateSectionDeclarationPortArgs{
		HostProductCode:    args.HostProductCode,
		SectionDeclaration: sectionDeclaration,
		CreatedBy:          args.OperatorEmail,
	})
	if err != nil {
		return VersionManagedEmailSectionDeclaration{}, xerrors.Wrap(err,
			xerrors.Field("handle", args.Handle),
			xerrors.Field("hostProductCode", args.HostProductCode))
	}

	return result, nil
}

// UpdateSectionDeclaration updates an existing email section declaration.
func (uc *versionManagedSectionDeclarationImpl) UpdateSectionDeclaration(
	ctx context.Context,
	args UpdateSectionDeclarationArgs,
) (VersionManagedEmailSectionDeclaration, error) {
	if err := validator.ValidateStruct(args); err != nil {
		return VersionManagedEmailSectionDeclaration{}, fmt.Errorf(
			"invalid update section declaration arguments: %w",
			err,
		)
	}

	// First, get the existing section declaration to retrieve the host product code
	existingResult, err := uc.revisionPort.GetSectionDeclaration(ctx, GetSectionDeclarationPortArgs{
		HostProductCode: args.HostProductCode,
		ID:              args.ID,
	})
	if err != nil {
		return VersionManagedEmailSectionDeclaration{}, xerrors.Wrap(err,
			xerrors.Field("sectionDeclarationID", args.ID))
	}

	// Create updated section declaration entity
	updatedSectionDeclaration := existingResult.Extension

	// Apply business logic for updates
	if err := updatedSectionDeclaration.UpdateName(args.Name); err != nil {
		return VersionManagedEmailSectionDeclaration{}, fmt.Errorf("failed to update section declaration name: %w", err)
	}

	if err := updatedSectionDeclaration.UpdateDescription(args.Description); err != nil {
		return VersionManagedEmailSectionDeclaration{}, fmt.Errorf(
			"failed to update section declaration description: %w",
			err,
		)
	}

	if err := updatedSectionDeclaration.UpdateCategory(args.Category); err != nil {
		return VersionManagedEmailSectionDeclaration{}, fmt.Errorf(
			"failed to update section declaration category: %w",
			err,
		)
	}

	if err := updatedSectionDeclaration.UpdateIconURL(args.IconURL); err != nil {
		return VersionManagedEmailSectionDeclaration{}, fmt.Errorf(
			"failed to update section declaration icon URL: %w",
			err,
		)
	}

	if err := updatedSectionDeclaration.UpdateApplicableSetting(args.ApplicableSetting); err != nil {
		return VersionManagedEmailSectionDeclaration{}, fmt.Errorf(
			"failed to update section declaration applicable setting: %w",
			err,
		)
	}

	if err := updatedSectionDeclaration.UpdateLimitationSetting(args.LimitationSetting); err != nil {
		return VersionManagedEmailSectionDeclaration{}, fmt.Errorf(
			"failed to update section declaration limitation setting: %w",
			err,
		)
	}

	if err := updatedSectionDeclaration.UpdateInputSetting(args.InputSetting); err != nil {
		return VersionManagedEmailSectionDeclaration{}, fmt.Errorf(
			"failed to update section declaration input setting: %w",
			err,
		)
	}

	if err := updatedSectionDeclaration.UpdateSectionTemplateSetting(args.SectionTemplateSetting); err != nil {
		return VersionManagedEmailSectionDeclaration{}, fmt.Errorf(
			"failed to update section declaration template setting: %w",
			err,
		)
	}

	if err := updatedSectionDeclaration.UpdatePresentationSettings(args.PresentationSettings); err != nil {
		return VersionManagedEmailSectionDeclaration{}, fmt.Errorf(
			"failed to update section declaration presentation settings: %w",
			err,
		)
	}

	if err := updatedSectionDeclaration.UpdateOrderingWeight(args.OrderingWeight); err != nil {
		return VersionManagedEmailSectionDeclaration{}, fmt.Errorf(
			"failed to update section declaration ordering weight: %w",
			err,
		)
	}

	// Use the RevisionPort to update the section declaration
	result, err := uc.revisionPort.UpdateSectionDeclaration(ctx, UpdateSectionDeclarationPortArgs{
		HostProductCode:    updatedSectionDeclaration.HostProductCode,
		SectionDeclaration: updatedSectionDeclaration,
		UpdatedBy:          args.UpdatedBy,
	})
	if err != nil {
		return VersionManagedEmailSectionDeclaration{}, xerrors.Wrap(err,
			xerrors.Field("sectionDeclarationID", args.ID))
	}

	return result, nil
}

// ResetSectionDeclaration resets an email section declaration to its previous released version.
func (uc *versionManagedSectionDeclarationImpl) ResetSectionDeclaration(
	ctx context.Context,
	args ResetSectionDeclarationArgs,
) error {
	if err := validator.ValidateStruct(args); err != nil {
		return fmt.Errorf("invalid reset section declaration arguments: %w", err)
	}

	// First, get the existing section declaration to retrieve necessary information
	existingResult, err := uc.revisionPort.GetSectionDeclaration(ctx, GetSectionDeclarationPortArgs{
		HostProductCode: args.HostProductCode,
		ID:              args.ID,
	})
	if err != nil {
		return xerrors.Wrap(err,
			xerrors.Field("sectionDeclarationID", args.ID))
	}

	// Check if section declaration can be deleted (business logic)
	if err := existingResult.Extension.CanBeDeleted(); err != nil {
		return fmt.Errorf("section declaration cannot be reset: %w", err)
	}

	// Use the RevisionPort to reset the section declaration
	return uc.revisionPort.ResetSectionDeclaration(ctx, ResetSectionDeclarationPortArgs{
		HostProductCode: existingResult.Extension.HostProductCode,
		ID:              args.ID,
		ResetBy:         args.ResetBy,
	})
}

// Release releases a version of email section declarations using the VersionAgent.
func (uc *versionManagedSectionDeclarationImpl) Release(
	ctx context.Context,
	args SectionDeclarationReleaseArgs,
) error {
	if err := validator.ValidateStruct(args); err != nil {
		return fmt.Errorf("invalid release arguments: %w", err)
	}

	err := uc.versionAgent.Release(ctx, args)
	if err != nil {
		return xerrors.Wrap(err,
			xerrors.Field("hostProductCode", args.HostProductCode))
	}
	return nil
}
