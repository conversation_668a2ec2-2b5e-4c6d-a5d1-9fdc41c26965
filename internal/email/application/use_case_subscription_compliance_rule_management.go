package emailapplication

import (
	"context"

	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
)

type UseCaseSubscriptionComplianceRuleManagement interface {
	// Create creates a new subscription compliance rule.
	Create(
		ctx context.Context,
		params CreateSubscriptionComplianceRuleParams,
	) (recipientprofile.SubscriptionComplianceRule, error)
}

type CreateSubscriptionComplianceRuleParams struct {
	HostProductCode  string                                `json:"host_product_code"`
	BusinessScenario string                                `json:"business_scenario"`
	RuleType         recipientprofile.RuleType             `json:"rule_type"`
	AllowStatus      []recipientprofile.SubscriptionStatus `json:"allow_status"`
	DenyStatus       []recipientprofile.SubscriptionStatus `json:"deny_status"`
	Description      string                                `json:"description"`
}

type useCaseSubscriptionComplianceRuleManagement struct {
	subscriptionComplianceRuleRepo recipientprofile.SubscriptionComplianceRuleRepository
}

func NewUseCaseSubscriptionComplianceRuleManagement(
	repo recipientprofile.SubscriptionComplianceRuleRepository,
) UseCaseSubscriptionComplianceRuleManagement {
	return &useCaseSubscriptionComplianceRuleManagement{
		subscriptionComplianceRuleRepo: repo,
	}
}

func (uc *useCaseSubscriptionComplianceRuleManagement) Create(
	ctx context.Context,
	params CreateSubscriptionComplianceRuleParams,
) (recipientprofile.SubscriptionComplianceRule, error) {
	rule := &recipientprofile.SubscriptionComplianceRule{
		ID:               uc.subscriptionComplianceRuleRepo.NextID(ctx),
		HostProductCode:  params.HostProductCode,
		BusinessScenario: params.BusinessScenario,
		RuleType:         params.RuleType,
		AllowStatus:      params.AllowStatus,
		DenyStatus:       params.DenyStatus,
		Description:      params.Description,
	}
	err := uc.subscriptionComplianceRuleRepo.Create(ctx, rule)
	if err != nil {
		return recipientprofile.SubscriptionComplianceRule{}, err
	}
	return *rule, nil
}
