package emailapplication_test

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/fx"

	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/pltf-nf-message/internal/adapters"
	"github.com/AfterShip/pltf-nf-message/internal/commoncontent"
	"github.com/AfterShip/pltf-nf-message/internal/email"
	emailapp "github.com/AfterShip/pltf-nf-message/internal/email/application"
)

func Example_sendEmailByTemplateGroupID() {
	var applicationService emailapp.ApplicationService

	app := fx.New(
		adapters.Module,
		email.Module,
		commoncontent.Module,

		fx.Populate(&applicationService),
	)

	ctx := context.Background()
	err := app.Start(ctx)
	if err != nil {
		panic(err)
	}

	res, err := applicationService.SendEmailByTemplateGroupID(ctx, emailapp.SendEmailByTemplateGroupIDParams{
		OrganizationID:  "92d71b37fcdb49c7baf542c44a663d3f",
		HostProductCode: "platform_notification",
		AppPlatform:     "shopify",
		AppKey:          "sunnygoods2",
		TemplateGroupID: "007910ea1f58477c87213ebf4a691c7a",
		LanguageTag:     nil,
		Data: map[string]any{
			"organization": map[string]any{
				"name": "AfterShip",
			},
			"customer": map[string]any{
				"first name": "Samuel",
			},
			"data": map[string]any{
				"tracking": map[string]any{
					"tracking_number": "1234567890",
				},
			},
			"trigger_event": map[string]any{
				"event_payload": map[string]any{
					"tracking": map[string]any{
						"tracking_number": "1234567890",
					},
				},
			},
		},
		Recipient: emailapp.Recipient{
			Email: "<EMAIL>",
			Name:  "Zy Chen",
		},
		UniqueKey: uuid.GenerateUUIDV4(),
	})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(res)
	// >Output:
}

func Example_unsubscribe() {
	var applicationService emailapp.ApplicationService

	app := fx.New(
		adapters.Module,
		email.Module,
		commoncontent.Module,

		fx.Populate(&applicationService),
	)
	ctx := context.Background()
	err := app.Start(ctx)
	if err != nil {
		panic(err)
	}

	err = applicationService.GlobalUnsubscribeByToken(ctx, emailapp.GlobalUnsubscribeByTokenParams{
		Token:          "1VBMqjx44606GA6aIUHdpOcyCvVUi1P7jdJi4ASECEeNv1c3OdHrb%2FD5Tl0ITxXGh2wECZWR4BWRcrNCh31sWMjVvDVz%2FckN0mPccXKaLVFNOBbN1ZNi1KO3hyQszsYeV4RO4mrM32vNkWZzAs7LNY2JaZbwT3c4ME8zXLTIxQaT%2BeWPqfb3DOo7JHazV0YLVWz4xNadpL75OaJ5MJ6O8ypPaKKzV9HkIeSHzuNgvZfS8CO3y79UGgjXM2gjxgkNL5rR1zXu3mnBmZQvHGq8rw%3D%3D",
		Version:        "v1",
		UnsubscribedAt: time.Now(),
	})
	if err != nil {
		fmt.Println(err)
	}
	// >Output:
}

func Example_cancelUnsubscribe() {
	var applicationService emailapp.ApplicationService

	app := fx.New(
		adapters.Module,
		email.Module,
		commoncontent.Module,

		fx.Populate(&applicationService),
	)
	ctx := context.Background()
	err := app.Start(ctx)
	if err != nil {
		panic(err)
	}

	err = applicationService.CancelGlobalUnsubscribed(ctx, emailapp.CancelGlobalUnsubscribedParams{
		Token:       "InalIgrso8ctO79C5yb8QxHdQnba9IvQjlXSHImxnU3+j9K6JH4Dr+TzIyVsQCBEAr6VniJj7BuM5KsS6dNokB2i50T5pgWKKvLfiDj5CRsig1PSTTZFg1LsDSQSTBR4ftiIfmmBsC8MzAopOZ5xUfQ32LZ4N4DpBizPJ6X0lTnHHVmCxAfWxrJ1edoh9FoHa3ysV9LCASSa9o1H/Pn7p8ryTS0jOpEie+WSAdq6UbDxqs4ug/FBoNIZUx9l+T8S7JILRCuU7ac/U3Hu5fp7zA==",
		Version:     "v1",
		CancelledAt: time.Now(),
	})
	if err != nil {
		fmt.Println(err)
	}
	// >Output:
}

func Example_updatePreferenceStatus() {
	var applicationService emailapp.ApplicationService

	app := fx.New(
		adapters.Module,
		email.Module,
		commoncontent.Module,

		fx.Populate(&applicationService),
	)
	ctx := context.Background()
	err := app.Start(ctx)
	if err != nil {
		panic(err)
	}

	err = applicationService.UpdatePreferenceStatus(ctx, emailapp.UpdatePreferenceStatusParams{
		OrganizationID:     "92d71b37fcdb49c7baf542c44a663d3f",
		Email:              "<EMAIL>",
		AppPlatform:        "shopify",
		AppKey:             "sunnygoods2",
		HostProductCode:    "platform_notification",
		BusinessScenario:   "order_shipped",
		SubscriptionStatus: "subscribed",
		EffectiveAt:        time.Now(),
	})
	if err != nil {
		fmt.Println(err)
	}
	// >Output:
}
