package recipientprofile

import "context"

type RecipientProfileRepository interface {
	RunInTransaction(ctx context.Context, fn func(ctx context.Context) error) error
	NextID(ctx context.Context) string
	Find(ctx context.Context, params FindParams) (RecipientProfile, error)
	Create(ctx context.Context, profile *RecipientProfile) error
	Update(ctx context.Context, profile *RecipientProfile) error
}

type FindParams struct {
	Email          string `json:"email"`
	OrganizationID string `json:"organization_id"`
	AppPlatform    string `json:"app_platform"`
	AppKey         string `json:"app_key"`
}
