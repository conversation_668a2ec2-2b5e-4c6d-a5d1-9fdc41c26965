package recipientprofile

import "time"

// NotificationPreferenceDeclaration describes how to create a notification preference.
// If BU wants to custom preference at a more granular level, they need to register a new declaration on BU admin.
// The registered declaration will be available for selection in the preference setting page for C site users.
type NotificationPreferenceDeclaration struct {
	ID              string `json:"id"`
	Handle          string `json:"handle"`
	HostProductCode string `json:"host_product_code"`

	// BusinessScenario indicates which business scenario that the notification preference applies to.
	BusinessScenario string `json:"business_scenario"`

	// IsDefault = true indicates that the notification preference created from this declaration will be the default one for the host product code.
	// One and only one declaration is required to set as default for each host product code.
	IsDefault bool `json:"is_default"`

	// Name of a notification preference declaration.
	// It is used to display in the preference setting page for C site users.
	Name string `json:"name"`

	// Description of a notification preference declaration.
	// It is used to display in the preference setting page for C site users.
	Description string `json:"description"`

	CreatedAt time.Time  `json:"created_at" body:"created_at"`
	UpdatedAt *time.Time `json:"updated_at" body:"updated_at"`
}
