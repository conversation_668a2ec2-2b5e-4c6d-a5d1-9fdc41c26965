package recipientprofile

import (
	"time"

	"github.com/samber/lo"
)

type SubscriptionStatus string
type Method string

const (
	SubscriptionStatusSubscribed          SubscriptionStatus = "subscribed"
	SubscriptionStatusUnsubscribed        SubscriptionStatus = "unsubscribed"
	SubscriptionStatusPendingConfirmation SubscriptionStatus = "pending_confirmation"
	SubscriptionStatusNotSet              SubscriptionStatus = "not_set"

	UpdateTriggerContactEvent Method = "contact_event"
	UpdateTriggerUserAction   Method = "user_action"

	HostProductCodeAutomizely = "automizely"
)

type RecipientProfile struct {
	ID             string `json:"id"`
	OrganizationID string `json:"organization_id"`
	AppPlatform    string `json:"app_platform"`
	AppKey         string `json:"app_key"`
	Email          string `json:"email"`

	// Subscription represents the subscription and preference status of the recipient.
	Subscription Subscription `json:"subscription"`

	// Suppressions is the list of suppressions for the recipient.
	Suppressions []Suppression `json:"suppressions"`

	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
}

type Subscription struct {
	SubscriptionID string `json:"subscription_id"`

	// GlobalUnsubscribe holds the global unsubscribe status and related metadata.
	GlobalUnsubscribe GlobalUnsubscribe `json:"global_unsubscribe"`

	// NotificationPreferences is the list of notification preferences for each host product.
	NotificationPreferences []NotificationPreference `json:"notification_preferences"`
}

type GlobalUnsubscribe struct {
	IsUnsubscribed bool `json:"is_unsubscribed"`

	// UnsubscribedAt is the timestamp when the unsubscribe action occurs.
	// This timestamp exists only when IsUnsubscribed is true.
	UnsubscribedAt time.Time `json:"unsubscribed_at"`

	// ResubscribedAt is the timestamp when the resubscribe action occurs.
	// When IsUnsubscribed is true, ResubscribedAt would always be empty.
	// When IsUnsubscribed is false, ResubscribedAt could be empty if the recipient has never been unsubscribed before.
	ResubscribedAt time.Time `json:"resubscribed_at"`

	// Method represents the direct entry point that caused global unsubscribe status change from system's perspective, such as unsubscribe_page, acrm_sync.
	Method Method `json:"method"`
}

type NotificationPreference struct {
	NotificationPreferenceID string `json:"notification_preference_id"`
	HostProductCode          string `json:"host_product_code"`

	// BusinessScenario is the business scenario that this notification preference is used for.
	// Note that at most one notification preference is allowed for each business scenario.
	BusinessScenario string `json:"business_scenario"`

	// IsProductDefault is true if this notification preference is the default one for the host product code when no preference is set for the given business scenario.
	IsProductDefault bool `json:"is_product_default"`

	// DeclarationHandle is the handle of the declaration that this notification preference is created from.
	// At this stage, BU is not allowed to register a preference declaration.
	DeclarationHandle string `json:"declaration_handle"`

	// SubscriptionStatus is the subscription status of this notification preference.
	// Candidate values are {"subscribed", "unsubscribed", "pending_confirmation", "not_set"}.
	SubscriptionStatus SubscriptionStatus `json:"subscription_status"`

	// CollectedAt is the timestamp when the subscription status is collected.
	CollectedAt time.Time `json:"collected_at"`

	// Method represents the direct entry point that caused a preference's subscription status change from system's perspective.
	// Candidate values are {"unsubscribe_page", "acrm_sync"}.
	Method Method `json:"method"`
}

type Suppression struct {
	SuppressionID string `json:"suppression_id"`

	// Type is the type of the suppression, e.g. "hard_bounce", "manual_suppression", etc.
	Type string `json:"type"`

	// Reason is the detail reason of the suppression, e.g. "the email address is unreachable", etc.
	Reason string `json:"reason"`

	SuppressedAt time.Time `json:"suppressed_at"`
}

func (rp *RecipientProfile) GetPreferenceStatus(hostProductCode string, businessScenario string) SubscriptionStatus {
	if rp.Subscription.GlobalUnsubscribe.IsUnsubscribed {
		return SubscriptionStatusUnsubscribed
	}
	if scenarioPreference, ok := lo.Find(rp.Subscription.NotificationPreferences, func(p NotificationPreference) bool {
		return p.HostProductCode == hostProductCode && p.BusinessScenario == businessScenario
	}); ok {
		return scenarioPreference.SubscriptionStatus
	}

	if defaultPreference, ok := lo.Find(rp.Subscription.NotificationPreferences, func(p NotificationPreference) bool {
		return p.HostProductCode == hostProductCode && p.IsProductDefault
	}); ok {
		return defaultPreference.SubscriptionStatus
	}

	return SubscriptionStatusNotSet
}

func (rp *RecipientProfile) GetPreferenceStatusAndEffectiveAt(
	hostProductCode string,
	businessScenario string,
) (SubscriptionStatus, time.Time) {
	if rp.Subscription.GlobalUnsubscribe.IsUnsubscribed {
		return SubscriptionStatusUnsubscribed, rp.Subscription.GlobalUnsubscribe.UnsubscribedAt
	}
	if scenarioPreference, ok := lo.Find(rp.Subscription.NotificationPreferences, func(p NotificationPreference) bool {
		return p.HostProductCode == hostProductCode && p.BusinessScenario == businessScenario
	}); ok {
		return scenarioPreference.SubscriptionStatus, lo.Ternary(
			rp.Subscription.GlobalUnsubscribe.UnsubscribedAt.After(scenarioPreference.CollectedAt),
			rp.Subscription.GlobalUnsubscribe.UnsubscribedAt,
			scenarioPreference.CollectedAt,
		)
	}
	if defaultPreference, ok := lo.Find(rp.Subscription.NotificationPreferences, func(p NotificationPreference) bool {
		return p.HostProductCode == hostProductCode && p.IsProductDefault
	}); ok {
		return defaultPreference.SubscriptionStatus, lo.Ternary(
			rp.Subscription.GlobalUnsubscribe.UnsubscribedAt.After(defaultPreference.CollectedAt),
			rp.Subscription.GlobalUnsubscribe.UnsubscribedAt,
			defaultPreference.CollectedAt,
		)
	}

	return SubscriptionStatusNotSet, time.Time{}
}

// Unsubscribe sets the global unsubscribe status and related metadata.
// If the provided timestamp is earlier than the existing one, the update will be rejected.
// Note that it does not change the preference status so that the preference status can be restored when the global unsubscribe is cancelled.
func (rp *RecipientProfile) Unsubscribe(unsubscribedAt time.Time, updateTrigger Method) bool {
	if unsubscribedAt.Before(rp.Subscription.GlobalUnsubscribe.UnsubscribedAt) {
		return false
	}
	rp.Subscription.GlobalUnsubscribe.IsUnsubscribed = true
	rp.Subscription.GlobalUnsubscribe.UnsubscribedAt = unsubscribedAt
	rp.Subscription.GlobalUnsubscribe.Method = updateTrigger
	return true
}

func (rp *RecipientProfile) CancelGlobalUnsubscribed(cancelledAt time.Time, updateTrigger Method) bool {
	if cancelledAt.Before(rp.Subscription.GlobalUnsubscribe.UnsubscribedAt) {
		return false
	}
	rp.Subscription.GlobalUnsubscribe.IsUnsubscribed = false
	rp.Subscription.GlobalUnsubscribe.UnsubscribedAt = cancelledAt
	rp.Subscription.GlobalUnsubscribe.Method = updateTrigger
	return true
}

func (rp *RecipientProfile) GetAMPreferenceStatusAndEffectiveAt() (SubscriptionStatus, time.Time) {
	return rp.GetPreferenceStatusAndEffectiveAt(HostProductCodeAutomizely, BusinessScenarioAny)
}

func (rp *RecipientProfile) UpdatePreferenceStatus(
	hostProductCode string,
	businessScenario string,
	newStatus SubscriptionStatus,
	newEffectiveAt time.Time,
	updateTrigger Method,
) bool {
	_, existingEffectiveAt := rp.GetPreferenceStatusAndEffectiveAt(hostProductCode, businessScenario)
	if newEffectiveAt.Before(existingEffectiveAt) {
		return false
	}
	if newStatus != SubscriptionStatusUnsubscribed {
		rp.CancelGlobalUnsubscribed(newEffectiveAt, updateTrigger)
	}

	// Try to find and update existing preference
	for i := range rp.Subscription.NotificationPreferences {
		if rp.Subscription.NotificationPreferences[i].HostProductCode == hostProductCode &&
			rp.Subscription.NotificationPreferences[i].BusinessScenario == businessScenario {
			rp.Subscription.NotificationPreferences[i].SubscriptionStatus = newStatus
			rp.Subscription.NotificationPreferences[i].CollectedAt = newEffectiveAt
			rp.Subscription.NotificationPreferences[i].Method = updateTrigger
			return true
		}
	}

	// If not found, create new preference
	rp.Subscription.NotificationPreferences = append(rp.Subscription.NotificationPreferences, NotificationPreference{
		HostProductCode:    hostProductCode,
		BusinessScenario:   businessScenario,
		SubscriptionStatus: newStatus,
		CollectedAt:        newEffectiveAt,
		Method:             updateTrigger,
	})
	return true
}

// UpdateAMPreferenceStatus updates the AMP reference status, will return true if the status is changed.
func (rp *RecipientProfile) UpdateAMPreferenceStatus(
	status SubscriptionStatus,
	newEffectiveAt time.Time,
	updateTrigger Method,
) bool {
	return rp.UpdatePreferenceStatus(
		HostProductCodeAutomizely,
		BusinessScenarioAny,
		status,
		newEffectiveAt,
		updateTrigger,
	)
}

//nolint:nonamedreturns // It is a valid check.
func (rp *RecipientProfile) IsSuppressed() (result bool, reason string) {
	if len(rp.Suppressions) == 0 {
		return false, ""
	}
	// suppressed if there is any suppression
	return true, rp.Suppressions[0].Reason
}

func (rp *RecipientProfile) AddSuppression(s Suppression) {
	rp.Suppressions = append(rp.Suppressions, s)
}

func (rp *RecipientProfile) RemoveSuppression(suppressionType string) {
	rp.Suppressions = lo.Filter(rp.Suppressions, func(s Suppression, _ int) bool {
		return s.Type != suppressionType
	})
}
