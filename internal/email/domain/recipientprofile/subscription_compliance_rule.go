package recipientprofile

import (
	"time"

	"github.com/samber/lo"
)

type RuleType string

const (
	RuleTypeAllowList RuleType = "allow_list"
	RuleTypeDenyList  RuleType = "deny_list"

	BusinessScenarioAny = "_any"
)

// SubscriptionComplianceRule defines a rule to check whether a subscription status is allowed to send notification.
// It is configured by BU.
// Each host product is required to have a global rule.
type SubscriptionComplianceRule struct {
	// ID is the unique identifier of the subscription rule.
	ID string `json:"id"`

	// HostProductCode is the host product code of the subscription rule.
	HostProductCode string `json:"host_product_code"`

	// BusinessScenario indicates which business scenario that the subscription rule applies to.
	// For product global rule, it is "_any", which means the rule applies to all business scenarios.
	BusinessScenario string `json:"business_scenario"`

	// RuleType is the type of the subscription rule.
	// "allow_list" means that notification can only be sent to the recipients whose subscription status is in the allow list.
	// "deny_list" means that notification can be sent to all recipients except those whose subscription status is in the deny list.
	RuleType RuleType `json:"rule_type"`

	// AllowStatus is the subscription status that is allowed by the subscription rule.
	// It will be used when the rule type is allow_list.
	AllowStatus []SubscriptionStatus `json:"allow_status"`

	// DenyStatus is the subscription status that is denied by the subscription rule.
	// It will be used when the rule type is deny_list.
	DenyStatus []SubscriptionStatus `json:"deny_status"`

	// Description of the subscription rule.
	Description string `json:"description"`

	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
}

// CheckSubscriptionStatus checks whether the given subscription status is allowed by the subscription rule.
func (rule *SubscriptionComplianceRule) CheckSubscriptionStatus(status SubscriptionStatus) bool {
	switch rule.RuleType {
	case RuleTypeAllowList:
		return lo.Contains(rule.AllowStatus, status)
	case RuleTypeDenyList:
		return !lo.Contains(rule.DenyStatus, status)
	}
	return false
}
