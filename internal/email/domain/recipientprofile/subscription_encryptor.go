package recipientprofile

type SubscriptionEncryptor interface {
	// Encrypt encrypts the subscription info.
	Encrypt(info SubscriptionInfo) (string, error)

	// Decrypt decrypts the subscription info.
	Decrypt(token string) (SubscriptionInfo, error)

	// Version returns the version of the encryption method.
	Version() string
}

type SubscriptionEncryptorRouter interface {
	// GetEncryptor returns the encryptor for the given version.
	GetEncryptor(version string) (SubscriptionEncryptor, error)

	// RegisterEncryptor registers a new encryptor.
	RegisterEncryptor(encryptor SubscriptionEncryptor) error

	// GetLatestEncryptor returns the latest encryptor.
	GetLatestEncryptor() (SubscriptionEncryptor, error)
}
