package recipientprofile

import "context"

type SubscriptionComplianceRuleRepository interface {
	NextID(ctx context.Context) string
	// FindByHostProductCode finds all subscription compliance rules for the given host product code.
	FindByHostProductCode(ctx context.Context, hostProductCode string) ([]SubscriptionComplianceRule, error)
	Create(ctx context.Context, rule *SubscriptionComplianceRule) error
	Update(ctx context.Context, rule *SubscriptionComplianceRule) error
}
