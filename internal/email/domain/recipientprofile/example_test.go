package recipientprofile_test

import (
	"context"

	"github.com/AfterShip/pltf-nf-message/internal/email/adapters/encryptor"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
)

func ExampleDomainService_GenerateUnsubscribeURL() {
	cipher := encryptor.NewSubscriptionMsgCipher("Bh0dRo2XAWs1exVAkObnKsVbFk5WxQ3PhKOngwjO0+Q")
	router := encryptor.NewEncryptorRouter()
	err := router.RegisterEncryptor(cipher)
	if err != nil {
		panic(err)
	}
	domainSVC := recipientprofile.NewDomainService(router)
	url, err := domainSVC.GenerateUnsubscribeURL(context.Background(), recipientprofile.GenerateUnsubscribeURLParams{
		Email:           "<EMAIL>",
		OrganizationID:  "92d71b37fcdb49c7baf542c44a663d3f",
		HostProductCode: "platform_notification",
		AppPlatform:     "shopify",
		AppKey:          "sunnygoods2",
	})
	if err != nil {
		panic(err)
	}
	println(url)
	// >Output:
}
