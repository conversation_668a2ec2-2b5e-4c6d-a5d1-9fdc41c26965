package recipientprofile

import (
	"context"
	"net/url"

	"github.com/samber/lo"

	"github.com/AfterShip/gopkg/cfg"
)

const (
	unsubscribePath = "api/v1/public/email/unsubscribe"
)

// TODO: replace with public host.
var (
	envHosts = map[string]string{
		"local":       "pltf-nf-message.as-in.io",
		"development": "pltf-nf-message.as-in.io",
		"testing":     "pltf-nf-message.as-in.io",
		"staging":     "pltf-nf-message.as-in.com",
		"production":  "pltf-nf-message.as-in.com",
	}
)

type DomainService interface {
	// CheckSubscriptionCompliance checks whether the recipient profile complies with the subscription rules.
	CheckSubscriptionCompliance(ctx context.Context, params SubscriptionCheckParams) (bool, error)

	// GenerateUnsubscribeURL generates the unsubscribed url for the recipient.
	GenerateUnsubscribeURL(ctx context.Context, params GenerateUnsubscribeURLParams) (string, error)

	// DecryptToken decrypts the token and returns the subscription info.
	DecryptToken(ctx context.Context, params DecryptTokenParams) (SubscriptionInfo, error)
}

type SubscriptionCheckParams struct {
	BusinessScenario                   string
	HostProductCode                    string
	RecipientProfile                   RecipientProfile
	ProductSubscriptionComplianceRules []SubscriptionComplianceRule
}

type GenerateUnsubscribeURLParams struct {
	Email           string         `json:"email"`
	OrganizationID  string         `json:"organization_id"`
	HostProductCode string         `json:"host_product_code"`
	AppPlatform     string         `json:"app_platform"`
	AppKey          string         `json:"app_key"`
	CustomFields    map[string]any `json:"custom_fields"`
}

type SubscriptionInfo struct {
	Email           string         `json:"email"`
	OrganizationID  string         `json:"organization_id"`
	HostProductCode string         `json:"host_product_code"`
	AppPlatform     string         `json:"app_platform"`
	AppKey          string         `json:"app_key"`
	CustomFields    map[string]any `json:"custom_fields"`
}

type DecryptTokenParams struct {
	// Token is the encrypted info of the unsubscribe request.
	Token string `json:"token"`

	// Version is the version of encryption method.
	Version string `json:"version"`
}

type domainService struct {
	subscriptionEncryptorRouter SubscriptionEncryptorRouter
}

func NewDomainService(encryptorRouter SubscriptionEncryptorRouter) DomainService {
	return &domainService{
		subscriptionEncryptorRouter: encryptorRouter,
	}
}

func (d *domainService) CheckSubscriptionCompliance(ctx context.Context, params SubscriptionCheckParams) (bool, error) {
	var (
		rule SubscriptionComplianceRule
		ok   bool
	)
	rule, ok = lo.Find(params.ProductSubscriptionComplianceRules, func(rule SubscriptionComplianceRule) bool {
		return rule.BusinessScenario == params.BusinessScenario
	})
	if !ok {
		rule, ok = lo.Find(params.ProductSubscriptionComplianceRules, func(rule SubscriptionComplianceRule) bool {
			return rule.BusinessScenario == BusinessScenarioAny
		})
		if !ok {
			return false, ErrSubscriptionComplianceRuleNotFound
		}
	}

	subscriptionStatus := params.RecipientProfile.GetPreferenceStatus(params.HostProductCode, params.BusinessScenario)
	return rule.CheckSubscriptionStatus(subscriptionStatus), nil
}

func (d *domainService) GenerateUnsubscribeURL(
	ctx context.Context,
	params GenerateUnsubscribeURLParams,
) (string, error) {
	subscriptionInfo := SubscriptionInfo(params)
	encryptor, err := d.subscriptionEncryptorRouter.GetLatestEncryptor()
	if err != nil {
		return "", err
	}
	encryptedToken, err := encryptor.Encrypt(subscriptionInfo)
	if err != nil {
		return "", err
	}
	return assembleUnsubscribeURL(assembleUnsubscribeURLArgs{
		Token:   encryptedToken,
		Version: encryptor.Version(),
		Scheme:  "https",
		Host:    envHosts[cfg.GetCurrentStage()],
		Path:    unsubscribePath,
	}), nil
}

func (d *domainService) DecryptToken(ctx context.Context, params DecryptTokenParams) (SubscriptionInfo, error) {
	encryptor, err := d.subscriptionEncryptorRouter.GetEncryptor(params.Version)
	if err != nil {
		return SubscriptionInfo{}, err
	}
	return encryptor.Decrypt(params.Token)
}

type assembleUnsubscribeURLArgs struct {
	Scheme  string
	Host    string
	Path    string
	Token   string
	Version string
}

func assembleUnsubscribeURL(params assembleUnsubscribeURLArgs) string {
	var u url.URL
	u.Scheme = params.Scheme
	u.Host = params.Host
	u.Path = params.Path

	q := u.Query()
	q.Add("t", params.Token)
	q.Add("v", params.Version)

	u.RawQuery = q.Encode()
	return u.String()
}
