package message

import (
	"context"
	"errors"

	"github.com/samber/lo"

	"github.com/AfterShip/library-heytea-go-common/xerrors"
)

const (
	ModeCustom = "custom"

	SenderSourceOrgSender      = "org_sender"
	SenderSourceProductDefault = "product_default"
)

var (
	ErrTemplateSenderNotFound = errors.New("template sender not found in organization senders")
	ErrInvalidSenderSource    = errors.New("invalid sender source")
)

// CustomSenderStrategy handles custom sender selection mode.
type CustomSenderStrategy struct{}

// NewCustomSenderStrategy creates a new custom sender strategy.
func NewCustomSenderStrategy() SenderSelectionStrategy {
	return &CustomSenderStrategy{}
}

// CanHandle returns true if this strategy can handle the given mode.
func (s *CustomSenderStrategy) CanHandle(mode string) bool {
	return mode == ModeCustom
}

// Priority returns the priority of this strategy.
func (s *CustomSenderStrategy) Priority() int {
	return 1 // High priority for custom mode
}

// SelectSender selects a sender using custom mode logic.
func (s *CustomSenderStrategy) SelectSender(ctx context.Context, params SenderSelectionParams) (EmailSender, error) {
	if !s.CanHandle(params.Mode) {
		return EmailSender{}, xerrors.Wrap(ErrInvalidMode, xerrors.Field("mode", params.Mode))
	}

	// Validate template sender
	if params.TemplateSender.ID == "" {
		return EmailSender{}, xerrors.Wrap(errors.New("template sender ID is required for custom mode"))
	}

	// Start with template sender info
	result := EmailSender{
		ID:   params.TemplateSender.ID,
		Name: params.TemplateSender.Name,
	}

	// Handle different sender sources
	switch params.SenderSource {
	case SenderSourceProductDefault:
		return s.handleProductDefaultSource(ctx, result, params)
	case SenderSourceOrgSender, "": // Empty defaults to org_sender
		return s.handleOrgSenderSource(ctx, result, params)
	default:
		return EmailSender{}, xerrors.Wrap(ErrInvalidSenderSource,
			xerrors.Field("sender_source", params.SenderSource))
	}
}

// handleProductDefaultSource handles product_default sender source.
func (s *CustomSenderStrategy) handleProductDefaultSource(
	_ context.Context,
	result EmailSender,
	params SenderSelectionParams,
) (EmailSender, error) {
	// For product_default, use the host product default sender's email
	if params.HostProductDefaultSender.Email == "" {
		return EmailSender{}, xerrors.Wrap(errors.New("host product default sender email is required"))
	}

	result.Email = params.HostProductDefaultSender.Email
	result.DomainVerified = true // Product default senders are always verified

	return result, nil
}

// handleOrgSenderSource handles org_sender source (default).
func (s *CustomSenderStrategy) handleOrgSenderSource(
	_ context.Context,
	result EmailSender,
	params SenderSelectionParams,
) (EmailSender, error) {
	// Find the template sender in organization senders
	orgSender, found := lo.Find(params.OrganizationSenders, func(sender EmailSender) bool {
		return sender.ID == result.ID
	})

	if !found {
		return EmailSender{}, xerrors.Wrap(ErrTemplateSenderNotFound,
			xerrors.Field("sender_id", result.ID))
	}

	// Use organization sender's email and verification status
	result.Email = orgSender.Email
	result.DomainVerified = orgSender.DomainVerified

	return result, nil
}

// Validate validates the parameters for custom sender strategy.
func (s *CustomSenderStrategy) Validate(params SenderSelectionParams) error {
	if params.TemplateSender.ID == "" {
		return errors.New("template sender ID is required for custom mode")
	}

	if params.SenderSource == SenderSourceProductDefault && params.HostProductDefaultSender.Email == "" {
		return errors.New("host product default sender email is required for product_default source")
	}

	if len(params.OrganizationSenders) == 0 {
		return errors.New("organization senders list cannot be empty")
	}

	return nil
}
