package message

import (
	"time"

	"github.com/samber/lo"

	"github.com/AfterShip/gopkg/uuid"
)

const (
	EmailMessageStatusInitial   = "Initial"
	EmailMessageStatusRendered  = "rendered"
	EmailMessageStatusAttempted = "attempted"
	EmailMessageStatusSent      = "sent"
	EmailMessageStatusFailed    = "failed"
	EmailMessageStatusCancelled = "cancelled"
)

type EmailMessage struct {
	// ID is the unique identifier of the email message.
	ID string `json:"id"`

	// OrganizationID is the organization ID of the organization that sent the notification.
	OrganizationID string `json:"organization_id"`

	// HostProductCode is the product code of the product that sends the notification.
	// e.g. aftership, returns
	// More details, please refer to the product code document https://cmdb.automizely.org/view/resource.
	HostProductCode string `json:"host_product_code"`

	// Sender is sender from which messages are sent
	Sender EmailSender `json:"sender"`

	// ReplyTo email will receive the response email if the to contact replied to the sent email .
	ReplyTo EmailSender `json:"reply_to"`

	// SendTo is whom the email will be sent
	SendTo EmailRecipient `json:"send_to"`

	// EmailContent is the rendered email content, represent the email content received by the recipient.
	EmailContent EmailContent `json:"rendered_email_multi_part"`

	// DataTracking is the data trace information of the notification.
	DataTracking DataTracking `json:"data_tracking"`

	// UniqueKey is used to ensure the notification is sent only once.
	// The UniqueKey is unique under the product code and organization, and will save for 7 days.
	UniqueKey string `json:"unique_key" body:"unique_key" validate:"required"`

	// MessageID is the message id returned by the message platform.
	MessageID string `json:"message_id"`

	// Status is the status of the process of sending the email.
	Status string `json:"status"`

	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
}

type EmailSender struct {
	// ID is the unique identifier of the sender.
	ID string `json:"id" body:"id"`

	// Email is the email address.
	Email string `json:"email" body:"email"`

	// Name is the name of the contact.
	Name string `json:"name" body:"name"`

	// DomainVerified is the flag to indicate whether the email domain is verified by the account.
	DomainVerified bool `json:"domain_verified" body:"domain_verified"`
}

type EmailRecipient struct {
	// ID is the recipient ID.
	// It is optional and can be empty.
	ID string `json:"id" body:"id"`

	// Type is used to identify the recipient type and to make the data trace more accurate.
	Type string `json:"type" body:"type" validate:"required,oneof=testing member contact"`

	// Email is email address to send the notification.
	Email string `json:"email" body:"email" validate:"required,email"`

	// Name of the recipient.
	// It will be used as the email to name when send email notification.
	// e.g. provided name is "John", the email to will be "John <email>".
	Name string `json:"name" body:"name"`
}

type EmailContent struct {
	Subject     string       `json:"subject"`
	HTML        string       `json:"html"`
	Attachments []Attachment `json:"attachments"`
}

type Attachment struct {
	Content     string `json:"content"`
	Disposition string `json:"disposition"`
	Filename    string `json:"filename"`
	Type        string `json:"type"`
}

type DataTracking struct {
	DataTrackingInput
	Content DataTrackingContent `json:"content"`
}

type DataTrackingInput struct {
	// BizType is the business type to trigger the notification.
	// It is used to breakdown the data report into different business types.
	// The BizType is the top level of the data report breakdown.
	// e.g. flows, test, campaigns, manual, btp_confirmation
	BizType string `json:"biz_type" body:"biz_type"`

	// BizSubType is the business sub type to trigger the notification.
	// It is used to breakdown the data report into different business sub types.
	// If the notification biz type if not enough to distinguish the notification, the biz sub type can be used.
	// The BizSubType is the second level of the data report breakdown.
	// And for now, the analytics system only support two levels of the data report breakdown.
	BizSubType string `json:"biz_sub_type" body:"biz_sub_type"`

	// BizID is the business entity id to trigger the notification.
	// It should be aligned with the biz type and biz sub type.
	// e.g if the biz type is flows, the biz id should be flow id.
	BizID string `json:"biz_id" body:"biz_id"`

	// BizMessageID is the notification id in the caller's system.
	// It is used to trace the notification in the caller's system.
	// The caller should pass the notification id when send the notification if the caller has own notification records.
	BizMessageID string `json:"biz_message_id" body:"biz_message_id"`

	// AppName is the app name of caller system to trigger the notification.
	AppName string `json:"app_name" body:"app_name"`

	// AppKey is the organization store's app key to trigger the notification.
	// It it used to make the data report to the organization store level.
	// We recommend to use the app key to make the data report more accurate.
	AppKey string `json:"app_key" body:"app_key"`

	// AppKey is the organization store's app platform to trigger the notification.
	// It it used to make the data report to the organization store level.
	// We recommend to use the app platform to make the data report more accurate.
	AppPlatform string `json:"app_platform" body:"app_platform"`
}

type DataTrackingContent struct {
	ContentGroupID   string `json:"content_group_id"`
	ContentVariantID string `json:"content_variant_id"`
}

type CreateEmailMessageParams struct {
	OrganizationID  string
	HostProductCode string
	Recipient       EmailRecipient
	Data            map[string]any
	UniqueKey       string
}

func NewEmailMessage(params CreateEmailMessageParams) *EmailMessage {
	return &EmailMessage{
		ID:              uuid.GenerateUUIDV4(),
		OrganizationID:  params.OrganizationID,
		HostProductCode: params.HostProductCode,
		SendTo:          params.Recipient,
		UniqueKey:       params.UniqueKey,
		Status:          EmailMessageStatusInitial,
		CreatedAt:       time.Now(),
	}
}

func (m *EmailMessage) MarkAsRendered(subject string, body string) {
	m.EmailContent.Subject = subject
	m.EmailContent.HTML = body
	m.Status = EmailMessageStatusRendered
	m.UpdatedAt = lo.ToPtr(time.Now())
}

func (m *EmailMessage) MarkAsAttempted(messageID string) {
	m.MessageID = messageID
	m.Status = EmailMessageStatusAttempted
	m.UpdatedAt = lo.ToPtr(time.Now())
}
