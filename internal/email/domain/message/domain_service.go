package message

import (
	"context"
)

type DomainService interface {
	ChooseEmailSender(ctx context.Context, params ChooseEmailSenderParams) (EmailSender, error)
}

type ChooseEmailSenderParams struct {
	Mode         string
	SenderSource string
	// OrganizationSenders is the list of senders that the organization configs in Account.
	OrganizationSenders []EmailSender
	// TemplateSender is the sender defined in the email template.
	TemplateSender EmailSender
	// GlobalSender is the default sender set by the organization for the given product.
	// Note: the email and verified fields are not set, you should read from the organization senders.
	GlobalSender EmailSender
	// HostProductDefaultSender is the default sender set by the host product.
	HostProductDefaultSender EmailSender
}

type domainService struct {
	senderSelectionStrategyRegistry SenderSelectionStrategyRegistry
}

func NewDomainService() DomainService {
	return &domainService{
		senderSelectionStrategyRegistry: NewSenderSelectionStrategyRegistry(),
	}
}

func (svc *domainService) ChooseEmailSender(ctx context.Context, params ChooseEmailSenderParams) (EmailSender, error) {
	// Convert to strategy params
	strategyParams := SenderSelectionParams(params)

	// Use strategy registry to select sender
	result, err := svc.senderSelectionStrategyRegistry.SelectSender(ctx, strategyParams)
	if err != nil {
		return EmailSender{}, err
	}

	return result.Sender, nil
}
