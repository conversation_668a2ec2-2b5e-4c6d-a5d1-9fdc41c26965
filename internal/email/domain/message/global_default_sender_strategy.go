package message

import (
	"context"
	"errors"

	"github.com/samber/lo"

	"github.com/AfterShip/library-heytea-go-common/xerrors"
)

const (
	ModeGlobalDefault = "global_default"
)

var (
	ErrGlobalSenderNotFound = errors.New("global sender not found in organization senders")
	ErrGlobalSenderIDEmpty  = errors.New("global sender ID is empty")
)

// GlobalDefaultSenderStrategy handles global_default sender selection mode.
type GlobalDefaultSenderStrategy struct{}

// NewGlobalDefaultSenderStrategy creates a new global default sender strategy.
func NewGlobalDefaultSenderStrategy() SenderSelectionStrategy {
	return &GlobalDefaultSenderStrategy{}
}

// CanHandle returns true if this strategy can handle the given mode.
func (s *GlobalDefaultSenderStrategy) CanHandle(mode string) bool {
	return mode == ModeGlobalDefault
}

// Priority returns the priority of this strategy.
func (s *GlobalDefaultSenderStrategy) Priority() int {
	//nolint:mnd // This is a valid check.
	return 2 // Lower priority than custom mode
}

// SelectSender selects a sender using global_default mode logic.
func (s *GlobalDefaultSenderStrategy) SelectSender(
	ctx context.Context,
	params SenderSelectionParams,
) (EmailSender, error) {
	if !s.CanHandle(params.Mode) {
		return EmailSender{}, xerrors.Wrap(ErrInvalidMode, xerrors.Field("mode", params.Mode))
	}

	// Validate global sender
	if params.GlobalSender.ID == "" {
		return EmailSender{}, xerrors.Wrap(ErrGlobalSenderIDEmpty,
			xerrors.Field("global_sender_id", params.GlobalSender.ID))
	}

	// Start with global sender info
	result := EmailSender{
		ID:   params.GlobalSender.ID,
		Name: params.GlobalSender.Name,
	}

	// Find the global sender in organization senders to get email and verification status
	orgSender, found := lo.Find(params.OrganizationSenders, func(sender EmailSender) bool {
		return sender.ID == result.ID
	})

	if !found {
		return EmailSender{}, xerrors.Wrap(ErrGlobalSenderNotFound,
			xerrors.Field("sender_id", result.ID))
	}

	// Use organization sender's email and verification status
	result.Email = orgSender.Email
	result.DomainVerified = orgSender.DomainVerified

	return result, nil
}

// Validate validates the parameters for global default sender strategy.
func (s *GlobalDefaultSenderStrategy) Validate(params SenderSelectionParams) error {
	if params.GlobalSender.ID == "" {
		return ErrGlobalSenderIDEmpty
	}

	if len(params.OrganizationSenders) == 0 {
		return errors.New("organization senders list cannot be empty")
	}

	return nil
}
