package message

import (
	"context"
	"errors"
)

var (
	ErrNoStrategyFound = errors.New("no suitable sender selection strategy found")
	ErrInvalidMode     = errors.New("invalid sender selection mode")
)

// SenderSelectionStrategy defines the interface for different sender selection strategies.
type SenderSelectionStrategy interface {
	// SelectSender selects an appropriate email sender based on the provided parameters
	SelectSender(ctx context.Context, params SenderSelectionParams) (EmailSender, error)

	// CanH<PERSON>le returns true if this strategy can handle the given mode
	CanHandle(mode string) bool

	// Priority returns the priority of this strategy (lower number = higher priority)
	Priority() int
}

// SenderSelectionParams contains all the parameters needed for sender selection.
type SenderSelectionParams struct {
	Mode         string
	SenderSource string

	// OrganizationSenders is the list of senders that the organization configs in Account.
	OrganizationSenders []EmailSender

	// TemplateSender is the sender defined in the email template.
	TemplateSender EmailSender

	// GlobalSender is the default sender set by the organization for the given product.
	// Note: the email and verified fields are not set, you should read from the organization senders.
	GlobalSender EmailSender

	// HostProductDefaultSender is the default sender set by the host product.
	HostProductDefaultSender EmailSender
}

// SenderSelectionResult contains the result of sender selection.
type SenderSelectionResult struct {
	Sender EmailSender
}

// SenderSelectionStrategyRegistry manages all available sender selection strategies.
type SenderSelectionStrategyRegistry interface {
	// RegisterStrategy registers a new strategy
	RegisterStrategy(strategy SenderSelectionStrategy) error

	// SelectSender selects a sender using the most appropriate strategy
	SelectSender(ctx context.Context, params SenderSelectionParams) (SenderSelectionResult, error)

	// GetStrategy returns a strategy that can handle the given mode
	GetStrategy(mode string) (SenderSelectionStrategy, error)
}

// strategyRegistry is the default implementation of SenderSelectionStrategyRegistry.
type strategyRegistry struct {
	strategies []SenderSelectionStrategy
}

// NewSenderSelectionStrategyRegistry creates a new strategy registry with default strategies.
func NewSenderSelectionStrategyRegistry() SenderSelectionStrategyRegistry {
	registry := &strategyRegistry{
		strategies: make([]SenderSelectionStrategy, 0),
	}

	// Register default strategies
	_ = registry.RegisterStrategy(NewCustomSenderStrategy())
	_ = registry.RegisterStrategy(NewGlobalDefaultSenderStrategy())

	return registry
}

// RegisterStrategy registers a new strategy.
func (r *strategyRegistry) RegisterStrategy(strategy SenderSelectionStrategy) error {
	if strategy == nil {
		return errors.New("strategy cannot be nil")
	}

	r.strategies = append(r.strategies, strategy)
	return nil
}

// SelectSender selects a sender using the most appropriate strategy.
func (r *strategyRegistry) SelectSender(
	ctx context.Context,
	params SenderSelectionParams,
) (SenderSelectionResult, error) {
	strategy, err := r.GetStrategy(params.Mode)
	if err != nil {
		return SenderSelectionResult{}, err
	}

	sender, err := strategy.SelectSender(ctx, params)
	if err != nil {
		return SenderSelectionResult{}, err
	}

	return SenderSelectionResult{
		Sender: sender,
	}, nil
}

// GetStrategy returns a strategy that can handle the given mode.
func (r *strategyRegistry) GetStrategy(mode string) (SenderSelectionStrategy, error) {
	var candidates []SenderSelectionStrategy

	// Find all strategies that can handle this mode
	for _, strategy := range r.strategies {
		if strategy.CanHandle(mode) {
			candidates = append(candidates, strategy)
		}
	}

	if len(candidates) == 0 {
		return nil, ErrNoStrategyFound
	}

	// Return the strategy with highest priority (lowest number)
	bestStrategy := candidates[0]
	for _, candidate := range candidates[1:] {
		if candidate.Priority() < bestStrategy.Priority() {
			bestStrategy = candidate
		}
	}

	return bestStrategy, nil
}
