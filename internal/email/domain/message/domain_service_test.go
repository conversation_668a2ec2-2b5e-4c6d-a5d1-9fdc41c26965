package message_test

import (
	"context"
	"testing"

	"github.com/AfterShip/pltf-nf-message/internal/email/domain/message"
	"github.com/stretchr/testify/require"
)

func TestDomainService_ChooseEmailSender_CustomMode(t *testing.T) {
	domainService := message.NewDomainService()
	ctx := context.Background()

	orgSenders := []message.EmailSender{
		{
			ID:             "sender-1",
			Email:          "<EMAIL>",
			Name:           "Sender One",
			DomainVerified: true,
		},
		{
			ID:             "sender-2",
			Email:          "<EMAIL>",
			Name:           "Sender Two",
			DomainVerified: false,
		},
	}

	tests := []struct {
		name           string
		params         message.ChooseEmailSenderParams
		expectedSender message.EmailSender
		expectError    bool
	}{
		{
			name: "custom mode with org_sender source",
			params: message.ChooseEmailSenderParams{
				Mode:         "custom",
				SenderSource: "org_sender",
				TemplateSender: message.EmailSender{
					ID:   "sender-1",
					Name: "Template Sender Name",
				},
				OrganizationSenders: orgSenders,
			},
			expectedSender: message.EmailSender{
				ID:             "sender-1",
				Email:          "<EMAIL>",
				Name:           "Template Sender Name",
				DomainVerified: true,
			},
			expectError: false,
		},
		{
			name: "custom mode with product_default source",
			params: message.ChooseEmailSenderParams{
				Mode:         "custom",
				SenderSource: "product_default",
				TemplateSender: message.EmailSender{
					ID:   "template-sender",
					Name: "Template Sender Name",
				},
				HostProductDefaultSender: message.EmailSender{
					Email: "<EMAIL>",
				},
				OrganizationSenders: orgSenders,
			},
			expectedSender: message.EmailSender{
				ID:             "template-sender",
				Email:          "<EMAIL>",
				Name:           "Template Sender Name",
				DomainVerified: true,
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := domainService.ChooseEmailSender(ctx, tt.params)

			if tt.expectError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expectedSender, result)
			}
		})
	}
}

func TestDomainService_ChooseEmailSender_GlobalDefaultMode(t *testing.T) {
	domainService := message.NewDomainService()
	ctx := context.Background()

	orgSenders := []message.EmailSender{
		{
			ID:             "global-sender-1",
			Email:          "<EMAIL>",
			Name:           "Global Sender One",
			DomainVerified: true,
		},
	}

	params := message.ChooseEmailSenderParams{
		Mode: "global_default",
		GlobalSender: message.EmailSender{
			ID:   "global-sender-1",
			Name: "Global Default Name",
		},
		OrganizationSenders: orgSenders,
	}

	result, err := domainService.ChooseEmailSender(ctx, params)

	require.NoError(t, err)
	expected := message.EmailSender{
		ID:             "global-sender-1",
		Email:          "<EMAIL>",
		Name:           "Global Default Name",
		DomainVerified: true,
	}
	require.Equal(t, expected, result)
}

func TestDomainService_ChooseEmailSender_UnknownMode(t *testing.T) {
	domainService := message.NewDomainService()
	ctx := context.Background()

	params := message.ChooseEmailSenderParams{
		Mode: "unknown_mode",
	}

	_, err := domainService.ChooseEmailSender(ctx, params)
	require.Error(t, err)
	require.Contains(t, err.Error(), "no suitable sender selection strategy found")
}

func TestCustomSenderStrategy_CanHandle(t *testing.T) {
	strategy := message.NewCustomSenderStrategy()

	tests := []struct {
		name     string
		mode     string
		expected bool
	}{
		{
			name:     "handles custom mode",
			mode:     message.ModeCustom,
			expected: true,
		},
		{
			name:     "does not handle global_default mode",
			mode:     message.ModeGlobalDefault,
			expected: false,
		},
		{
			name:     "does not handle unknown mode",
			mode:     "unknown",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := strategy.CanHandle(tt.mode)
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestCustomSenderStrategy_SelectSender_ProductDefaultSource(t *testing.T) {
	strategy := message.NewCustomSenderStrategy()
	ctx := context.Background()

	tests := []struct {
		name           string
		params         message.SenderSelectionParams
		expectedSender message.EmailSender
		expectError    bool
	}{
		{
			name: "successful selection with product_default source",
			params: message.SenderSelectionParams{
				Mode:         message.ModeCustom,
				SenderSource: message.SenderSourceProductDefault,
				TemplateSender: message.EmailSender{
					ID:   "template-sender",
					Name: "Template Sender Name",
				},
				HostProductDefaultSender: message.EmailSender{
					Email: "<EMAIL>",
				},
				OrganizationSenders: []message.EmailSender{}, // Not needed for product_default
			},
			expectedSender: message.EmailSender{
				ID:             "template-sender",
				Email:          "<EMAIL>",
				Name:           "Template Sender Name",
				DomainVerified: true, // Product default is always verified
			},
			expectError: false,
		},
		{
			name: "error when host product default sender email is empty",
			params: message.SenderSelectionParams{
				Mode:         message.ModeCustom,
				SenderSource: message.SenderSourceProductDefault,
				TemplateSender: message.EmailSender{
					ID:   "template-sender",
					Name: "Template Sender Name",
				},
				HostProductDefaultSender: message.EmailSender{
					Email: "",
				},
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := strategy.SelectSender(ctx, tt.params)

			if tt.expectError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expectedSender, result)
			}
		})
	}
}
