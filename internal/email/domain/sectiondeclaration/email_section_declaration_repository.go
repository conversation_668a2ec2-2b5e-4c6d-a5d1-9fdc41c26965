package sectiondeclaration

import (
	"context"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx/repositorytpl"
)

type Query struct {
	HostProductCode string `sql:"omitemapty"`
	Ids             []string
}

type EmailSectionDeclarationRepository interface {
	repositorytpl.Repository[EmailSectionDeclaration, Query]
	FindByHostProductCode(ctx context.Context, hostProductCode string) ([]EmailSectionDeclaration, error)
}
