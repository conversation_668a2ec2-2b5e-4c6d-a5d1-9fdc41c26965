package sectiondeclaration

import (
	"encoding/json"
	"errors"
	"time"

	"github.com/AfterShip/pltf-nf-infra/pkg/revision"
)

const (
	EmailSectionExtensionType = "email_section_declarations"
)

// EmailSectionDeclaration represents the configuration of a content section declaration.
//
//nolint:recvcheck // It is a valid check.
type EmailSectionDeclaration struct {
	ID string `json:"id" body:"id"`

	// HostProductCode the product code of the host product.
	HostProductCode string `json:"host_product_code" body:"host_product_code"`

	// Handle is the internal unique identity for a content section.
	// Usually it is generated by the system
	Handle string `json:"handle" body:"handle"`

	// Type of a content section.
	// It is used to identify a content section by the user.
	// Usually it should be snake, lowercase, and length not exceed 128
	// eg. image_and_text, product_recommendation, etc.
	Type string `json:"type" body:"type"`

	// Name of a content section.
	// The name of a content section is used to display in the list.
	Name string `json:"name" body:"name"`

	// Description of a content section.
	// The description of a content section is used to display in the list.
	Description string `json:"description" body:"description"`

	// Category of a content section.
	// eg. basic, footer, etc.
	Category string `json:"category" body:"category"`

	// OrderingWeight the ordering weight of a content section.
	// The content section will be sorted by the ordering weight in the list.
	OrderingWeight int64 `json:"ordering_weight" body:"ordering_weight"`

	// IconURL is the icon url of a content section.
	// The icon of a content section is used to display in the list.
	IconURL string `json:"icon_url" body:"icon_url"`

	// ApplicableSetting is the setting that a content section is applicable to.
	// The user can customize the setting of the section when create or update.
	ApplicableSetting ApplicableSetting `json:"applicable_setting" body:"applicable_setting"`

	// LimitationSetting is the limitation setting of a content section.
	// The user can customize the limitation setting of the section when create or update.
	LimitationSetting LimitationSetting `json:"limitation_setting" body:"limitation_setting"`

	// InputSetting os the default input setting of a content section.
	// Will fulfill the input setting to the input panel when a content section is added.
	InputSetting json.RawMessage `json:"input_setting" body:"input_setting"`

	// SectionTemplateSetting is the setting of a content section.
	// The user can customize the style and logic of the block.
	SectionTemplateSetting json.RawMessage `json:"section_template_setting" body:"section_template_setting"`

	// PresentationSettings is used to save the presentation settings by the FE side.
	PresentationSettings json.RawMessage `json:"presentation_settings" body:"presentation_settings"`

	// Hidden is the flag to hide a content section.
	// The hidden content section will not be shown in the list.
	Hidden bool `json:"hidden" body:"hidden"`

	CreatedAt time.Time  `json:"created_at" body:"created_at"`
	UpdatedAt *time.Time `json:"updated_at" body:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at" body:"deleted_at"`
}

// ApplicableSetting is the setting that the content section is applicable to.
type ApplicableSetting struct {
	// MessageChannels is the message channel that the content section is applicable to
	// Empty means all message channels.
	// e.g ["email", "sms"].
	MessageChannels []string `json:"message_channels" body:"message_channels"`

	// EditorKeys is the editor key that the content section is applicable to.
	// Empty means all editor keys.
	// e.g ["easy_email", "dnd_email"].
	EditorKeys []string `json:"editor_keys" body:"editor_keys"`

	// BusinessScenarioTypes is the business scenarios the content sections that is applicable to.
	// Empty means all business scenarios.
	// e.g ["order", "fulfillment"].
	BusinessScenarioTypes []string `json:"business_scenario_types" body:"business_scenario_types"`

	// Tags is the tags that the content section is applicable to.
	Tags []string `json:"tags" body:"tags"`
}

type LimitationSetting struct {
	// MaxCount is the max count of a content section can repeat in a content.
	MaxCount int64 `json:"max_count" body:"max_count"`

	// ExclusiveWithContentSectionTypes is the section types that a content section is exclusive with.
	// If a content section is exclusive with another content section, the two content sections can not be added to the same content.
	ExclusiveWithContentSectionTypes []string `json:"exclusive_with_content_section_types" body:"exclusive_with_content_section_types"`
}

type ContentSectionOrderingDescriptor struct {
	ID             string `json:"id"              body:"id"              validate:"required"`
	OrderingWeight int64  `json:"ordering_weight" body:"ordering_weight" validate:"required"`
}

// EmailSectionDeclarationValue represents the configuration value of an email section declaration,
// excluding metadata like ID, timestamps, etc. This matches the return type of GetValue().
type EmailSectionDeclarationValue struct {
	Type                   string            `json:"type"`
	Name                   string            `json:"name"`
	Description            string            `json:"description"`
	Category               string            `json:"category"`
	OrderingWeight         int64             `json:"ordering_weight"`
	IconURL                string            `json:"icon_url"`
	ApplicableSetting      ApplicableSetting `json:"applicable_setting"`
	LimitationSetting      LimitationSetting `json:"limitation_setting"`
	InputSetting           json.RawMessage   `json:"input_setting"`
	SectionTemplateSetting json.RawMessage   `json:"section_template_setting"`
	PresentationSettings   json.RawMessage   `json:"presentation_settings"`
	Hidden                 bool              `json:"hidden"`
}

// Identifier implements the revision.Extension interface.
//

func (e EmailSectionDeclaration) Identifier() revision.ExtensionIdentifier {
	return revision.ExtensionIdentifier{
		Type:   EmailSectionExtensionType,
		ID:     e.ID,
		Handle: e.Handle,
	}
}

// GetValue implements the revision.Extension interface
// Returns the configuration value of the email section declaration, excluding metadata.
//

func (e EmailSectionDeclaration) GetValue() any {
	return EmailSectionDeclarationValue{
		Type:                   e.Type,
		Name:                   e.Name,
		Description:            e.Description,
		Category:               e.Category,
		OrderingWeight:         e.OrderingWeight,
		IconURL:                e.IconURL,
		ApplicableSetting:      e.ApplicableSetting,
		LimitationSetting:      e.LimitationSetting,
		InputSetting:           e.InputSetting,
		SectionTemplateSetting: e.SectionTemplateSetting,
		PresentationSettings:   e.PresentationSettings,
		Hidden:                 e.Hidden,
	}
}

// Domain Business Logic Methods (following DDD principles)

// CanBeCreated checks if the email section declaration can be created.
func (e *EmailSectionDeclaration) CanBeCreated() error {
	if e.ID == "" {
		return errors.New("id cannot be empty")
	}

	if e.Handle == "" {
		return errors.New("handle cannot be empty")
	}

	if e.Name == "" {
		return errors.New("name cannot be empty")
	}

	if e.Type == "" {
		return errors.New("type cannot be empty")
	}

	// Add additional business rules here
	// For example: validate format, check uniqueness constraints, etc.

	return nil
}

// CanBeDeleted checks if the email section declaration can be deleted.
func (e *EmailSectionDeclaration) CanBeDeleted() error {
	if e.DeletedAt != nil {
		return errors.New("email section declaration is already deleted")
	}

	// Add additional business rules here
	// For example: check if it's being used by other entities

	return nil
}

// CanBeUpdated checks if the email section declaration can be updated.
func (e *EmailSectionDeclaration) CanBeUpdated() error {
	if e.DeletedAt != nil {
		return errors.New("cannot update deleted email section declaration")
	}

	return nil
}

// UpdateName updates the name with business validation.
func (e *EmailSectionDeclaration) UpdateName(newName string) error {
	if err := e.CanBeUpdated(); err != nil {
		return err
	}

	if newName == "" {
		return errors.New("name cannot be empty")
	}

	e.Name = newName
	now := time.Now()
	e.UpdatedAt = &now

	return nil
}

// UpdateDescription updates the description with business validation.
func (e *EmailSectionDeclaration) UpdateDescription(newDescription string) error {
	if err := e.CanBeUpdated(); err != nil {
		return err
	}

	e.Description = newDescription
	now := time.Now()
	e.UpdatedAt = &now

	return nil
}

// UpdateCategory updates the category with business validation.
func (e *EmailSectionDeclaration) UpdateCategory(newCategory string) error {
	if err := e.CanBeUpdated(); err != nil {
		return err
	}

	e.Category = newCategory
	now := time.Now()
	e.UpdatedAt = &now

	return nil
}

// UpdateIconURL updates the icon URL with business validation.
func (e *EmailSectionDeclaration) UpdateIconURL(newIconURL string) error {
	if err := e.CanBeUpdated(); err != nil {
		return err
	}

	e.IconURL = newIconURL
	now := time.Now()
	e.UpdatedAt = &now

	return nil
}

// UpdateApplicableSetting updates the applicable setting with business validation.
func (e *EmailSectionDeclaration) UpdateApplicableSetting(newSetting ApplicableSetting) error {
	if err := e.CanBeUpdated(); err != nil {
		return err
	}

	e.ApplicableSetting = newSetting
	now := time.Now()
	e.UpdatedAt = &now

	return nil
}

// UpdateLimitationSetting updates the limitation setting with business validation.
func (e *EmailSectionDeclaration) UpdateLimitationSetting(newSetting LimitationSetting) error {
	if err := e.CanBeUpdated(); err != nil {
		return err
	}

	e.LimitationSetting = newSetting
	now := time.Now()
	e.UpdatedAt = &now

	return nil
}

// UpdateInputSetting updates the input setting with business validation.
func (e *EmailSectionDeclaration) UpdateInputSetting(newSetting json.RawMessage) error {
	if err := e.CanBeUpdated(); err != nil {
		return err
	}

	e.InputSetting = newSetting
	now := time.Now()
	e.UpdatedAt = &now

	return nil
}

// UpdateSectionTemplateSetting updates the section template setting with business validation.
func (e *EmailSectionDeclaration) UpdateSectionTemplateSetting(newSetting json.RawMessage) error {
	if err := e.CanBeUpdated(); err != nil {
		return err
	}

	e.SectionTemplateSetting = newSetting
	now := time.Now()
	e.UpdatedAt = &now

	return nil
}

// UpdatePresentationSettings updates the presentation settings with business validation.
func (e *EmailSectionDeclaration) UpdatePresentationSettings(newSettings json.RawMessage) error {
	if err := e.CanBeUpdated(); err != nil {
		return err
	}

	e.PresentationSettings = newSettings
	now := time.Now()
	e.UpdatedAt = &now

	return nil
}

// UpdateOrderingWeight updates the ordering weight with business validation.
func (e *EmailSectionDeclaration) UpdateOrderingWeight(newWeight int64) error {
	if err := e.CanBeUpdated(); err != nil {
		return err
	}

	e.OrderingWeight = newWeight
	now := time.Now()
	e.UpdatedAt = &now

	return nil
}

// MarkAsDeleted performs soft delete with business validation.
func (e *EmailSectionDeclaration) MarkAsDeleted() error {
	if err := e.CanBeDeleted(); err != nil {
		return err
	}

	now := time.Now()
	e.DeletedAt = &now

	return nil
}

// ValidateForCreation validates the email section declaration for creation.
func (e *EmailSectionDeclaration) ValidateForCreation() error {
	if e.HostProductCode == "" {
		return errors.New("host product code is required")
	}

	if e.Handle == "" {
		return errors.New("handle is required")
	}

	if e.Type == "" {
		return errors.New("type is required")
	}

	if e.Name == "" {
		return errors.New("name is required")
	}

	// Add more business validation rules as needed

	return nil
}
