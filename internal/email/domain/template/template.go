package template

import (
	"crypto/md5" //nolint:gosec // Just for checksum.
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"time"

	"github.com/samber/lo"
	"golang.org/x/text/language"

	"github.com/AfterShip/library-heytea-go-common/xerrors"
	"github.com/AfterShip/library-marketing-go/tenantscope"
)

const (
	ActivationModeLanguageMatch = "language_match"
)

type TemplateGroup struct {
	Id                   string                   `json:"id"`
	TenantScope          *tenantscope.TenantScope `json:"tenant_scope"`
	HostProductCode      string                   `json:"host_product_code"       validate:"required"`
	PresentationSettings json.RawMessage          `json:"presentation_settings"`
	// DefaultVariantId refers to TemplateGroupVariants field. If none of the TemplateGroupVariant can be
	// activated according to TemplateGroupVariant.ActivateConditionId, the DefaultVariantId
	// will be chosen. When kept empty, we assume no default option is configured and
	// thus no variant shall be chosen in the default case.
	DefaultVariantId      string                 `json:"default_variant_id"`
	TemplateGroupVariants []TemplateGroupVariant `json:"template_group_variants"`
	// SpamReviewId keeps the latest spam review record for the template group.
	// The risk management service keeps a full history of all the spam reviews.
	SpamReviewId string    `json:"spam_review_id"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

func (group *TemplateGroup) CollectTemplateVariantIds() []string {
	var ids []string
	for _, variant := range group.TemplateGroupVariants {
		ids = append(ids, variant.TemplateVariantId)
	}
	return ids
}

func (group *TemplateGroup) MultilingualVariants() bool {
	var multilingualVariants []string
	for _, v := range group.TemplateGroupVariants {
		if v.ActivateCondition != nil && v.ActivateCondition.Mode == ActivationModeLanguageMatch {
			multilingualVariants = append(multilingualVariants, v.TemplateVariantId)
		}
	}
	return len(multilingualVariants) > 1 // If there are more than one multilingual variants, return true.
}

type VariantActivationFactors struct {
	// LanguageCodes is the list of language codes that the user prefers.
	// The order of the language codes is important, the earlier the language code appears, the more preferred it is.
	LanguageCodes []string
}

type cgvList []TemplateGroupVariant

func (s cgvList) Len() int {
	return len(s)
}

func (s cgvList) Less(i, j int) bool {
	return s[i].OrderIndex < s[j].OrderIndex
}

func (s cgvList) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}

func (group *TemplateGroup) PickVariantIdByFactors(factors VariantActivationFactors) (string, error) {
	if len(group.TemplateGroupVariants) == 0 {
		return "", xerrors.Wrap(errors.New("cannot pick variant because the config is empty"))
	}
	// scan for unknown activation modes
	for _, v := range group.TemplateGroupVariants {
		if v.ActivateCondition != nil && v.ActivateCondition.Mode != ActivationModeLanguageMatch {
			return "", xerrors.Wrap(
				errors.New("cannot pick variant because the config contains unknown activation mode"),
			)
		}
	}

	// Sort the variants by their `OrderIndex` field.
	list := cgvList(group.TemplateGroupVariants)
	sort.Sort(list)

	// We support multiple activation modes, all modes will compete for the smallest activated index.
	var m1, m2 = -1, -1

	// Mode 1: no condition
	for i, v := range list {
		if v.ActivateCondition == nil {
			m1 = i
			break
		}
	}

	// Mode 2: language match
	tags, err := deriveVariantLanguages(list)
	if err != nil {
		return "", xerrors.Wrap(err)
	}
	for _, languageCode := range factors.LanguageCodes {
		_, i, c := language.NewMatcher(tags).Match(language.Make(languageCode))
		if c >= language.High &&
			(list[i].ActivateCondition != nil && list[i].ActivateCondition.Mode == ActivationModeLanguageMatch) {
			m2 = i
			break
		}
	}

	// complete for the smallest activated index
	modes := []int{m1, m2}
	avail := lo.Filter(modes, func(i int, _ int) bool { return i >= 0 })
	switch {
	case len(avail) == 0 && group.DefaultVariantId == "":
		return "", xerrors.Wrap(
			errors.New("cannot pick variant because no variant is activated and no default variant is configured"),
		)
	case len(avail) == 0:
		return group.DefaultVariantId, nil
	default:
		minIndex := lo.Min(avail)
		return list[minIndex].TemplateVariantId, nil
	}
}

// deriveVariantLanguages derives the language tags of the variants.
// The returned tag list will be the same length and order as the input variants.
// If a variant does not have an activation condition, or the activation condition is not a language match mode,
// the corresponding tag will be language.Und.
func deriveVariantLanguages(variants []TemplateGroupVariant) ([]language.Tag, error) {
	var tags []language.Tag
	for _, v := range variants {
		if v.ActivateCondition == nil {
			tags = append(tags, language.Und)
			continue
		}

		if v.ActivateCondition.Mode == ActivationModeLanguageMatch {
			tag, err := language.Parse(v.ActivateCondition.LanguageCode)
			if err != nil {
				return nil, xerrors.Wrap(err, xerrors.Field("language_code", v.ActivateCondition.LanguageCode))
			}
			tags = append(tags, tag)
			continue
		}

		tags = append(tags, language.Und)
	}
	return tags, nil
}

type ActivationCondition struct {
	Mode string `json:"mode"          validate:"oneof=language_match"` // enum: language_match
	// LanguageCode is an ISO 639 two-letter language code optionally followed by an
	// underscore followed by an ISO 3166 code. Examples are "en" for English or
	// "zh_CN" for Simplified Chinese.
	LanguageCode string `json:"language_code"`
}

// TemplateGroupVariant keeps the relationship between a TemplateGroup and its TemplateVariant
// objects.
// It is an error for a TemplateGroup to have multiple TemplateGroupVariant objects and some
// of them do not have ActivateConditionId.
type TemplateGroupVariant struct {
	TemplateVariantId string `json:"content_variant_id" validate:"required"`
	// OrderIndex is used to manage the order of all the variants. If multiple TemplateVariant
	// objects are activated, the one ranked higher will be used;
	OrderIndex int `json:"order_index"`
	// ActivateCondition defines when to activate this variant. If kept empty,
	// it means the variant is always active.
	ActivateCondition *ActivationCondition `json:"activate_condition"`
}

type TemplateVariant struct {
	Id              string                   `json:"id"`
	TenantScope     *tenantscope.TenantScope `json:"tenant_scope"`
	HostProductCode string                   `json:"host_product_code"     validate:"required"`
	TemplateSource  string                   `json:"template_source"       validate:"omitempty,oneof=system_template user_generated"`
	ContentSource   string                   `json:"content_source"        validate:"omitempty,oneof=system_template user_generated"`
	// Name is a user defined name attached to the variant. Mainly used for display andsearch.
	Name string `json:"name"`
	// LanguageTag follows the string format of language.Tag.
	// NOTE: DO NOT DIRECTLY USE THIS FIELD, NOT YET!!!
	// Use GetLanguageTag() instead.
	LanguageTag          string          `json:"language_tag"`
	PresentationSettings json.RawMessage `json:"presentation_settings"`
	// EmailTemplate is used to define all the template related settings of an email
	// message. This field is filled only when MessageChannel equals email.
	EmailTemplate EmailTemplate `json:"email_template"`
	// Checksum is a digest generated using the variant' message template.
	// We can use this value to check if the variant's message template has changed.
	Checksum  string    `json:"checksum"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	parent *TemplateGroup
}

type emailCheckSumColumns struct {
	Subject           string            `json:"subject"`
	Preheader         string            `json:"preheader"`
	EmailBodyTemplate EmailBodyTemplate `json:"email_body_template"`
}

func (variant *TemplateVariant) CheckSum() (string, error) {
	raw, err := json.Marshal(emailCheckSumColumns{
		Subject:           variant.EmailTemplate.Subject,
		Preheader:         variant.EmailTemplate.Preheader,
		EmailBodyTemplate: variant.EmailTemplate.EmailBodyTemplate,
	})
	if err != nil {
		return "", xerrors.Wrap(err)
	}
	return checkSum(raw), nil
}

func (variant *TemplateVariant) SetParent(parent *TemplateGroup) {
	if parent == nil {
		panic("parent template group cannot be nil")
	}
	variant.parent = parent
}

// GetLanguageTag returns the language code of the variant with the given id.
// If the variant is found, but it does not have ActivateCondition or is not using language match mode,
// it returns the English (en) as the language code.
// If the variant is not found, it returns an error.
// If the variant is in the old version, and the parent is not set, it returns an error.
func (variant *TemplateVariant) GetLanguageTag() (string, error) {
	if variant.parent == nil {
		return "", xerrors.Wrap(errors.New("the variant is in old version, please call SetParent() first"))
	}

	// If not explicitly set, the system assumes it is English.  This is an intended behavior.
	defaultLanguageTag := language.English

	for _, v := range variant.parent.TemplateGroupVariants {
		if v.TemplateVariantId == variant.Id {
			if v.ActivateCondition == nil {
				return defaultLanguageTag.String(), nil
			}
			if v.ActivateCondition.Mode != ActivationModeLanguageMatch {
				return defaultLanguageTag.String(), nil
			}

			return v.ActivateCondition.LanguageCode, nil
		}
	}

	return "", xerrors.Wrap(
		errors.New("variant not found"),
		xerrors.Field("variant_id", variant.Id),
		xerrors.Field("template_group_id", variant.parent.Id),
	)
}

func checkSum(raw []byte) string {
	return fmt.Sprintf("%x", md5.Sum(raw)) //nolint:gosec // Just checksum.
}

type EmailTemplate struct {
	Sender            EmailSender       `json:"sender"              body:"sender"`
	ReplyTo           EmailReplyTo      `json:"reply_to"            body:"reply_to"`
	Subject           string            `json:"subject"             body:"subject"`
	Preheader         string            `json:"preheader"           body:"preheader"`
	EditorKey         string            `json:"editor_key"          body:"editor_key"          validate:"omitempty,oneof=easy_email dnd_email html_email safe_html_email"`
	EmailBodyTemplate EmailBodyTemplate `json:"email_body_template" body:"email_body_template"`
	Sections          []EmailSection    `json:"sections,omitempty"  body:"sections"`
}

func (template *EmailTemplate) IsUseEasyEmailEditor() bool {
	return template.EditorKey == "easy_email"
}

type EmailSender struct {
	// Id Refers to accounts platform's sender ID. It can be either verified or
	// unverified.
	Id string `json:"id"        body:"id"`
	// FromName is a user-friendly name that is displayed to your recipient when they
	// receive their email.
	FromName string `json:"from_name" body:"from_name"`
	Mode     string `json:"mode"      body:"mode"      validate:"omitempty,oneof=global_default custom"`

	// SenderSource is used to decide to use order sender or the product default sender email when the mode is custom.
	// If the mode is global_default, this field is ignored.
	// If the mode is custom, and this field is product_default, will use the product default sender email when sending the email.
	// If the mode is custom, and this field is empty or org_sender, will use the Id referred organization sender when sending the email.
	SenderSource string `json:"sender_source,omitempty"`
}

// EmailReplyTo if the user hits reply in their email, the reply will go to this address.
type EmailReplyTo struct {
	// Type is set to `sender` if this setting should be kept the same as EmailSender
	// Set to `custom` and use the CustomEmail field to define a different value.
	Type        string `json:"type"  body:"type"  validate:"omitempty,oneof=sender custom"`
	CustomEmail string `json:"email" body:"email"`
}

func (ert EmailReplyTo) Pick(senderEmail string) string {
	if ert.Type == "custom" {
		return ert.CustomEmail
	}
	return senderEmail
}

// EmailBodyTemplate keeps the template settings of the email body. We support different
// types of email editors, each editor keeps a set of separate data.
type EmailBodyTemplate struct {
	EasyEmail     EasyEmailTemplate `json:"easy_email"      body:"easy_email"`
	DndEmail      DndEmailTemplate  `json:"dnd_email"       body:"dnd_email"`
	HTMLEmail     HTMLEmailTemplate `json:"html_email"      body:"html_email"`
	SafeHTMLEmail HTMLEmailTemplate `json:"safe_html_email" body:"safe_html_email"`
}

type EasyEmailTemplate struct {
	Data       json.RawMessage `json:"data,omitempty"       body:"data"`
	Type       string          `json:"type"                 body:"type"`
	Title      string          `json:"title"                body:"title"`
	Children   json.RawMessage `json:"children,omitempty"   body:"children"`
	Attributes json.RawMessage `json:"attributes,omitempty" body:"attributes"`
}

type DndEmailTemplate struct {
	Blocks json.RawMessage `json:"blocks" body:"blocks"`
	Style  json.RawMessage `json:"style"  body:"style"`
	Font   json.RawMessage `json:"font"   body:"font"`
}

type HTMLEmailTemplate struct {
	Content string `json:"content" body:"content"`
}

type EmailSection struct {
	// Type a template section.
	// It is used to identify a template section by the user.
	// Usually it should be snaked, lowercase, and length not exceed 128
	// example. image_and_text, product_recommendation, etc.
	Type string `json:"type" body:"type"`
}

type Attachment struct {
	Content     string `json:"content"`
	Disposition string `json:"disposition"`
	Filename    string `json:"filename"`
	Type        string `json:"type"`
}
