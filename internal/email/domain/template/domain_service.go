package template

import (
	"context"
	"regexp"

	"github.com/AfterShip/library-heytea-go-common/sync/errgroup"
	"github.com/AfterShip/pltf-nf-message/internal/commoncontent/sharedkernel"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"
)

const (
	MergeTagRegex = `\*\|[A-Z0-9_]+\|\*`
)

type DomainService interface {
	RenderTemplateToContent(
		ctx context.Context,
		params RenderTemplateToContentParams,
	) (RenderTemplateToContentResult, error)
}

type RenderTemplateToContentParams struct {
	OrganizationID           string
	HostProductCode          string
	AppPlatform              string
	AppKey                   string
	EmailTemplate            EmailTemplate
	LanguageTag              *string
	Data                     map[string]any
	RenderSettings           sharedkernel.RenderSettingAggregate
	EmailSectionDeclarations []sectiondeclaration.EmailSectionDeclaration
	MergeTagDeclarations     []sharedkernel.MergeTagDeclaration
	UnsubscribeURL           string
}

type RenderTemplateToContentResult struct {
	Subject     string
	Body        string
	Attachments []Attachment
}
type domainService struct {
	preRenderPort PreRenderPort
	renderEngine  RenderEngine
}

func NewDomainService(preRenderPort PreRenderPort, renderEngine RenderEngine) DomainService {
	return &domainService{
		preRenderPort: preRenderPort,
		renderEngine:  renderEngine,
	}
}

type PreRenderPort interface {
	PreRender(ctx context.Context, params PreRenderParams) (string, error)
}

func (svc *domainService) RenderTemplateToContent(
	ctx context.Context,
	params RenderTemplateToContentParams,
) (RenderTemplateToContentResult, error) {
	// Step1: pre render email template to django template.
	preRenderedTemplate, err := svc.preRenderPort.PreRender(ctx, PreRenderParams{
		EmailTemplate:            params.EmailTemplate,
		EmailSectionDeclarations: params.EmailSectionDeclarations,
	})
	if err != nil {
		return RenderTemplateToContentResult{}, err
	}

	// Step2: transpile merge tags from placeholder like '*|NAME|*' to template language like '{{ name }}' in all template parts
	var (
		subjectRawTemplate string
		bodyRawTemplate    string
	)
	eg := errgroup.WithContext(ctx)
	eg.Go(func(ctx context.Context) error {
		subjectRawTemplate, err = transpileMergeTags(params.EmailTemplate.Subject, params.MergeTagDeclarations)
		if err != nil {
			return err
		}
		return nil
	})
	eg.Go(func(ctx context.Context) error {
		bodyRawTemplate, err = transpileMergeTags(preRenderedTemplate, params.MergeTagDeclarations)
		if err != nil {
			return err
		}
		return nil
	})
	if err := eg.Wait(); err != nil {
		return RenderTemplateToContentResult{}, err
	}

	// Step3: build render context and register template functions
	languageTag := "en"
	if params.LanguageTag != nil {
		languageTag = *params.LanguageTag
	}
	renderContext := RenderContext{
		Language: Language{
			Tag: languageTag,
		},
		Data:              params.Data,
		UnsubscribeURL:    params.UnsubscribeURL,
		RenderSettings:    params.RenderSettings.ToMap(),
		TemplateFunctions: map[string]any{},
	}
	registerTemplateFunctions(renderContext)

	// Step4: render django template with render context to email content
	var (
		emailSubject     string
		emailBody        string
		emailAttachments []Attachment
	)
	eg.Go(func(ctx context.Context) error {
		emailSubject, err = svc.renderEngine.RenderToText(ctx, subjectRawTemplate, renderContext)
		if err != nil {
			return err
		}
		return nil
	})
	eg.Go(func(ctx context.Context) error {
		emailBody, err = svc.renderEngine.RenderToHTML(ctx, bodyRawTemplate, renderContext)
		if err != nil {
			return err
		}
		return nil
	})
	if err := eg.Wait(); err != nil {
		return RenderTemplateToContentResult{}, err
	}

	return RenderTemplateToContentResult{
		Subject:     emailSubject,
		Body:        emailBody,
		Attachments: emailAttachments,
	}, nil
}

// transpileMergeTags transpiles merge tag placeholder like '*|NAME|*' to template language like '{{ name }}' based on merge tag declarations.
//
//nolint:unparam // It is a valid check.
func transpileMergeTags(template string, mergeTagDeclarations []sharedkernel.MergeTagDeclaration) (string, error) {
	mergeTagContext := make(map[string]string)
	for _, mergeTag := range mergeTagDeclarations {
		mergeTagContext[mergeTag.ToTag()] = mergeTag.ToRawDjango()
	}
	replaceFunc := func(tag string) string {
		lc, ok := mergeTagContext[tag]
		if ok {
			return lc
		}
		return tag
	}

	re := regexp.MustCompile(MergeTagRegex)
	return re.ReplaceAllStringFunc(template, replaceFunc), nil
}
