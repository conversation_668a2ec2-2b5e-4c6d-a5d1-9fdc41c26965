package template

import (
	"context"

	"github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"
)

type PreRenderParams struct {
	EmailTemplate            EmailTemplate
	EmailSectionDeclarations []sectiondeclaration.EmailSectionDeclaration
}

type PreRenderedTemplate struct {
	Subject string
	Body    string
}

type RenderEngine interface {
	// RenderToText renders the render engine template to text.
	RenderToText(ctx context.Context, template string, renderContext RenderContext) (string, error)

	// RenderToHTML renders the render engine template to html.
	RenderToHTML(ctx context.Context, template string, renderContext RenderContext) (string, error)
}
