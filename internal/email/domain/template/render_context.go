package template

import "golang.org/x/text/language"

type RenderContext struct {
	// Language is the language of the content.
	Language Language `json:"language,omitempty" template:"language"`

	// Data is the dynamic data used by the engine to render the content.
	// The data can be used by the engine template functions or merge tag.
	Data map[string]any `json:"data,omitempty" template:"data"`

	// RenderSettings contains the settings for the rendering process.
	// The render engine is not aware of the settings, so it is just a map of key-value pairs.
	// The BU can use the settings to control the rendering process in the content template or merge tag.
	// e.g. footer address, remove powered by, hide unsubscribe link, etc.
	RenderSettings map[string]any `json:"render_settings,omitempty" template:"render_settings"`

	// TemplateFunctions contains the functions that can be used in the template.
	TemplateFunctions map[string]any `json:"template_functions,omitempty" template:"template_functions"`

	UnsubscribeURL string `json:"unsubscribe_url,omitempty" template:"unsubscribe_url"`

	// AttachmentsResult stores the attachments that are generated in the rendering process.
	AttachmentsResult []Attachment `json:"attachments,omitempty" template:"attachments"`
}

type Language struct {
	Tag string `json:"tag"`
}

func registerTemplateFunctions(renderCtx RenderContext) {
	newUnsubscribeBtnTextExec(renderCtx, supportedLocales(), language.AmericanEnglish).Register(renderCtx)
}
