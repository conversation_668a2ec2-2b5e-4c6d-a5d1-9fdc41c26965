package template

import (
	"go.uber.org/fx"
)

type Params struct {
	fx.In

	PreRenderPort PreRenderPort
	RenderEngine  RenderEngine
}

type Result struct {
	fx.Out

	DomainService DomainService
}

func New(p Params) Result {
	return Result{
		DomainService: NewDomainService(p.PreRenderPort, p.RenderEngine),
	}
}

const moduleName = "template_domain"

var Module = fx.Module(moduleName,
	fx.Provide(New))
