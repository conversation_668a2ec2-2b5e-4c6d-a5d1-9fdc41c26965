//nolint:testpackage // It is a valid check.
package template

import (
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/AfterShip/pltf-nf-message/internal/commoncontent/sharedkernel"
)

func Test_transpileMergeTags(t *testing.T) {
	mergeTagDeclarations := []sharedkernel.MergeTagDeclaration{
		{
			MergeTag: "*|TRACKING_URL|*",
			MergeLogic: sharedkernel.MergeLogic{
				Mode:  sharedkernel.MergeModeContextPath,
				Value: "tracking.tracking_url",
			},
		},
	}
	template := "tracking_url: *|TRACKING_URL|*"
	res, err := transpileMergeTags(template, mergeTagDeclarations)
	require.NoError(t, err)
	require.Equal(t, "tracking_url: {{ tracking.tracking_url }}", res)
}
