package template_test

import (
	"context"
	"encoding/json"
	"fmt"
	"os"

	"github.com/AfterShip/pltf-nf-message/internal/adapters/renderengine"
	"github.com/AfterShip/pltf-nf-message/internal/commoncontent/sharedkernel"
	"github.com/AfterShip/pltf-nf-message/internal/email/adapters/prerender"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/template"
)

func Example_renderTemplateToContent() {
	preRenderPort := prerender.NewPreRenderer()
	renderEngine := renderengine.NewDjangoEngine()
	domainService := template.NewDomainService(preRenderPort, renderEngine)

	templateJsonData, err := os.ReadFile("testdata/test.json")
	if err != nil {
		panic(err)
	}

	var emailTemplate template.EmailTemplate
	err = json.Unmarshal(templateJsonData, &emailTemplate)
	if err != nil {
		panic(err)
	}

	res, err := domainService.RenderTemplateToContent(context.Background(), template.RenderTemplateToContentParams{
		EmailTemplate: emailTemplate,
		LanguageTag:   nil,
		Data: map[string]any{
			"tracking": map[string]any{
				"tracking_url": "https://www.example.com",
			},
		},
		RenderSettings:           sharedkernel.RenderSettingAggregate{},
		EmailSectionDeclarations: nil,
		MergeTagDeclarations: []sharedkernel.MergeTagDeclaration{
			{
				MergeTag: "*|TRACKING_URL|*",
				MergeLogic: sharedkernel.MergeLogic{
					Mode:  sharedkernel.MergeModeContextPath,
					Value: "tracking.tracking_url",
				},
			},
			{
				MergeTag: "*|UNSUBSCRIBE_URL|*",
				MergeLogic: sharedkernel.MergeLogic{
					Mode:  sharedkernel.MergeModeContextPath,
					Value: "unsubscribe_url",
				},
			},
		},
		UnsubscribeURL: "https://www.example.com",
	})
	if err != nil {
		fmt.Println(err)
	}

	fmt.Println(res.Body)
	// >Output:
}
