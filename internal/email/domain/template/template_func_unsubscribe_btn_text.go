package template

import (
	"github.com/pkg/errors"
	"golang.org/x/text/language"
)

var unsubscribeTranslateMap = map[language.Tag]string{
	language.AmericanEnglish:     "Unsubscribe",
	language.Portuguese:          "Cancelar inscrição",
	language.French:              "<PERSON> désabonner",
	language.Spanish:             "Darse de baja",
	language.German:              "Abmelden",
	language.Swedish:             "säga upp",
	language.Italian:             "Annulla l'iscrizione",
	language.Japanese:            "配信停止",
	language.Turkish:             "Üyelikten ayrıl",
	language.SimplifiedChinese:   "取消订阅",
	language.Dutch:               "Afmelden",
	language.Norwegian:           "Avslutt abonnementet",
	language.Korean:              "구독 취소",
	language.Czech:               "Odhlásit odběr",
	language.Greek:               "Διαγραφείτε",
	language.Polish:              "Odsubskrybuj",
	language.Lithuanian:          "atšaukti prenumeratą",
	language.Hungarian:           "Leiratkozás",
	language.Croatian:            "otkaži poruke",
	language.TraditionalChinese:  "取消訂閱",
	language.Romanian:            "<PERSON><PERSON><PERSON><PERSON>",
	language.Latvian:             "Atrakstīties",
	language.BrazilianPortuguese: "Cancelar inscrição",
	language.Danish:              "Afmeld",
	language.Hebrew:              "ביטול רישום",
	language.Estonian:            "Loobu tellimusest",
	language.Slovenian:           "Odjava",
	language.Slovak:              "Zrušiť odber",
	language.Finnish:             "Peruuta tilaus",
	language.Bulgarian:           "Отписване",
	language.Ukrainian:           "Відписатися",
	language.Russian:             "Отписаться",
	language.Arabic:              "إلغاء الاشتراك",
	language.Thai:                "ยกเลิกการสมัคร",
	language.Vietnamese:          "Hủy đăng ký",
	language.Indonesian:          "Berhenti berlangganan",
	language.Malay:               "Berhenti berlangganan",
}

func supportedLocales() []language.Tag {
	locales := make([]language.Tag, 0, len(unsubscribeTranslateMap))
	for k := range unsubscribeTranslateMap {
		locales = append(locales, k)
	}
	return locales
}

type unsubscribeBtnTextDef struct {
	supportedLocales []language.Tag
	matcher          language.Matcher
	fallbackLanguage language.Tag
}
type unsubscribeBtnTextExec struct {
	*unsubscribeBtnTextDef
	renderCtx RenderContext
}

func newUnsubscribeBtnTextExec(
	renderCtx RenderContext,
	supported []language.Tag,
	fallback language.Tag,
) *unsubscribeBtnTextExec {
	defaultMatcher := language.NewMatcher(supported)
	return &unsubscribeBtnTextExec{
		unsubscribeBtnTextDef: &unsubscribeBtnTextDef{
			supportedLocales: supported,
			matcher:          defaultMatcher,
			fallbackLanguage: fallback,
		},
		renderCtx: renderCtx,
	}
}

func (e *unsubscribeBtnTextExec) translateTo(lang Language) (string, error) {
	// match the language tag to the supported locales
	_, index, c := e.matcher.Match(language.Make(lang.Tag))
	switch c {
	case language.Exact, language.High:
		// return the translation
		key := e.supportedLocales[index]
		return unsubscribeTranslateMap[key], nil
	case language.Low, language.No:
		// fallback to the default language
		return unsubscribeTranslateMap[e.fallbackLanguage], nil
	}

	return "", errors.Errorf("unexpected confidence level: %v", c)
}

func (e *unsubscribeBtnTextExec) Key() string {
	return "unsubscribe_btn_text"
}

func (e *unsubscribeBtnTextExec) run() (string, error) {
	return e.translateTo(e.renderCtx.Language)
}

func (e *unsubscribeBtnTextExec) Register(renderCtx RenderContext) {
	renderCtx.TemplateFunctions[e.Key()] = e.run
}
