package commoncontentapplication_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/pltf-nf-infra/pkg/version"
	commoncontentapplication "github.com/AfterShip/pltf-nf-message/internal/commoncontent/application"
	mergetagdeclarationdomain "github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/mergetagdeclaration"
)

func TestVersionManagedMergeTagImpl_CreateMergeTag(t *testing.T) {
	// Setup
	mockRevisionPort := commoncontentapplication.NewMockRevisionPort(t)
	mockVersionAgent := &MockVersionAgent[mergetagdeclarationdomain.MergeTagDeclarationValue]{}

	uc := commoncontentapplication.NewVersionManagedMergeTag(mockRevisionPort, mockVersionAgent)

	// Test data
	args := commoncontentapplication.CreateVersionManagedArgs{
		HostProductCode: "test-product",
		Handle:          "test-handle",
		Name:            "Test Merge Tag",
		Description:     "Test description",
		Category:        "test",
		MergeTag:        "*|TEST_TAG|*",
		MergeLogic: mergetagdeclarationdomain.MergeLogic{
			Mode:  mergetagdeclarationdomain.MergeModeContextPath,
			Value: "test.value",
		},
		ApplicableSetting: mergetagdeclarationdomain.ApplicableSetting{
			MessageChannels: []string{"email"},
		},
		OrderingWeight: 100,
		OperatorEmail:  "<EMAIL>",
	}

	expectedResult := commoncontentapplication.VersionManagedMergeTagDeclaration{
		Extension: mergetagdeclarationdomain.MergeTagDeclaration{
			ID:              "test-id",
			HostProductCode: args.HostProductCode,
			Handle:          args.Handle,
			Name:            args.Name,
			Description:     args.Description,
			Category:        args.Category,
			MergeTag:        args.MergeTag,
			MergeLogic:      args.MergeLogic,
			Hidden:          false,
			CreatedAt:       time.Now(),
		},
	}

	// Mock expectations - need to match the actual MergeTag that will be created
	mockRevisionPort.EXPECT().CreateMergeTag(
		context.Background(),
		MatchCreateMergeTagPortArgs(args),
	).Return(expectedResult, nil)

	// Execute
	result, err := uc.CreateMergeTag(context.Background(), args)

	// Verify
	require.NoError(t, err)
	assert.Equal(t, expectedResult.Extension.ID, result.Extension.ID)
	assert.Equal(t, expectedResult.Extension.Name, result.Extension.Name)
	assert.Equal(t, expectedResult.Extension.Handle, result.Extension.Handle)
}

func TestVersionManagedMergeTagImpl_CreateMergeTag_ValidationError(t *testing.T) {
	// Setup
	mockRevisionPort := commoncontentapplication.NewMockRevisionPort(t)
	mockVersionAgent := &MockVersionAgent[mergetagdeclarationdomain.MergeTagDeclarationValue]{}

	uc := commoncontentapplication.NewVersionManagedMergeTag(mockRevisionPort, mockVersionAgent)

	// Test data with missing required fields
	args := commoncontentapplication.CreateVersionManagedArgs{
		// Missing required fields to trigger validation error
		OperatorEmail: "invalid-email", // Invalid email format
	}

	// Execute
	_, err := uc.CreateMergeTag(context.Background(), args)

	// Verify
	require.Error(t, err)
	assert.Contains(t, err.Error(), "invalid create merge tag arguments")
}

type MockVersionAgent[T any] struct{}

func (m *MockVersionAgent[T]) Release(ctx context.Context, args version.ReleaseArgs[T]) error {
	return nil
}

// MatchCreateMergeTagPortArgs creates a mock matcher for CreateMergeTagPortArgs.
func MatchCreateMergeTagPortArgs(args commoncontentapplication.CreateVersionManagedArgs) any {
	return mock.MatchedBy(func(portArgs commoncontentapplication.CreateMergeTagPortArgs) bool {
		// Check basic fields
		if portArgs.HostProductCode != args.HostProductCode ||
			portArgs.CreatedBy != args.OperatorEmail {
			return false
		}

		// Check the MergeTag domain entity fields
		mergeTag := portArgs.MergeTag
		if mergeTag.HostProductCode != args.HostProductCode ||
			mergeTag.Handle != args.Handle ||
			mergeTag.Name != args.Name ||
			mergeTag.Description != args.Description ||
			mergeTag.Category != args.Category ||
			mergeTag.MergeTag != args.MergeTag ||
			mergeTag.OrderingWeight != args.OrderingWeight ||
			mergeTag.Hidden != false {
			return false
		}

		// Check MergeLogic
		if mergeTag.MergeLogic.Mode != args.MergeLogic.Mode ||
			mergeTag.MergeLogic.Value != args.MergeLogic.Value {
			return false
		}

		// Check ApplicableSetting
		if len(mergeTag.ApplicableSetting.MessageChannels) != len(args.ApplicableSetting.MessageChannels) {
			return false
		}
		for i, channel := range mergeTag.ApplicableSetting.MessageChannels {
			if channel != args.ApplicableSetting.MessageChannels[i] {
				return false
			}
		}

		// Check that ID is generated (not empty) and CreatedAt is recent
		if mergeTag.ID == "" {
			return false
		}

		// Allow some tolerance for CreatedAt (within the last minute)
		if time.Since(mergeTag.CreatedAt) > time.Minute {
			return false
		}

		return true
	})
}
