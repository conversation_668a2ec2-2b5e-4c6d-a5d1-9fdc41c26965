// Code generated by mockery v2.52.3. DO NOT EDIT.

package commoncontentapplication

import (
	context "context"

	mergetagdeclaration "github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/mergetagdeclaration"
	mock "github.com/stretchr/testify/mock"

	revision "github.com/AfterShip/pltf-nf-infra/pkg/revision"
)

// MockRevisionPort is an autogenerated mock type for the RevisionPort type
type MockRevisionPort struct {
	mock.Mock
}

type MockRevisionPort_Expecter struct {
	mock *mock.Mock
}

func (_m *MockRevisionPort) EXPECT() *MockRevisionPort_Expecter {
	return &MockRevisionPort_Expecter{mock: &_m.Mock}
}

// CreateMergeTag provides a mock function with given fields: ctx, args
func (_m *MockRevisionPort) CreateMergeTag(ctx context.Context, args CreateMergeTagPortArgs) (revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CreateMergeTag")
	}

	var r0 revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, CreateMergeTagPortArgs) (revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, CreateMergeTagPortArgs) revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration]); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration])
	}

	if rf, ok := ret.Get(1).(func(context.Context, CreateMergeTagPortArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRevisionPort_CreateMergeTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateMergeTag'
type MockRevisionPort_CreateMergeTag_Call struct {
	*mock.Call
}

// CreateMergeTag is a helper method to define mock.On call
//   - ctx context.Context
//   - args CreateMergeTagPortArgs
func (_e *MockRevisionPort_Expecter) CreateMergeTag(ctx interface{}, args interface{}) *MockRevisionPort_CreateMergeTag_Call {
	return &MockRevisionPort_CreateMergeTag_Call{Call: _e.mock.On("CreateMergeTag", ctx, args)}
}

func (_c *MockRevisionPort_CreateMergeTag_Call) Run(run func(ctx context.Context, args CreateMergeTagPortArgs)) *MockRevisionPort_CreateMergeTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(CreateMergeTagPortArgs))
	})
	return _c
}

func (_c *MockRevisionPort_CreateMergeTag_Call) Return(_a0 revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], _a1 error) *MockRevisionPort_CreateMergeTag_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRevisionPort_CreateMergeTag_Call) RunAndReturn(run func(context.Context, CreateMergeTagPortArgs) (revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error)) *MockRevisionPort_CreateMergeTag_Call {
	_c.Call.Return(run)
	return _c
}

// GetMergeTag provides a mock function with given fields: ctx, args
func (_m *MockRevisionPort) GetMergeTag(ctx context.Context, args GetMergeTagPortArgs) (revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetMergeTag")
	}

	var r0 revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, GetMergeTagPortArgs) (revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, GetMergeTagPortArgs) revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration]); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration])
	}

	if rf, ok := ret.Get(1).(func(context.Context, GetMergeTagPortArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRevisionPort_GetMergeTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMergeTag'
type MockRevisionPort_GetMergeTag_Call struct {
	*mock.Call
}

// GetMergeTag is a helper method to define mock.On call
//   - ctx context.Context
//   - args GetMergeTagPortArgs
func (_e *MockRevisionPort_Expecter) GetMergeTag(ctx interface{}, args interface{}) *MockRevisionPort_GetMergeTag_Call {
	return &MockRevisionPort_GetMergeTag_Call{Call: _e.mock.On("GetMergeTag", ctx, args)}
}

func (_c *MockRevisionPort_GetMergeTag_Call) Run(run func(ctx context.Context, args GetMergeTagPortArgs)) *MockRevisionPort_GetMergeTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(GetMergeTagPortArgs))
	})
	return _c
}

func (_c *MockRevisionPort_GetMergeTag_Call) Return(_a0 revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], _a1 error) *MockRevisionPort_GetMergeTag_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRevisionPort_GetMergeTag_Call) RunAndReturn(run func(context.Context, GetMergeTagPortArgs) (revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error)) *MockRevisionPort_GetMergeTag_Call {
	_c.Call.Return(run)
	return _c
}

// ListMergeTags provides a mock function with given fields: ctx, args
func (_m *MockRevisionPort) ListMergeTags(ctx context.Context, args ListMergeTagsPortArgs) ([]revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for ListMergeTags")
	}

	var r0 []revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ListMergeTagsPortArgs) ([]revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ListMergeTagsPortArgs) []revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration]); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ListMergeTagsPortArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRevisionPort_ListMergeTags_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListMergeTags'
type MockRevisionPort_ListMergeTags_Call struct {
	*mock.Call
}

// ListMergeTags is a helper method to define mock.On call
//   - ctx context.Context
//   - args ListMergeTagsPortArgs
func (_e *MockRevisionPort_Expecter) ListMergeTags(ctx interface{}, args interface{}) *MockRevisionPort_ListMergeTags_Call {
	return &MockRevisionPort_ListMergeTags_Call{Call: _e.mock.On("ListMergeTags", ctx, args)}
}

func (_c *MockRevisionPort_ListMergeTags_Call) Run(run func(ctx context.Context, args ListMergeTagsPortArgs)) *MockRevisionPort_ListMergeTags_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ListMergeTagsPortArgs))
	})
	return _c
}

func (_c *MockRevisionPort_ListMergeTags_Call) Return(_a0 []revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], _a1 error) *MockRevisionPort_ListMergeTags_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRevisionPort_ListMergeTags_Call) RunAndReturn(run func(context.Context, ListMergeTagsPortArgs) ([]revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error)) *MockRevisionPort_ListMergeTags_Call {
	_c.Call.Return(run)
	return _c
}

// ResetMergeTag provides a mock function with given fields: ctx, args
func (_m *MockRevisionPort) ResetMergeTag(ctx context.Context, args ResetMergeTagPortArgs) error {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for ResetMergeTag")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, ResetMergeTagPortArgs) error); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRevisionPort_ResetMergeTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ResetMergeTag'
type MockRevisionPort_ResetMergeTag_Call struct {
	*mock.Call
}

// ResetMergeTag is a helper method to define mock.On call
//   - ctx context.Context
//   - args ResetMergeTagPortArgs
func (_e *MockRevisionPort_Expecter) ResetMergeTag(ctx interface{}, args interface{}) *MockRevisionPort_ResetMergeTag_Call {
	return &MockRevisionPort_ResetMergeTag_Call{Call: _e.mock.On("ResetMergeTag", ctx, args)}
}

func (_c *MockRevisionPort_ResetMergeTag_Call) Run(run func(ctx context.Context, args ResetMergeTagPortArgs)) *MockRevisionPort_ResetMergeTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ResetMergeTagPortArgs))
	})
	return _c
}

func (_c *MockRevisionPort_ResetMergeTag_Call) Return(_a0 error) *MockRevisionPort_ResetMergeTag_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRevisionPort_ResetMergeTag_Call) RunAndReturn(run func(context.Context, ResetMergeTagPortArgs) error) *MockRevisionPort_ResetMergeTag_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateMergeTag provides a mock function with given fields: ctx, args
func (_m *MockRevisionPort) UpdateMergeTag(ctx context.Context, args UpdateMergeTagPortArgs) (revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for UpdateMergeTag")
	}

	var r0 revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, UpdateMergeTagPortArgs) (revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, UpdateMergeTagPortArgs) revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration]); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration])
	}

	if rf, ok := ret.Get(1).(func(context.Context, UpdateMergeTagPortArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRevisionPort_UpdateMergeTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateMergeTag'
type MockRevisionPort_UpdateMergeTag_Call struct {
	*mock.Call
}

// UpdateMergeTag is a helper method to define mock.On call
//   - ctx context.Context
//   - args UpdateMergeTagPortArgs
func (_e *MockRevisionPort_Expecter) UpdateMergeTag(ctx interface{}, args interface{}) *MockRevisionPort_UpdateMergeTag_Call {
	return &MockRevisionPort_UpdateMergeTag_Call{Call: _e.mock.On("UpdateMergeTag", ctx, args)}
}

func (_c *MockRevisionPort_UpdateMergeTag_Call) Run(run func(ctx context.Context, args UpdateMergeTagPortArgs)) *MockRevisionPort_UpdateMergeTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(UpdateMergeTagPortArgs))
	})
	return _c
}

func (_c *MockRevisionPort_UpdateMergeTag_Call) Return(_a0 revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], _a1 error) *MockRevisionPort_UpdateMergeTag_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRevisionPort_UpdateMergeTag_Call) RunAndReturn(run func(context.Context, UpdateMergeTagPortArgs) (revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error)) *MockRevisionPort_UpdateMergeTag_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockRevisionPort creates a new instance of MockRevisionPort. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockRevisionPort(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockRevisionPort {
	mock := &MockRevisionPort{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
