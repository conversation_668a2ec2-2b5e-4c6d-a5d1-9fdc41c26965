package commoncontentapplication

import "errors"

var (
	ErrMergeTagNotFound         = errors.New("merge tag declaration not found")
	ErrMergeTagAlreadyExists    = errors.New("merge tag declaration already exists")
	ErrMergeTagCannotBeUpdated  = errors.New("merge tag declaration cannot be updated")
	ErrMergeTagCannotBeDeleted  = errors.New("merge tag declaration cannot be deleted")
	ErrInvalidMergeTagHandle    = errors.New("invalid merge tag handle")
	ErrInvalidHostProductCode   = errors.New("invalid host product code")
	ErrInvalidMergeLogic        = errors.New("invalid merge logic")
	ErrDuplicateMergeTag        = errors.New("duplicate merge tag")
	ErrMergeTagInUse            = errors.New("merge tag is in use and cannot be deleted")
	ErrInvalidApplicableSetting = errors.New("invalid applicable setting")
	ErrUnauthorizedOperation    = errors.New("unauthorized operation")
)
