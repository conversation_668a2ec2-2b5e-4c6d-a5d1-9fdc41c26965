// Code generated by mockery v2.52.3. DO NOT EDIT.

package commoncontentapplication

import (
	context "context"

	mergetagdeclaration "github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/mergetagdeclaration"
	mock "github.com/stretchr/testify/mock"

	revision "github.com/AfterShip/pltf-nf-infra/pkg/revision"

	version "github.com/AfterShip/pltf-nf-infra/pkg/version"
)

// MockUseCaseMergeTagManager is an autogenerated mock type for the ucVersionManagedMergeTag type
type MockUseCaseMergeTagManager struct {
	mock.Mock
}

type MockUseCaseMergeTagManager_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUseCaseMergeTagManager) EXPECT() *MockUseCaseMergeTagManager_Expecter {
	return &MockUseCaseMergeTagManager_Expecter{mock: &_m.Mock}
}

// CreateMergeTag provides a mock function with given fields: ctx, args
func (_m *MockUseCaseMergeTagManager) CreateMergeTag(ctx context.Context, args CreateVersionManagedArgs) (revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for CreateMergeTag")
	}

	var r0 revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, CreateVersionManagedArgs) (revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, CreateVersionManagedArgs) revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration]); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration])
	}

	if rf, ok := ret.Get(1).(func(context.Context, CreateVersionManagedArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUseCaseMergeTagManager_CreateMergeTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateMergeTag'
type MockUseCaseMergeTagManager_CreateMergeTag_Call struct {
	*mock.Call
}

// CreateMergeTag is a helper method to define mock.On call
//   - ctx context.Context
//   - args CreateVersionManagedArgs
func (_e *MockUseCaseMergeTagManager_Expecter) CreateMergeTag(ctx interface{}, args interface{}) *MockUseCaseMergeTagManager_CreateMergeTag_Call {
	return &MockUseCaseMergeTagManager_CreateMergeTag_Call{Call: _e.mock.On("CreateMergeTag", ctx, args)}
}

func (_c *MockUseCaseMergeTagManager_CreateMergeTag_Call) Run(run func(ctx context.Context, args CreateVersionManagedArgs)) *MockUseCaseMergeTagManager_CreateMergeTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(CreateVersionManagedArgs))
	})
	return _c
}

func (_c *MockUseCaseMergeTagManager_CreateMergeTag_Call) Return(_a0 revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], _a1 error) *MockUseCaseMergeTagManager_CreateMergeTag_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUseCaseMergeTagManager_CreateMergeTag_Call) RunAndReturn(run func(context.Context, CreateVersionManagedArgs) (revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error)) *MockUseCaseMergeTagManager_CreateMergeTag_Call {
	_c.Call.Return(run)
	return _c
}

// GetMergeTag provides a mock function with given fields: ctx, args
func (_m *MockUseCaseMergeTagManager) GetMergeTag(ctx context.Context, args GetArgs) (revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetMergeTag")
	}

	var r0 revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, GetArgs) (revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, GetArgs) revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration]); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration])
	}

	if rf, ok := ret.Get(1).(func(context.Context, GetArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUseCaseMergeTagManager_GetMergeTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMergeTag'
type MockUseCaseMergeTagManager_GetMergeTag_Call struct {
	*mock.Call
}

// GetMergeTag is a helper method to define mock.On call
//   - ctx context.Context
//   - args GetArgs
func (_e *MockUseCaseMergeTagManager_Expecter) GetMergeTag(ctx interface{}, args interface{}) *MockUseCaseMergeTagManager_GetMergeTag_Call {
	return &MockUseCaseMergeTagManager_GetMergeTag_Call{Call: _e.mock.On("GetMergeTag", ctx, args)}
}

func (_c *MockUseCaseMergeTagManager_GetMergeTag_Call) Run(run func(ctx context.Context, args GetArgs)) *MockUseCaseMergeTagManager_GetMergeTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(GetArgs))
	})
	return _c
}

func (_c *MockUseCaseMergeTagManager_GetMergeTag_Call) Return(_a0 revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], _a1 error) *MockUseCaseMergeTagManager_GetMergeTag_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUseCaseMergeTagManager_GetMergeTag_Call) RunAndReturn(run func(context.Context, GetArgs) (revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error)) *MockUseCaseMergeTagManager_GetMergeTag_Call {
	_c.Call.Return(run)
	return _c
}

// ListMergeTags provides a mock function with given fields: ctx, args
func (_m *MockUseCaseMergeTagManager) ListMergeTags(ctx context.Context, args ListArgs) ([]revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for ListMergeTags")
	}

	var r0 []revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ListArgs) ([]revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ListArgs) []revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration]); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ListArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUseCaseMergeTagManager_ListMergeTags_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListMergeTags'
type MockUseCaseMergeTagManager_ListMergeTags_Call struct {
	*mock.Call
}

// ListMergeTags is a helper method to define mock.On call
//   - ctx context.Context
//   - args ListArgs
func (_e *MockUseCaseMergeTagManager_Expecter) ListMergeTags(ctx interface{}, args interface{}) *MockUseCaseMergeTagManager_ListMergeTags_Call {
	return &MockUseCaseMergeTagManager_ListMergeTags_Call{Call: _e.mock.On("ListMergeTags", ctx, args)}
}

func (_c *MockUseCaseMergeTagManager_ListMergeTags_Call) Run(run func(ctx context.Context, args ListArgs)) *MockUseCaseMergeTagManager_ListMergeTags_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ListArgs))
	})
	return _c
}

func (_c *MockUseCaseMergeTagManager_ListMergeTags_Call) Return(_a0 []revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], _a1 error) *MockUseCaseMergeTagManager_ListMergeTags_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUseCaseMergeTagManager_ListMergeTags_Call) RunAndReturn(run func(context.Context, ListArgs) ([]revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error)) *MockUseCaseMergeTagManager_ListMergeTags_Call {
	_c.Call.Return(run)
	return _c
}

// Release provides a mock function with given fields: ctx, args
func (_m *MockUseCaseMergeTagManager) Release(ctx context.Context, args version.ReleaseArgs[mergetagdeclaration.MergeTagDeclarationValue]) error {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for Release")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, version.ReleaseArgs[mergetagdeclaration.MergeTagDeclarationValue]) error); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUseCaseMergeTagManager_Release_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Release'
type MockUseCaseMergeTagManager_Release_Call struct {
	*mock.Call
}

// Release is a helper method to define mock.On call
//   - ctx context.Context
//   - args version.ReleaseArgs[mergetagdeclaration.MergeTagDeclarationValue]
func (_e *MockUseCaseMergeTagManager_Expecter) Release(ctx interface{}, args interface{}) *MockUseCaseMergeTagManager_Release_Call {
	return &MockUseCaseMergeTagManager_Release_Call{Call: _e.mock.On("Release", ctx, args)}
}

func (_c *MockUseCaseMergeTagManager_Release_Call) Run(run func(ctx context.Context, args version.ReleaseArgs[mergetagdeclaration.MergeTagDeclarationValue])) *MockUseCaseMergeTagManager_Release_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(version.ReleaseArgs[mergetagdeclaration.MergeTagDeclarationValue]))
	})
	return _c
}

func (_c *MockUseCaseMergeTagManager_Release_Call) Return(_a0 error) *MockUseCaseMergeTagManager_Release_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUseCaseMergeTagManager_Release_Call) RunAndReturn(run func(context.Context, version.ReleaseArgs[mergetagdeclaration.MergeTagDeclarationValue]) error) *MockUseCaseMergeTagManager_Release_Call {
	_c.Call.Return(run)
	return _c
}

// ResetMergeTag provides a mock function with given fields: ctx, args
func (_m *MockUseCaseMergeTagManager) ResetMergeTag(ctx context.Context, args ResetArgs) error {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for ResetMergeTag")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, ResetArgs) error); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUseCaseMergeTagManager_ResetMergeTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ResetMergeTag'
type MockUseCaseMergeTagManager_ResetMergeTag_Call struct {
	*mock.Call
}

// ResetMergeTag is a helper method to define mock.On call
//   - ctx context.Context
//   - args ResetArgs
func (_e *MockUseCaseMergeTagManager_Expecter) ResetMergeTag(ctx interface{}, args interface{}) *MockUseCaseMergeTagManager_ResetMergeTag_Call {
	return &MockUseCaseMergeTagManager_ResetMergeTag_Call{Call: _e.mock.On("ResetMergeTag", ctx, args)}
}

func (_c *MockUseCaseMergeTagManager_ResetMergeTag_Call) Run(run func(ctx context.Context, args ResetArgs)) *MockUseCaseMergeTagManager_ResetMergeTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ResetArgs))
	})
	return _c
}

func (_c *MockUseCaseMergeTagManager_ResetMergeTag_Call) Return(_a0 error) *MockUseCaseMergeTagManager_ResetMergeTag_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUseCaseMergeTagManager_ResetMergeTag_Call) RunAndReturn(run func(context.Context, ResetArgs) error) *MockUseCaseMergeTagManager_ResetMergeTag_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateMergeTag provides a mock function with given fields: ctx, args
func (_m *MockUseCaseMergeTagManager) UpdateMergeTag(ctx context.Context, args UpdateArgs) (revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for UpdateMergeTag")
	}

	var r0 revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, UpdateArgs) (revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, UpdateArgs) revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration]); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration])
	}

	if rf, ok := ret.Get(1).(func(context.Context, UpdateArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUseCaseMergeTagManager_UpdateMergeTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateMergeTag'
type MockUseCaseMergeTagManager_UpdateMergeTag_Call struct {
	*mock.Call
}

// UpdateMergeTag is a helper method to define mock.On call
//   - ctx context.Context
//   - args UpdateArgs
func (_e *MockUseCaseMergeTagManager_Expecter) UpdateMergeTag(ctx interface{}, args interface{}) *MockUseCaseMergeTagManager_UpdateMergeTag_Call {
	return &MockUseCaseMergeTagManager_UpdateMergeTag_Call{Call: _e.mock.On("UpdateMergeTag", ctx, args)}
}

func (_c *MockUseCaseMergeTagManager_UpdateMergeTag_Call) Run(run func(ctx context.Context, args UpdateArgs)) *MockUseCaseMergeTagManager_UpdateMergeTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(UpdateArgs))
	})
	return _c
}

func (_c *MockUseCaseMergeTagManager_UpdateMergeTag_Call) Return(_a0 revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], _a1 error) *MockUseCaseMergeTagManager_UpdateMergeTag_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUseCaseMergeTagManager_UpdateMergeTag_Call) RunAndReturn(run func(context.Context, UpdateArgs) (revision.VersionManagedExtension[mergetagdeclaration.MergeTagDeclaration], error)) *MockUseCaseMergeTagManager_UpdateMergeTag_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockUseCaseMergeTagManager creates a new instance of MockUseCaseMergeTagManager. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUseCaseMergeTagManager(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUseCaseMergeTagManager {
	mock := &MockUseCaseMergeTagManager{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
