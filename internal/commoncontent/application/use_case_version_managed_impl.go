package commoncontentapplication

import (
	"context"
	"fmt"
	"time"

	"github.com/AfterShip/library-heytea-go-common/utils/uuid"
	"github.com/AfterShip/library-heytea-go-common/validator"
	"github.com/AfterShip/library-heytea-go-common/xerrors"
	"github.com/AfterShip/pltf-nf-infra/pkg/version"
	mergetagdeclarationdomain "github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/mergetagdeclaration"
)

// versionManagedMergeTagImpl implements the ucVersionManagedMergeTag interface.
type versionManagedMergeTagImpl struct {
	revisionPort RevisionPort
	versionAgent version.VersionAgent[mergetagdeclarationdomain.MergeTagDeclarationValue]
}

// NewVersionManagedMergeTag creates a new instance of the version managed merge tag use case.
func NewVersionManagedMergeTag(
	revisionPort RevisionPort,
	versionAgent version.VersionAgent[mergetagdeclarationdomain.MergeTagDeclarationValue],
) ucVersionManagedMergeTag {
	return &versionManagedMergeTagImpl{
		revisionPort: revisionPort,
		versionAgent: versionAgent,
	}
}

// GetMergeTag retrieves a specific merge tag declaration by ID.
func (uc *versionManagedMergeTagImpl) GetMergeTag(
	ctx context.Context,
	args GetArgs,
) (VersionManagedMergeTagDeclaration, error) {
	if err := validator.ValidateStruct(args); err != nil {
		return VersionManagedMergeTagDeclaration{}, fmt.Errorf("invalid get merge tag arguments: %w", err)
	}

	// Use the RevisionPort to get the merge tag
	result, err := uc.revisionPort.GetMergeTag(ctx, GetMergeTagPortArgs(args))
	if err != nil {
		return VersionManagedMergeTagDeclaration{}, xerrors.Wrap(err,
			xerrors.Field("mergeTagID", args.ID))
	}

	return result, nil
}

// ListMergeTags retrieves all merge tag declarations for a given host product code.
func (uc *versionManagedMergeTagImpl) ListMergeTags(
	ctx context.Context,
	args ListArgs,
) ([]VersionManagedMergeTagDeclaration, error) {
	if err := validator.ValidateStruct(args); err != nil {
		return nil, fmt.Errorf("invalid list merge tags arguments: %w", err)
	}

	// Use the RevisionPort to list merge tags
	result, err := uc.revisionPort.ListMergeTags(ctx, ListMergeTagsPortArgs(args))
	if err != nil {
		return nil, xerrors.Wrap(err,
			xerrors.Field("hostProductCode", args.HostProductCode))
	}

	return result, nil
}

// CreateMergeTag creates a new merge tag declaration.
func (uc *versionManagedMergeTagImpl) CreateMergeTag(
	ctx context.Context,
	args CreateVersionManagedArgs,
) (VersionManagedMergeTagDeclaration, error) {
	if err := validator.ValidateStruct(args); err != nil {
		return VersionManagedMergeTagDeclaration{}, fmt.Errorf("invalid create merge tag arguments: %w", err)
	}

	// Create the merge tag declaration domain entity
	now := time.Now()
	mergeTag := mergetagdeclarationdomain.MergeTagDeclaration{
		ID:                uuid.GenerateUUIDV4(), // Generate a unique ID
		HostProductCode:   args.HostProductCode,
		Handle:            args.Handle,
		Name:              args.Name,
		Description:       args.Description,
		Category:          args.Category,
		OrderingWeight:    args.OrderingWeight,
		ApplicableSetting: args.ApplicableSetting,
		MergeTag:          args.MergeTag,
		MergeLogic:        args.MergeLogic,
		Hidden:            false,
		CreatedAt:         now,
	}

	// Validate the merge tag for creation
	if err := mergeTag.ValidateForCreation(); err != nil {
		return VersionManagedMergeTagDeclaration{}, fmt.Errorf("merge tag validation failed: %w", err)
	}

	// Use the RevisionPort to create the merge tag
	result, err := uc.revisionPort.CreateMergeTag(ctx, CreateMergeTagPortArgs{
		HostProductCode: args.HostProductCode,
		MergeTag:        mergeTag,
		CreatedBy:       args.OperatorEmail,
	})
	if err != nil {
		return VersionManagedMergeTagDeclaration{}, xerrors.Wrap(err,
			xerrors.Field("handle", args.Handle),
			xerrors.Field("hostProductCode", args.HostProductCode))
	}

	return result, nil
}

// UpdateMergeTag updates an existing merge tag declaration.
func (uc *versionManagedMergeTagImpl) UpdateMergeTag(
	ctx context.Context,
	args UpdateArgs,
) (VersionManagedMergeTagDeclaration, error) {
	if err := validator.ValidateStruct(args); err != nil {
		return VersionManagedMergeTagDeclaration{}, fmt.Errorf("invalid update merge tag arguments: %w", err)
	}

	// First, get the existing merge tag to retrieve the host product code
	existingResult, err := uc.revisionPort.GetMergeTag(ctx, GetMergeTagPortArgs{
		HostProductCode: args.HostProductCode,
		ID:              args.ID,
	})
	if err != nil {
		return VersionManagedMergeTagDeclaration{}, xerrors.Wrap(err,
			xerrors.Field("mergeTagID", args.ID))
	}

	// Create updated merge tag entity
	updatedMergeTag := existingResult.Extension

	// Apply business logic for updates
	if err := updatedMergeTag.UpdateName(args.Name); err != nil {
		return VersionManagedMergeTagDeclaration{}, fmt.Errorf("failed to update merge tag name: %w", err)
	}

	if err := updatedMergeTag.UpdateDescription(args.Description); err != nil {
		return VersionManagedMergeTagDeclaration{}, fmt.Errorf("failed to update merge tag description: %w", err)
	}

	if err := updatedMergeTag.UpdateCategory(args.Category); err != nil {
		return VersionManagedMergeTagDeclaration{}, fmt.Errorf("failed to update merge tag category: %w", err)
	}

	if err := updatedMergeTag.UpdateMergeLogic(args.MergeLogic); err != nil {
		return VersionManagedMergeTagDeclaration{}, fmt.Errorf("failed to update merge tag logic: %w", err)
	}

	if err := updatedMergeTag.UpdateApplicableSetting(args.ApplicableSetting); err != nil {
		return VersionManagedMergeTagDeclaration{}, fmt.Errorf("failed to update merge tag applicable setting: %w", err)
	}

	if err := updatedMergeTag.UpdateOrderingWeight(args.OrderingWeight); err != nil {
		return VersionManagedMergeTagDeclaration{}, fmt.Errorf("failed to update merge tag ordering weight: %w", err)
	}

	// Use the RevisionPort to update the merge tag
	result, err := uc.revisionPort.UpdateMergeTag(ctx, UpdateMergeTagPortArgs{
		HostProductCode: updatedMergeTag.HostProductCode,
		MergeTag:        updatedMergeTag,
		UpdatedBy:       args.UpdatedBy,
	})
	if err != nil {
		return VersionManagedMergeTagDeclaration{}, xerrors.Wrap(err,
			xerrors.Field("mergeTagID", args.ID))
	}

	return result, nil
}

// ResetMergeTag resets a merge tag declaration to its previous released version.
func (uc *versionManagedMergeTagImpl) ResetMergeTag(
	ctx context.Context,
	args ResetArgs,
) error {
	if err := validator.ValidateStruct(args); err != nil {
		return fmt.Errorf("invalid reset merge tag arguments: %w", err)
	}

	// First, get the existing merge tag to retrieve necessary information
	existingResult, err := uc.revisionPort.GetMergeTag(ctx, GetMergeTagPortArgs{
		HostProductCode: args.HostProductCode,
		ID:              args.ID,
	})
	if err != nil {
		return xerrors.Wrap(err,
			xerrors.Field("mergeTagID", args.ID))
	}

	// Check if merge tag can be deleted (business logic)
	if err := existingResult.Extension.CanBeDeleted(); err != nil {
		return fmt.Errorf("merge tag cannot be reset: %w", err)
	}

	// Use the RevisionPort to reset the merge tag
	return uc.revisionPort.ResetMergeTag(ctx, ResetMergeTagPortArgs{
		HostProductCode: existingResult.Extension.HostProductCode,
		ID:              args.ID,
		ResetBy:         args.ResetBy,
	})
}

// Release releases a version of merge tag declarations using the VersionAgent.
func (uc *versionManagedMergeTagImpl) Release(
	ctx context.Context,
	args ReleaseArgs,
) error {
	if err := validator.ValidateStruct(args); err != nil {
		return fmt.Errorf("invalid release arguments: %w", err)
	}

	err := uc.versionAgent.Release(ctx, args)
	if err != nil {
		return xerrors.Wrap(err,
			xerrors.Field("hostProductCode", args.HostProductCode))
	}
	return nil
}
