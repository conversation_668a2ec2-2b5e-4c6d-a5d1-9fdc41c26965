package commoncontentapplication

import (
	"context"

	mergetagdeclarationdomain "github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/mergetagdeclaration"
)

// RevisionPort defines the interface for managing merge tag declaration revisions
// This port abstracts the revision management functionality needed by the application layer
//
//go:generate mockery --name RevisionPort --with-expecter --inpackage --structname MockRevisionPort --filename revision_port.mock.gen.go
type RevisionPort interface {
	// CreateMergeTag creates a new merge tag declaration revision
	CreateMergeTag(ctx context.Context, args CreateMergeTagPortArgs) (VersionManagedMergeTagDeclaration, error)

	// GetMergeTag retrieves a merge tag declaration by ID
	GetMergeTag(ctx context.Context, args GetMergeTagPortArgs) (VersionManagedMergeTagDeclaration, error)

	// ListMergeTags lists all working merge tag declarations for a host product
	ListMergeTags(ctx context.Context, args ListMergeTagsPortArgs) ([]VersionManagedMergeTagDeclaration, error)

	// UpdateMergeTag updates an existing merge tag declaration
	UpdateMergeTag(ctx context.Context, args UpdateMergeTagPortArgs) (VersionManagedMergeTagDeclaration, error)

	// ResetMergeTag resets a merge tag declaration to its previous released version
	ResetMergeTag(ctx context.Context, args ResetMergeTagPortArgs) error
}

// Port-specific argument types.
type CreateMergeTagPortArgs struct {
	HostProductCode string
	MergeTag        mergetagdeclarationdomain.MergeTagDeclaration
	CreatedBy       string
}

type GetMergeTagPortArgs struct {
	HostProductCode string
	ID              string
}

type ListMergeTagsPortArgs struct {
	HostProductCode string
}

type UpdateMergeTagPortArgs struct {
	HostProductCode string
	MergeTag        mergetagdeclarationdomain.MergeTagDeclaration
	UpdatedBy       string
}

type ResetMergeTagPortArgs struct {
	HostProductCode string
	ID              string
	ResetBy         string
}
