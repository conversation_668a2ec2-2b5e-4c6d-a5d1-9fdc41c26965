package commoncontentapplication

import (
	"context"

	"github.com/AfterShip/pltf-nf-infra/pkg/revision"
	"github.com/AfterShip/pltf-nf-infra/pkg/version"
	mergetagdeclarationdomain "github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/mergetagdeclaration"
)

type CreateVersionManagedArgs struct {
	HostProductCode   string                                      `json:"host_product_code"  body:"host_product_code"  validate:"required"`
	Handle            string                                      `json:"handle"             body:"handle"             validate:"required"`
	Name              string                                      `json:"name"               body:"name"               validate:"required"`
	Description       string                                      `json:"description"        body:"description"`
	Category          string                                      `json:"category"           body:"category"`
	MergeTag          string                                      `json:"merge_tag"          body:"merge_tag"          validate:"required"`
	MergeLogic        mergetagdeclarationdomain.MergeLogic        `json:"merge_logic"        body:"merge_logic"        validate:"required"`
	ApplicableSetting mergetagdeclarationdomain.ApplicableSetting `json:"applicable_setting" body:"applicable_setting"`
	OrderingWeight    int64                                       `json:"ordering_weight"    body:"ordering_weight"`
	OperatorEmail     string                                      `json:"operator_email"     body:"operator_email"     validate:"required"`
}

type UpdateArgs struct {
	HostProductCode   string                                      `json:"host_product_code"  body:"host_product_code"  validate:"required"`
	ID                string                                      `json:"id"                                           validate:"required" path:"id"`
	Name              string                                      `json:"name"               body:"name"               validate:"required"`
	Description       string                                      `json:"description"        body:"description"`
	Category          string                                      `json:"category"           body:"category"`
	MergeLogic        mergetagdeclarationdomain.MergeLogic        `json:"merge_logic"        body:"merge_logic"        validate:"required"`
	ApplicableSetting mergetagdeclarationdomain.ApplicableSetting `json:"applicable_setting" body:"applicable_setting"`
	OrderingWeight    int64                                       `json:"ordering_weight"    body:"ordering_weight"`
	UpdatedBy         string                                      `json:"updated_by"         body:"updated_by"         validate:"required"`
}

type ListArgs struct {
	// HostProductCode the product code of the host product
	HostProductCode string `json:"host_product_code" query:"host_product_code"`
}

type ResetArgs struct {
	HostProductCode string `json:"host_product_code" query:"host_product_code"`
	ID              string `json:"id"                                          path:"id" validate:"required"`
	ResetBy         string `json:"reset_by"                                              validate:"required" body:"reset_by"`
}

type GetArgs struct {
	HostProductCode string `json:"host_product_code" query:"host_product_code"`
	ID              string `json:"id"                                          path:"id" validate:"required"`
}

type VersionManagedMergeTagDeclaration = revision.VersionManagedExtension[mergetagdeclarationdomain.MergeTagDeclaration]

// ReleaseArgs defines the arguments for releasing merge tag declarations.
type ReleaseArgs = version.ReleaseArgs[mergetagdeclarationdomain.MergeTagDeclarationValue]

// ExtensionIdentifier identifies a merge tag declaration extension.
type ExtensionIdentifier = revision.ExtensionIdentifier

// ucVersionManagedMergeTag defines the contract for managing merge tag declarations in the message console.
//
//go:generate mockery --with-expecter --name ucVersionManagedMergeTag --filename use_case_manager.mock.gen.go --output ./ --inpackage --structname MockUseCaseMergeTagManager
type ucVersionManagedMergeTag interface {
	GetMergeTag(ctx context.Context, args GetArgs) (VersionManagedMergeTagDeclaration, error)
	ListMergeTags(ctx context.Context, args ListArgs) ([]VersionManagedMergeTagDeclaration, error)
	CreateMergeTag(ctx context.Context, args CreateVersionManagedArgs) (VersionManagedMergeTagDeclaration, error)
	UpdateMergeTag(ctx context.Context, args UpdateArgs) (VersionManagedMergeTagDeclaration, error)
	ResetMergeTag(ctx context.Context, args ResetArgs) error

	// Release releases a version of merge tag declarations
	Release(ctx context.Context, args ReleaseArgs) error
}
