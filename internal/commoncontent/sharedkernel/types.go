package sharedkernel

import (
	"github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/mergetagdeclaration"
	"github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/rendersetting"
)

type MergeTagDeclaration = mergetagdeclaration.MergeTagDeclaration

type MergeMode = mergetagdeclaration.MergeMode

type MergeLogic = mergetagdeclaration.MergeLogic

type ApplicableSetting = mergetagdeclaration.ApplicableSetting

type RenderSettingAggregate = rendersetting.RenderSettingAggregate

type RenderSetting = rendersetting.RenderSetting

type GlobalSenderInfo = rendersetting.GlobalSenderInfo

const (
	// 合并模式.
	MergeModeContextPath    = mergetagdeclaration.MergeModeContextPath
	MergeModeDjangoTemplate = mergetagdeclaration.MergeModeDjangoTemplate
)
