package sharedkernel

import (
	"go.uber.org/fx"

	mergetagrepo "github.com/AfterShip/pltf-nf-message/internal/adapters/persistence/spanner/mergetagdeclaration"
	rendersettingrepo "github.com/AfterShip/pltf-nf-message/internal/adapters/persistence/spanner/rendersetting"
)

var Module = fx.Module("common_content_shared_kernel",
	fx.Provide(
		fx.Annotate(mergetagrepo.NewRepositoryImpl, fx.As(new(MergeTagDeclarationRepository))),
		fx.Annotate(rendersettingrepo.NewRepository, fx.As(new(RenderSettingRepository))),
	),
)
