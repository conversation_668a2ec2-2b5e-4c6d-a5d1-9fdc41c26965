package adapters

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/AfterShip/pltf-nf-infra/pkg/revision"
	commoncontentapplication "github.com/AfterShip/pltf-nf-message/internal/commoncontent/application"
	mergetagdeclarationdomain "github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/mergetagdeclaration"
)

// mergeTagRevisionPortAdapter implements the RevisionPort interface using the RevisionAgent.
type mergeTagRevisionPortAdapter struct {
	revisionAgent revision.RevisionAgent[mergetagdeclarationdomain.MergeTagDeclaration]
}

// NewMergeTagRevisionPortAdapter creates a new adapter that implements RevisionPort.
func NewMergeTagRevisionPortAdapter(
	revisionAgent revision.RevisionAgent[mergetagdeclarationdomain.MergeTagDeclaration],
) commoncontentapplication.RevisionPort {
	return &mergeTagRevisionPortAdapter{
		revisionAgent: revisionAgent,
	}
}

// CreateMergeTag implements RevisionPort.CreateMergeTag.
func (a *mergeTagRevisionPortAdapter) CreateMergeTag(
	ctx context.Context,
	args commoncontentapplication.CreateMergeTagPortArgs,
) (commoncontentapplication.VersionManagedMergeTagDeclaration, error) {
	revisionArgs := revision.CreateExtensionArgs[mergetagdeclarationdomain.MergeTagDeclaration]{
		HostProductCode: args.HostProductCode,
		Extension:       args.MergeTag,
		CreatedBy:       args.CreatedBy,
	}

	return a.revisionAgent.CreateExtension(ctx, revisionArgs)
}

// GetMergeTag implements RevisionPort.GetMergeTag.
func (a *mergeTagRevisionPortAdapter) GetMergeTag(
	ctx context.Context,
	args commoncontentapplication.GetMergeTagPortArgs,
) (commoncontentapplication.VersionManagedMergeTagDeclaration, error) {
	revisionArgs := revision.GetExtensionArgs{
		HostProductCode: args.HostProductCode,
		ID:              args.ID,
	}

	return a.revisionAgent.GetExtension(ctx, revisionArgs)
}

// ListMergeTags implements RevisionPort.ListMergeTags.
func (a *mergeTagRevisionPortAdapter) ListMergeTags(
	ctx context.Context,
	args commoncontentapplication.ListMergeTagsPortArgs,
) ([]commoncontentapplication.VersionManagedMergeTagDeclaration, error) {
	revisionArgs := revision.ListExtensionsArgs{
		HostProductCode: args.HostProductCode,
	}

	return a.revisionAgent.ListWorkingExtensions(ctx, revisionArgs)
}

// UpdateMergeTag implements RevisionPort.UpdateMergeTag.
func (a *mergeTagRevisionPortAdapter) UpdateMergeTag(
	ctx context.Context,
	args commoncontentapplication.UpdateMergeTagPortArgs,
) (commoncontentapplication.VersionManagedMergeTagDeclaration, error) {
	revisionArgs := revision.UpdateExtensionArgs[mergetagdeclarationdomain.MergeTagDeclaration]{
		HostProductCode: args.HostProductCode,
		Extension:       args.MergeTag,
		UpdatedBy:       args.UpdatedBy,
	}

	return a.revisionAgent.UpdateExtension(ctx, revisionArgs)
}

// ResetMergeTag implements RevisionPort.ResetMergeTag.
func (a *mergeTagRevisionPortAdapter) ResetMergeTag(
	ctx context.Context,
	args commoncontentapplication.ResetMergeTagPortArgs,
) error {
	// First get the extension to retrieve the identifier
	extension, err := a.revisionAgent.GetExtension(ctx, revision.GetExtensionArgs{
		HostProductCode: args.HostProductCode,
		ID:              args.ID,
	})
	if err != nil {
		return fmt.Errorf("failed to get extension for reset: %w", err)
	}

	revisionArgs := revision.ResetExtensionArgs{
		HostProductCode: args.HostProductCode,
		Identifier:      extension.Extension.Identifier(),
		ResetBy:         args.ResetBy,
	}

	return a.revisionAgent.ResetExtension(ctx, revisionArgs)
}

// MergeTagDeclarationBuilder builds a MergeTagDeclaration from an ExtensionRevision.
func MergeTagDeclarationBuilder(rev revision.ExtensionRevision) (mergetagdeclarationdomain.MergeTagDeclaration, error) {
	var mergeTagValue struct {
		HostProductCode   string                                      `json:"host_product_code"`
		Name              string                                      `json:"name"`
		Description       string                                      `json:"description"`
		Category          string                                      `json:"category"`
		OrderingWeight    int64                                       `json:"ordering_weight"`
		ApplicableSetting mergetagdeclarationdomain.ApplicableSetting `json:"applicable_setting"`
		MergeTag          string                                      `json:"merge_tag"`
		MergeLogic        mergetagdeclarationdomain.MergeLogic        `json:"merge_logic"`
		Hidden            bool                                        `json:"hidden"`
	}

	if err := json.Unmarshal(rev.Value, &mergeTagValue); err != nil {
		return mergetagdeclarationdomain.MergeTagDeclaration{}, fmt.Errorf(
			"failed to unmarshal merge tag value: %w",
			err,
		)
	}

	mergeTag := mergetagdeclarationdomain.MergeTagDeclaration{
		ID:                rev.ExtensionIdentifier.ID,
		HostProductCode:   mergeTagValue.HostProductCode,
		Handle:            rev.ExtensionIdentifier.Handle,
		Name:              mergeTagValue.Name,
		Description:       mergeTagValue.Description,
		Category:          mergeTagValue.Category,
		OrderingWeight:    mergeTagValue.OrderingWeight,
		ApplicableSetting: mergeTagValue.ApplicableSetting,
		MergeTag:          mergeTagValue.MergeTag,
		MergeLogic:        mergeTagValue.MergeLogic,
		Hidden:            mergeTagValue.Hidden,
		CreatedAt:         rev.CreatedAt,
	}

	// Set UpdatedAt only if it exists and is different from CreatedAt
	if rev.UpdatedAt != nil && !rev.UpdatedAt.Equal(rev.CreatedAt) {
		mergeTag.UpdatedAt = rev.UpdatedAt
	}

	return mergeTag, nil
}

// NewMergeTagDeclarationRevisionAgent creates a new RevisionAgent for MergeTagDeclaration.
func NewMergeTagDeclarationRevisionAgent() revision.RevisionAgent[mergetagdeclarationdomain.MergeTagDeclaration] {
	return revision.NewRevisionAgent(
		mergetagdeclarationdomain.MergeTagExtensionType,
		MergeTagDeclarationBuilder,
	)
}
