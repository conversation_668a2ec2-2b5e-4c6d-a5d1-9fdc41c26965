package adapters

import (
	"go.uber.org/fx"

	"github.com/AfterShip/pltf-nf-infra/pkg/revision"
	"github.com/AfterShip/pltf-nf-infra/pkg/version"
	commoncontentapplication "github.com/AfterShip/pltf-nf-message/internal/commoncontent/application"
	mergetagdeclarationdomain "github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/mergetagdeclaration"
	"github.com/AfterShip/pltf-nf-message/internal/ports"
)

// Module provides the fx module for commoncontent adapters.
var Module = fx.Module("commoncontent_adapters",
	fx.Provide(
		NewMergeTagDeclarationRevisionAgent,
		NewMergeTagRevisionPortAdapter,
		NewMergeTagDeclarationExtensionProvider,
		NewMergeTagDeclarationVersionAgent,
	),
)

// NewMergeTagRevisionPortAdapterFromAgent creates a RevisionPort from a RevisionAgent.
func NewMergeTagRevisionPortAdapterFromAgent(
	revisionAgent revision.RevisionAgent[mergetagdeclarationdomain.MergeTagDeclaration],
) commoncontentapplication.RevisionPort {
	return NewMergeTagRevisionPortAdapter(revisionAgent)
}

// NewMergeTagDeclarationExtensionProviderFromPorts creates an ExtensionProvider from ports.
func NewMergeTagDeclarationExtensionProviderFromPorts(
	repository mergetagdeclarationdomain.MergeTagDeclarationRepository,
	transactionPort ports.TransactionPort,
) version.ExtensionProvider[mergetagdeclarationdomain.MergeTagDeclarationValue] {
	return NewMergeTagDeclarationExtensionProvider(repository, transactionPort)
}

// NewMergeTagDeclarationVersionAgentFromProvider creates a VersionAgent from an ExtensionProvider.
func NewMergeTagDeclarationVersionAgentFromProvider(
	provider version.ExtensionProvider[mergetagdeclarationdomain.MergeTagDeclarationValue],
) version.VersionAgent[mergetagdeclarationdomain.MergeTagDeclarationValue] {
	return version.NewVersionAgent(
		provider,
		mergetagdeclarationdomain.MergeTagExtensionType,
	)
}
