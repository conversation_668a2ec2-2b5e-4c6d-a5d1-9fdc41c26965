package adapters

import (
	"context"
	"time"

	"github.com/samber/lo"

	"github.com/AfterShip/pltf-nf-infra/pkg/version"
	mergetagdeclarationdomain "github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/mergetagdeclaration"
	"github.com/AfterShip/pltf-nf-message/internal/ports"
)

// mergeTagDeclarationExtensionProvider implements the version.ExtensionProvider interface
// for merge tag declarations. It handles the actual persistence operations during version releases.
type mergeTagDeclarationExtensionProvider struct {
	repository      mergetagdeclarationdomain.MergeTagDeclarationRepository
	transactionPort ports.TransactionPort
}

// NewMergeTagDeclarationExtensionProvider creates a new ExtensionProvider for merge tag declarations.
func NewMergeTagDeclarationExtensionProvider(
	repository mergetagdeclarationdomain.MergeTagDeclarationRepository,
	transactionPort ports.TransactionPort,
) version.ExtensionProvider[mergetagdeclarationdomain.MergeTagDeclarationValue] {
	return &mergeTagDeclarationExtensionProvider{
		repository:      repository,
		transactionPort: transactionPort,
	}
}

// RunInTransaction runs the given function in a transaction.
func (p *mergeTagDeclarationExtensionProvider) RunInTransaction(
	ctx context.Context,
	fn func(context.Context) error,
) error {
	return p.transactionPort.RunInTransaction(ctx, fn)
}

// CreateExtensions creates new merge tag declaration extensions.
func (p *mergeTagDeclarationExtensionProvider) CreateExtensions(
	ctx context.Context,
	extensions []version.ExtensionRevision[mergetagdeclarationdomain.MergeTagDeclarationValue],
) error {
	if len(extensions) == 0 {
		return nil
	}

	// Convert extensions to MergeTagDeclaration domain objects
	mergeTags := make([]mergetagdeclarationdomain.MergeTagDeclaration, 0, len(extensions))
	now := time.Now()

	for _, ext := range extensions {
		// Validate merge tag declaration data
		mergeTag := mergetagdeclarationdomain.MergeTagDeclaration{
			ID:                ext.Identifier.ID,
			HostProductCode:   ext.HostProductCode,
			Handle:            ext.Identifier.Handle,
			Name:              ext.Value.Name,
			Description:       ext.Value.Description,
			Category:          ext.Value.Category,
			OrderingWeight:    ext.Value.OrderingWeight,
			ApplicableSetting: ext.Value.ApplicableSetting,
			MergeTag:          ext.Value.MergeTag,
			MergeLogic:        ext.Value.MergeLogic,
			Hidden:            ext.Value.Hidden,
			CreatedAt:         now,
		}

		// Validate before creating
		if err := mergeTag.CanBeCreated(); err != nil {
			return err
		}

		mergeTags = append(mergeTags, mergeTag)
	}

	// Use batch create for better performance
	_, err := p.repository.BatchCreate(ctx, mergeTags)
	return err
}

// UpdateExtensions updates existing merge tag declaration extensions.
func (p *mergeTagDeclarationExtensionProvider) UpdateExtensions(
	ctx context.Context,
	extensions []version.ExtensionRevision[mergetagdeclarationdomain.MergeTagDeclarationValue],
) error {
	if len(extensions) == 0 {
		return nil
	}

	// Extract all identifiers for batch query
	identifierIDs := lo.Map(
		extensions,
		func(ext version.ExtensionRevision[mergetagdeclarationdomain.MergeTagDeclarationValue], _ int) string {
			return ext.Identifier.ID
		},
	)

	// Find existing merge tags in batch
	existingMergeTags, err := p.repository.Find(ctx, mergetagdeclarationdomain.Query{
		HostProductCode: extensions[0].HostProductCode, // All extensions should have the same host product code
		Ids:             identifierIDs,
	})
	if err != nil {
		return err
	}

	// Create a map for quick lookup
	existingMap := lo.KeyBy(existingMergeTags, func(mergeTag mergetagdeclarationdomain.MergeTagDeclaration) string {
		return mergeTag.ID
	})

	// Update merge tags
	updatedMergeTags := make([]mergetagdeclarationdomain.MergeTagDeclaration, 0, len(extensions))
	now := time.Now()

	for _, ext := range extensions {
		existing, found := existingMap[ext.Identifier.ID]
		if !found {
			// Skip if merge tag not found - this shouldn't happen in normal flow
			continue
		}

		// Update the merge tag with new values and timestamp
		existing.Name = ext.Value.Name
		existing.Description = ext.Value.Description
		existing.Category = ext.Value.Category
		existing.OrderingWeight = ext.Value.OrderingWeight
		existing.ApplicableSetting = ext.Value.ApplicableSetting
		existing.MergeTag = ext.Value.MergeTag
		existing.MergeLogic = ext.Value.MergeLogic
		existing.Hidden = ext.Value.Hidden
		existing.UpdatedAt = &now

		updatedMergeTags = append(updatedMergeTags, existing)
	}

	if len(updatedMergeTags) == 0 {
		return nil
	}

	// Use batch update for better performance
	_, err = p.repository.BatchUpdate(ctx, updatedMergeTags)
	return err
}

// DeleteExtensions deletes merge tag declaration extensions by their identifiers.
func (p *mergeTagDeclarationExtensionProvider) DeleteExtensions(
	ctx context.Context,
	identifiers []version.ExtensionIdentifier,
) error {
	if len(identifiers) == 0 {
		return nil
	}

	// Extract all identifiers for batch query
	identifierIDs := lo.Map(identifiers, func(identifier version.ExtensionIdentifier, _ int) string {
		return identifier.ID
	})

	// Find existing merge tags in batch by ID only (since ID should be globally unique)
	// We don't have HostProductCode here, but ID should be sufficient for identification
	existingMergeTags, err := p.repository.Find(ctx, mergetagdeclarationdomain.Query{
		Ids: identifierIDs,
	})
	if err != nil {
		return err
	}

	if len(existingMergeTags) == 0 {
		return nil
	}

	// Delete merge tags in batch by marking them as deleted (soft delete)
	now := time.Now()
	for i := range existingMergeTags {
		existingMergeTags[i].DeletedAt = &now
	}

	// Use batch update to mark as deleted (soft delete)
	_, err = p.repository.BatchUpdate(ctx, existingMergeTags)
	return err
}

// NewMergeTagDeclarationVersionAgent creates a new VersionAgent for merge tag declarations.
func NewMergeTagDeclarationVersionAgent(
	repository mergetagdeclarationdomain.MergeTagDeclarationRepository,
	transactionPort ports.TransactionPort,
) version.VersionAgent[mergetagdeclarationdomain.MergeTagDeclarationValue] {
	provider := NewMergeTagDeclarationExtensionProvider(repository, transactionPort)
	return version.NewVersionAgent(
		provider,
		mergetagdeclarationdomain.MergeTagExtensionType,
	)
}
