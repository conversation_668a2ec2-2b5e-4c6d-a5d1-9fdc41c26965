package mergetagdeclaration

import (
	"context"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx/repositorytpl"
)

type Query struct {
	HostProductCode string   `sql:"omitempty"`
	Ids             []string `sql:"column=merge_tag_id,op=in,omitempty"`
}

type MergeTagDeclarationRepository interface {
	repositorytpl.Repository[MergeTagDeclaration, Query]
	FindByHostProductCode(ctx context.Context, hostProductCode string) ([]MergeTagDeclaration, error)
}
