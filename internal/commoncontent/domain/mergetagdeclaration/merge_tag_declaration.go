package mergetagdeclaration

import (
	"errors"
	"fmt"
	"time"

	"github.com/AfterShip/pltf-nf-infra/pkg/revision"
)

type MergeMode string

const (
	MergeTagExtensionType = "merge_tag_declarations"

	// MergeModeContextPath just get the value from the context.
	MergeModeContextPath = "render_context_path"

	// MergeModeDjangoTemplate  use the django template to render the value.
	MergeModeDjangoTemplate = "django_template"
)

// MergeTagDeclaration represents the configuration of a merge tag declaration.
//
//nolint:recvcheck // It is a valid check.
type MergeTagDeclaration struct {
	ID string `json:"id" body:"id"`

	// HostProductCode the product code of the host product.
	HostProductCode string `json:"host_product_code" body:"host_product_code"`

	// Handle is the internal unique identity for a merge tag.
	// Usually it is generated by the system
	Handle string `json:"handle" body:"handle"`

	// Name of a merge tag.
	// The name of a merge tag is used to display in the list.
	Name string `json:"name" body:"name"`

	// Description of a merge tag.
	// The description of a merge tag is used to display in the list.
	Description string `json:"description" body:"description"`

	// Category of a merge tag.
	// eg. tracking, organization, etc.
	Category string `json:"category" body:"category"`

	// OrderingWeight the ordering weight of a merge tag.
	// The merge tag will be sorted by the ordering weight in the list.
	OrderingWeight int64 `json:"ordering_weight" body:"ordering_weight"`

	// ApplicableSetting is the setting that a merge tag is applicable to.
	// The user can customize the setting of the merge tag when create or update.
	ApplicableSetting ApplicableSetting `json:"applicable_setting" body:"applicable_setting"`

	// MergeTag is a placeholder refer to a render context data field.
	// It will be replaced by the real value when rendering.
	// The merge tag should be unique in a host product code.
	// The merge tag should be uppercase.
	// It should be surrounded by *| and |*.
	// eg. *|ORDER_NUMBER|* is a merge tag refer to the order number.
	MergeTag string `json:"merge_tag" body:"merge_tag"`

	// MergeLogic is the logic to render the merge tag.
	MergeLogic MergeLogic `json:"merge_logic" body:"merge_logic"`

	// Hidden is the flag to hide a merge tag.
	// The hidden merge tag will not be shown in the list.
	Hidden bool `json:"hidden" body:"hidden"`

	CreatedAt time.Time  `json:"created_at" body:"created_at"`
	UpdatedAt *time.Time `json:"updated_at" body:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at" body:"deleted_at"`
}

// ApplicableSetting is the setting that the merge tag is applicable to.
type ApplicableSetting struct {
	// MessageChannels is the message channel that the merge tag is applicable to
	// Empty means all message channels.
	// e.g ["email", "sms"].
	MessageChannels []string `json:"message_channels" body:"message_channels"`

	// EditorKeys is the editor key that the merge tag is applicable to.
	// Empty means all editor keys.
	// e.g ["easy_email", "dnd_email"].
	EditorKeys []string `json:"editor_keys" body:"editor_keys"`

	// BusinessScenarioTypes is the business scenarios the merge tags that is applicable to.
	// Empty means all business scenarios.
	// e.g ["order", "fulfillment"].
	BusinessScenarioTypes []string `json:"business_scenario_types" body:"business_scenario_types"`

	// Tags is the tags that the merge tag is applicable to.
	Tags []string `json:"tags" body:"tags"`
}

type MergeLogic struct {
	// Mode is the mode of the merge logic.
	Mode MergeMode `json:"mode" body:"mode" validate:"required,oneof=render_context_path django_template"`

	// Value is the value of the merge logic.
	// If the mode is render_context_path, the value is the path of the context.
	// If the mode is django_template, the value is the django template.
	Value string `json:"value" body:"value" validate:"required"`
}

type MergeTagOrderingDescriptor struct {
	ID             string `json:"id"              body:"id"              validate:"required"`
	OrderingWeight int64  `json:"ordering_weight" body:"ordering_weight" validate:"required"`
}

func (tag *MergeTagDeclaration) ToRawDjango() string {
	switch tag.MergeLogic.Mode {
	case MergeModeContextPath:
		return fmt.Sprintf("{{ %s }}", tag.MergeLogic.Value)
	case MergeModeDjangoTemplate:
		return tag.MergeLogic.Value
	default:
		return ""
	}
}

func (tag *MergeTagDeclaration) ToTag() string {
	return tag.MergeTag
}

// MergeTagDeclarationValue represents the configuration value of a merge tag declaration,
// excluding metadata like ID, timestamps, etc. This matches the return type of GetValue().
type MergeTagDeclarationValue struct {
	Name              string            `json:"name"`
	Description       string            `json:"description"`
	Category          string            `json:"category"`
	OrderingWeight    int64             `json:"ordering_weight"`
	ApplicableSetting ApplicableSetting `json:"applicable_setting"`
	MergeTag          string            `json:"merge_tag"`
	MergeLogic        MergeLogic        `json:"merge_logic"`
	Hidden            bool              `json:"hidden"`
}

// Identifier implements the revision.Extension interface.
//

func (m MergeTagDeclaration) Identifier() revision.ExtensionIdentifier {
	return revision.ExtensionIdentifier{
		Type:   MergeTagExtensionType,
		ID:     m.ID,
		Handle: m.Handle,
	}
}

// GetValue implements the revision.Extension interface
// Returns the configuration value of the merge tag declaration, excluding metadata.
//

func (m MergeTagDeclaration) GetValue() any {
	return MergeTagDeclarationValue{
		Name:              m.Name,
		Description:       m.Description,
		Category:          m.Category,
		OrderingWeight:    m.OrderingWeight,
		ApplicableSetting: m.ApplicableSetting,
		MergeTag:          m.MergeTag,
		MergeLogic:        m.MergeLogic,
		Hidden:            m.Hidden,
	}
}

// Domain Business Logic Methods (following DDD principles)

// CanBeCreated checks if the merge tag declaration can be created.
func (m MergeTagDeclaration) CanBeCreated() error {
	if m.ID == "" {
		return errors.New("id cannot be empty")
	}

	if m.Handle == "" {
		return errors.New("handle cannot be empty")
	}

	if m.Name == "" {
		return errors.New("name cannot be empty")
	}

	if m.MergeTag == "" {
		return errors.New("merge tag cannot be empty")
	}

	// Add additional business rules here
	// For example: validate format, check uniqueness constraints, etc.

	return nil
}

// CanBeDeleted checks if the merge tag declaration can be deleted.
func (m MergeTagDeclaration) CanBeDeleted() error {
	if m.DeletedAt != nil {
		return errors.New("merge tag declaration is already deleted")
	}

	// Add additional business rules here
	// For example: check if it's being used by other entities

	return nil
}

// CanBeUpdated checks if the merge tag declaration can be updated.
func (m MergeTagDeclaration) CanBeUpdated() error {
	if m.DeletedAt != nil {
		return errors.New("cannot update deleted merge tag declaration")
	}

	return nil
}

// UpdateName updates the name with business validation.
func (m *MergeTagDeclaration) UpdateName(newName string) error {
	if err := m.CanBeUpdated(); err != nil {
		return err
	}

	if newName == "" {
		return errors.New("name cannot be empty")
	}

	m.Name = newName
	now := time.Now()
	m.UpdatedAt = &now

	return nil
}

// UpdateDescription updates the description with business validation.
func (m *MergeTagDeclaration) UpdateDescription(newDescription string) error {
	if err := m.CanBeUpdated(); err != nil {
		return err
	}

	m.Description = newDescription
	now := time.Now()
	m.UpdatedAt = &now

	return nil
}

// UpdateCategory updates the category with business validation.
func (m *MergeTagDeclaration) UpdateCategory(newCategory string) error {
	if err := m.CanBeUpdated(); err != nil {
		return err
	}

	m.Category = newCategory
	now := time.Now()
	m.UpdatedAt = &now

	return nil
}

// UpdateMergeLogic updates the merge logic with business validation.
func (m *MergeTagDeclaration) UpdateMergeLogic(newMergeLogic MergeLogic) error {
	if err := m.CanBeUpdated(); err != nil {
		return err
	}

	// Validate merge logic
	if newMergeLogic.Mode == "" {
		return errors.New("merge logic mode cannot be empty")
	}

	if newMergeLogic.Value == "" {
		return errors.New("merge logic value cannot be empty")
	}

	m.MergeLogic = newMergeLogic
	now := time.Now()
	m.UpdatedAt = &now

	return nil
}

// UpdateApplicableSetting updates the applicable setting with business validation.
func (m *MergeTagDeclaration) UpdateApplicableSetting(newSetting ApplicableSetting) error {
	if err := m.CanBeUpdated(); err != nil {
		return err
	}

	m.ApplicableSetting = newSetting
	now := time.Now()
	m.UpdatedAt = &now

	return nil
}

// UpdateOrderingWeight updates the ordering weight with business validation.
func (m *MergeTagDeclaration) UpdateOrderingWeight(newWeight int64) error {
	if err := m.CanBeUpdated(); err != nil {
		return err
	}

	m.OrderingWeight = newWeight
	now := time.Now()
	m.UpdatedAt = &now

	return nil
}

// MarkAsDeleted performs soft delete with business validation.
func (m *MergeTagDeclaration) MarkAsDeleted() error {
	if err := m.CanBeDeleted(); err != nil {
		return err
	}

	now := time.Now()
	m.DeletedAt = &now

	return nil
}

// ValidateForCreation validates the merge tag declaration for creation.
func (m MergeTagDeclaration) ValidateForCreation() error {
	if m.HostProductCode == "" {
		return errors.New("host product code is required")
	}

	if m.Handle == "" {
		return errors.New("handle is required")
	}

	if m.Name == "" {
		return errors.New("name is required")
	}

	if m.MergeTag == "" {
		return errors.New("merge tag is required")
	}

	if m.MergeLogic.Mode == "" {
		return errors.New("merge logic mode is required")
	}

	if m.MergeLogic.Value == "" {
		return errors.New("merge logic value is required")
	}

	// Add more business validation rules as needed

	return nil
}
