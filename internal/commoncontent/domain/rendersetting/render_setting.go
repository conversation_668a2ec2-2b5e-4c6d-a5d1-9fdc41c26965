package rendersetting

import (
	"encoding/json"
	"time"

	"github.com/AfterShip/library-marketing-go/tenantscope"
)

const (
	RenderSettingTypeGlobalSenderInfo = "global_sender_info"
)

type RenderSettingAggregate struct {
	RenderSettings map[string]RenderSetting
}

type RenderSetting struct {
	*tenantscope.TenantScope
	HostProductCode string
	Id              string          `json:"id"`
	Type            string          `json:"type"`
	Value           json.RawMessage `json:"value"`
	CreatedAt       time.Time       `json:"created_at"`
	UpdatedAt       time.Time       `json:"updated_at"`
}

type GlobalSenderInfo struct {
	Name string `json:"name"`

	// NameMode is the mode of sender name.
	// Values: "custom", "org_name".
	// If "custom", the sender name is the static Name field.
	// If "org_name", the sender name is the dynamic organization name.
	// If empty, the name mode is custom.
	NameMode string `json:"name_mode,omitempty"`

	// SenderId is the sender id of the sender.
	// If the SenderSource is the org_sender, the sender id is the organization sender id.
	// If the SenderSource is the product_default, the sender id is empty.
	SenderId string `json:"sender_id"`

	// SenderSource is the source of the sender.
	// Values: "org_sender", "product_default".
	// If "org_sender", the sender is one of organization sender.
	// If "product_default", the sender is the product default sender.
	// If empty, the sender mode is org_sender.
	SenderSource string `json:"sender_source,omitempty"`
}

func (r *RenderSettingAggregate) GetGlobalSenderInfo() (GlobalSenderInfo, error) {
	setting, ok := r.RenderSettings[RenderSettingTypeGlobalSenderInfo]
	if !ok {
		return GlobalSenderInfo{}, ErrGlobalSenderInfoNotFound
	}

	var globalSenderInfo GlobalSenderInfo
	if err := json.Unmarshal(setting.Value, &globalSenderInfo); err != nil {
		return GlobalSenderInfo{}, err
	}

	return globalSenderInfo, nil
}

func (a *RenderSettingAggregate) ToMap() map[string]any {
	res := make(map[string]any)
	for k, v := range a.RenderSettings {
		res[k] = v.Value
	}
	return res
}
