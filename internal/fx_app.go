package internal

import (
	"go.uber.org/fx"
	"go.uber.org/fx/fxevent"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-nf-message/internal/adapters"
	"github.com/AfterShip/pltf-nf-message/internal/commoncontent"
	"github.com/AfterShip/pltf-nf-message/internal/email"
	"github.com/AfterShip/pltf-nf-message/internal/sms"
	"github.com/AfterShip/pltf-nf-message/internal/userinterface"
)

var FxEventLogging = fx.WithLogger(func(logger *log.Logger) fxevent.Logger {
	child := logger.Named("fx")
	return &fxevent.ZapLogger{Logger: child.GetZapLogger()}
})

func NewApplication(excludeUI bool, populates ...any) *fx.App {
	options := []fx.Option{
		fx.Decorate(func(logger *log.Logger) *log.Logger { return logger.Named("flow") }),

		FxEventLogging,

		adapters.Module,
		commoncontent.Module,
		email.Module,
		sms.Module,

		fx.Populate(populates...),
	}
	if !excludeUI {
		options = append(options, userinterface.Module)
	}

	return fx.New(options...)
}
