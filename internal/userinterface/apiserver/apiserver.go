package apiserver

import (
	"context"

	"github.com/danielgtaylor/huma/v2"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/library-heytea-go-common/http/server/gins/apiserver"
	"github.com/AfterShip/library-heytea-go-common/http/server/gins/humas"
	"github.com/AfterShip/library-heytea-go-common/lang/types"
	"github.com/AfterShip/library-heytea-go-common/logging"
	"github.com/AfterShip/library-heytea-go-common/whoami"
	"github.com/AfterShip/pltf-nf-message/internal/userinterface/apiserver/conf"
	"github.com/AfterShip/pltf-nf-message/internal/userinterface/apiserver/handlers"
)

//nolint:funlen // It is a valid check.
func NewAPIServer(
	apiConfig apiserver.ServerConfig,
	handlers handlers.Handlers) *apiserver.ApiServer {
	server := apiserver.NewApiServer(apiConfig)

	humas.Group(server.HumaAPI(), "/api/v1", func(api huma.API) {
		humas.Group(api, "/private", func(api huma.API) {
			humas.Group(api, "/email", func(api huma.API) {
				humas.Post(
					api,
					"/render-and-send",
					handlers.EmailHandlers.RenderAndSendEmailHandler,
					handlers.EmailHandlers.RenderAndSendEmailErrorHandler,
				)
			})
		})

		humas.Group(api, "/internal", func(api huma.API) {
			humas.Group(api, "/email", func(api huma.API) {
				humas.Group(api, "/subscription-compliance-rule", func(api huma.API) {
					humas.Post(
						api,
						"/",
						handlers.SubscriptionComplianceRuleHandler.Create,
						handlers.SubscriptionComplianceRuleHandler.CreateErrorHandler,
					)
				})

				// Email Sections Management
				humas.Group(api, "/sections", func(api huma.API) {
					humas.Group(api, "/versioned", func(api huma.API) {
						humas.Post(
							api,
							"/",
							handlers.VersionedEmailSectionHandler.CreateSection,
							handlers.VersionedEmailSectionHandler.HandlerError,
						)
						humas.Get(
							api,
							"/",
							handlers.VersionedEmailSectionHandler.ListSections,
							handlers.VersionedEmailSectionHandler.HandlerError,
						)
						humas.Get(
							api,
							"/{id}",
							handlers.VersionedEmailSectionHandler.GetSection,
							handlers.VersionedEmailSectionHandler.HandlerError,
						)
						humas.Put(
							api,
							"/{id}",
							handlers.VersionedEmailSectionHandler.UpdateSection,
							handlers.VersionedEmailSectionHandler.HandlerError,
						)
						humas.Post(
							api,
							"/{id}/reset",
							handlers.VersionedEmailSectionHandler.ResetSection,
							handlers.VersionedEmailSectionHandler.HandlerError,
						)
						humas.Post(
							api,
							"/release",
							handlers.VersionedEmailSectionHandler.ReleaseSections,
							handlers.VersionedEmailSectionHandler.HandlerError,
						)
					}, humas.WithTag("Versioned Email Section"))
				})

				// Merge Tags Management
				humas.Group(api, "/merge-tags", func(api huma.API) {
					humas.Group(api, "/versioned", func(api huma.API) {
						humas.Post(
							api,
							"/",
							handlers.VersionedMergeTagHandler.CreateMergeTag,
							handlers.VersionedMergeTagHandler.HandlerError,
						)
						humas.Get(
							api,
							"/",
							handlers.VersionedMergeTagHandler.ListMergeTags,
							handlers.VersionedMergeTagHandler.HandlerError,
						)
						humas.Get(
							api,
							"/{id}",
							handlers.VersionedMergeTagHandler.GetMergeTag,
							handlers.VersionedMergeTagHandler.HandlerError,
						)
						humas.Put(
							api,
							"/{id}",
							handlers.VersionedMergeTagHandler.UpdateMergeTag,
							handlers.VersionedMergeTagHandler.HandlerError,
						)
						humas.Post(
							api,
							"/{id}/reset",
							handlers.VersionedMergeTagHandler.ResetMergeTag,
							handlers.VersionedMergeTagHandler.HandlerError,
						)
						humas.Post(
							api,
							"/release",
							handlers.VersionedMergeTagHandler.ReleaseMergeTags,
							handlers.VersionedMergeTagHandler.HandlerError,
						)
					}, humas.WithTag("Versioned Merge Tag"))
				})
			})
		})

		humas.Group(api, "/public", func(api huma.API) {
			humas.Group(api, "/email", func(api huma.API) {
				humas.Post(
					api,
					"/unsubscribe",
					handlers.RecipientProfileHandler.UnsubscribeHandler,
					handlers.RecipientProfileHandler.UnsubscribeErrorHandler,
				)
			})
		})
	})

	return server
}

func LoadConfig(logger *log.Logger) (conf.AppConf, error) {
	return conf.LoadConfig(logger)
}

func PreStartSetup(ctx context.Context, logger *log.Logger) {
	logger.InfoCtx(
		ctx,
		"who am i?",
		zap.String("commit", whoami.CommitHash()),
		zap.String("build_at", whoami.BuildAt()),
	)
	types.RegisterEncoder()

	// Setup heytea-go-common logger
	l, err := logging.BuildZapLogger(logging.LoggerConf{
		Level:    logger.GetZapLogger().Level().String(),
		Encoding: "json", // just hardcode it for now
	})
	if err != nil {
		panic(err)
	}
	logging.SetZapLogger(l)

	gin.SetMode(gin.ReleaseMode)
}
