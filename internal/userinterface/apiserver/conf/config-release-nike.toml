[ApiServerConfig]
Port = 8080
BasePath = ""
SocketReadTimeout = "60s"
DefaultRequestTimeout = "60s"

[AdminServerConfig]
Addr = "0.0.0.0:10000"

[ApiServerConfig.NewRelic]
AppName = "pltf-nf-service-release-nike"
Enabled = true

[ApiServerConfig.NewRelic.DistributedTracer]
Enabled = true

[ApiServerConfig.NewRelic.ErrorCollector]
Enabled = true
CaptureEvents = true
IgnoreStatusCodes = [404, 400, 422, 401, 403, 409, 412]
