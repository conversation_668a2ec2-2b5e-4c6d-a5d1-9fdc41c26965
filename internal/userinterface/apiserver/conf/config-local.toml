[ApiServerConfig]
Port = 8081
BasePath = ""
SocketReadTimeout = "60s"
DefaultRequestTimeout = "60s"
WaitTimeBeforeShutdown = "1ns" # offline no need to wait
EnableMetric = false

[AdminServerConfig]
Addr = "0.0.0.0:10000"

[ApiServerConfig.NewRelic]
AppName = "pltf-nf-service-local"
Enabled = true

[ApiServerConfig.NewRelic.DistributedTracer]
Enabled = true

[ApiServerConfig.NewRelic.ErrorCollector]
Enabled = true
CaptureEvents = true
IgnoreStatusCodes = [404, 400, 422, 401, 403, 409, 412]
