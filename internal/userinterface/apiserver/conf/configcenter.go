package conf

import (
	"go.uber.org/fx"

	cc "github.com/AfterShip/config-center-sdk-go"
)

type Config struct {
	fx.Out

	GCPServiceAccountKey GCPServiceAccountKey `config:"gcp-auth.service-account"`
}

// LoadFromCC loads configs from config center.
func LoadFromCC() (Config, error) {
	// Following are the default values to be load in local environment because cc.Once
	// will do nothing in local environment.
	var config Config

	if err := cc.Once(&config); err != nil {
		return Config{}, err
	}
	return config, nil
}

/* List all the custom config types below. Custom types are used for the ease of DI. */

// GCPServiceAccountKey is a JSON secret key provided by GCP for each service account.
// Ref: https://cloud.google.com/docs/authentication/production#cloud-console
type GCPServiceAccountKey string
