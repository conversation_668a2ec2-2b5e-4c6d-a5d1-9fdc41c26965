package conf

import (
	"context"
	"os"

	"go.uber.org/fx"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/library-marketing-go/adminserver"

	"github.com/AfterShip/library-heytea-go-common/cfg"
	"github.com/AfterShip/library-heytea-go-common/http/client"
	"github.com/AfterShip/library-heytea-go-common/http/server/gins/apiserver"
	"github.com/AfterShip/library-heytea-go-common/logging"
)

type AppConf struct {
	fx.Out
	ApiServerConfig   apiserver.ServerConfig `json:"-"` // 不能打日志
	Logger            logging.LoggerConf
	HttpClient        client.HttpClientConfig `json:"-"` // 不能打日志
	AutomizelyApi     AutomizelyApiConfig     `json:"-"` // 不能打日志
	Spanner           Spanner
	PubSub            PubSub
	CloudTasks        CloudTasks
	Host              Host
	AdminServerConfig adminserver.Config `json:"-"`
}

// changing to variable from const will make conveniences for unittest.
var (
	DefaultActiveProfile = "local"
)

func LoadConfig(logger *log.Logger) (AppConf, error) {
	if os.Getenv(cfg.ActiveProfileEnvVar) == "" {
		// default active profile
		if err := os.Setenv(cfg.ActiveProfileEnvVar, DefaultActiveProfile); err != nil {
			panic(err.Error())
		}
	}

	viper, err := cfg.LoadViperConfig(cfg.ViperConfigName("config-" + os.Getenv("APP_ENV")))
	if err != nil {
		return AppConf{}, err
	}
	logging.Info(context.Background(), "loaded config",
		zap.String("config_file", viper.ConfigFileUsed()),
		zap.String("NODE_ENV", os.Getenv("NODE_ENV")),
		zap.String("APP_ENV", os.Getenv("APP_ENV")),
	)
	conf := AppConf{}
	err = cfg.ViperUnmarshal(&conf, viper, "")
	if err != nil {
		return AppConf{}, err
	}
	logging.Info(context.Background(), "config",
		zap.Any("base_path", conf.ApiServerConfig.BasePath),
		zap.Any("config", conf),
	)
	return conf, nil
}

type ServiceAuth struct {
	GCPServiceAccountFile CloudFile
}

type PubSub struct {
	ProjectID string
	Producer  struct {
		Topics []struct {
			Slug  string
			Topic string
		}
	}
}

type CloudFile struct {
	Bucket string
	Key    string
}

type Spanner struct {
	DatabasePath string
}

type AutomizelyApiConfig struct {
	fx.Out
	MessagesPlatformHost string `name:"messages_platform_host"`
	DataPlatformDMSHost  string `name:"data_platform_dms_host"`
}

type CloudTasks struct {
	Default CloudTask
}

type CloudTask struct {
	ProjectID  string
	LocationID string
	QueueID    string
}

type Host struct {
	Domain string
}
