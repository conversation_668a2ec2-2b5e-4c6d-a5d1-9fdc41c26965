package apiserver

import (
	"context"

	"go.uber.org/fx"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/library-heytea-go-common/http/server/gins/apiserver"
	"github.com/AfterShip/library-marketing-go/adminserver"
	"github.com/AfterShip/pltf-nf-message/internal/userinterface/apiserver/handlers"
)

var Module = fx.Module("api_server",
	handlers.Module,
	fx.Provide(NewAPIServer),
	fx.Provide(NewAdminServer),
	fx.Provide(LoadConfig),

	fx.Invoke(func(lc fx.Lifecycle, server *apiserver.ApiServer, adminServer *adminserver.Server, logger *log.Logger) {
		lc.Append(fx.Hook{
			OnStart: func(ctx context.Context) error {
				PreStartSetup(ctx, logger)
				server.Start()
				return adminServer.Run()
			},
			OnStop: func(ctx context.Context) error {
				server.GracefullyShutdown()
				return adminServer.Shutdown()
			},
		})
	}),
)
