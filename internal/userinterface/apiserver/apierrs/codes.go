package apierrs

import (
	"github.com/AfterShip/library-heytea-go-common/http/model"
)

//nolint:gochecknoinits // Init status code descriptions in init is a common pattern.
func init() {
	model.AddStatusCodeDescriptions(statusCodeDescriptions)
}

var statusCodeDescriptions = map[int]model.StatusCodeDescription{
	42201: {
		TypeName: "LackOfStoreConnectionKey",
		Message:  "Unprocessable request due to lack of store connection key",
	},
}
