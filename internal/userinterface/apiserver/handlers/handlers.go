package handlers

import (
	mergetaghandlers "github.com/AfterShip/pltf-nf-message/internal/userinterface/apiserver/handlers/email/mergetag"
	"github.com/AfterShip/pltf-nf-message/internal/userinterface/apiserver/handlers/email/message"
	"github.com/AfterShip/pltf-nf-message/internal/userinterface/apiserver/handlers/email/recipientprofile"
	emailsectionhandlers "github.com/AfterShip/pltf-nf-message/internal/userinterface/apiserver/handlers/email/section"
	"github.com/AfterShip/pltf-nf-message/internal/userinterface/apiserver/handlers/email/subscriptioncompliancerule"
)

type Handlers struct {
	EmailHandlers                     *message.Handlers
	VersionedEmailSectionHandler      *emailsectionhandlers.VersionedHandlers
	VersionedMergeTagHandler          *mergetaghandlers.VersionedHandlers
	RecipientProfileHandler           *recipientprofile.Handlers
	SubscriptionComplianceRuleHandler *subscriptioncompliancerule.Handlers
}

type Dependencies struct {
	EmailHandlers                     *message.Handlers
	VersionedEmailSectionHandler      *emailsectionhandlers.VersionedHandlers
	VersionedMergeTagHandler          *mergetaghandlers.VersionedHandlers
	RecipientProfileHandler           *recipientprofile.Handlers
	SubscriptionComplianceRuleHandler *subscriptioncompliancerule.Handlers
}

func NewHandlers(d Dependencies) Handlers {
	//nolint:staticcheck // TODO: remove this once we have a proper way to handle this
	return Handlers{
		EmailHandlers:                     d.EmailHandlers,
		VersionedEmailSectionHandler:      d.VersionedEmailSectionHandler,
		VersionedMergeTagHandler:          d.VersionedMergeTagHandler,
		RecipientProfileHandler:           d.RecipientProfileHandler,
		SubscriptionComplianceRuleHandler: d.SubscriptionComplianceRuleHandler,
	}
}
