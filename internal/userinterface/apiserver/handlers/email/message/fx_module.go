package message

import (
	"go.uber.org/fx"

	emailapplication "github.com/AfterShip/pltf-nf-message/internal/email/application"
)

type Params struct {
	fx.In

	ApplicationService *emailapplication.ApplicationService
}

type Result struct {
	fx.Out

	EmailHandler *Handlers
}

func New(p Params) Result {
	return Result{
		EmailHandler: NewHandlers(p.ApplicationService),
	}
}

const ModuleName = "email_handlers"

var Module = fx.Module(ModuleName,
	fx.Provide(New))
