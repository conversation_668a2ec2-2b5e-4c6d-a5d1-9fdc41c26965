package message

import (
	"context"
	"errors"

	"gopkg.in/go-playground/validator.v9"

	"github.com/AfterShip/library-heytea-go-common/http/server/apierrors"
	"github.com/AfterShip/library-heytea-go-common/xerrors"
	emailapplication "github.com/AfterShip/pltf-nf-message/internal/email/application"
)

type Handlers struct {
	ApplicationService *emailapplication.ApplicationService
}

func NewHandlers(applicationService *emailapplication.ApplicationService) *Handlers {
	return &Handlers{
		ApplicationService: applicationService,
	}
}

type RenderAndSendEmailArgs struct {
	OrganizationID  string `header:"am-organization-id"`
	HostProductCode string `header:"am-host-product-code"`
	AppPlatform     string `header:"am-app-platform"`
	AppKey          string `header:"am-app-key"`
	IdempotencyKey  string `header:"am-idempotency-key"`
	Body            struct {
		TemplateGroupID string         `json:"template_group_id" validate:"required"`
		LanguageTag     *string        `json:"language_tag"`
		Data            map[string]any `json:"data" validate:"required"`
		Recipient       struct {
			ID    string `json:"id"`
			Name  string `json:"name"`
			Email string `json:"email" validate:"required,email"`
			Type  string `json:"type"`
		} `json:"recipient"`
	} `                              body:"body" validate:"required"`
}

type RenderAndSendEmailResponse struct {
	MessageID string `json:"message_id"`
}

func (h *Handlers) RenderAndSendEmailHandler(
	ctx context.Context,
	args RenderAndSendEmailArgs,
) (RenderAndSendEmailResponse, error) {
	messageID, err := h.ApplicationService.SendEmailByTemplateGroupID(
		ctx,
		emailapplication.SendEmailByTemplateGroupIDParams{
			OrganizationID:  args.OrganizationID,
			HostProductCode: args.HostProductCode,
			AppPlatform:     args.AppPlatform,
			AppKey:          args.AppKey,
			TemplateGroupID: args.Body.TemplateGroupID,
			LanguageTag:     args.Body.LanguageTag,
			Data:            args.Body.Data,
			Recipient: emailapplication.Recipient{
				ID:    args.Body.Recipient.ID,
				Name:  args.Body.Recipient.Name,
				Email: args.Body.Recipient.Email,
				Type:  args.Body.Recipient.Type,
			},
			UniqueKey: args.IdempotencyKey,
		},
	)
	return RenderAndSendEmailResponse{
		MessageID: messageID,
	}, err
}

func (h *Handlers) RenderAndSendEmailErrorHandler(err error) apierrors.APIError {
	if errors.Is(err, xerrors.ErrBusinessRecordDuplicated) {
		return apierrors.Wrap(apierrors.CauseBy(apierrors.ErrConflict, err))
	}
	if errors.As(err, new(validator.ValidationErrors)) {
		return apierrors.Wrap(apierrors.CauseBy(apierrors.ErrUnprocessableEntity, err))
	}
	return apierrors.CauseBy(apierrors.ErrInternalError, err)
}
