package mergetaghandlers

import (
	"go.uber.org/fx"

	commoncontentapplication "github.com/AfterShip/pltf-nf-message/internal/commoncontent/application"
)

type Params struct {
	fx.In

	CommonContentApp *commoncontentapplication.ApplicationService
}

type Result struct {
	fx.Out

	VersionedHandlers *VersionedHandlers
}

func New(params Params) Result {
	return Result{
		VersionedHandlers: &VersionedHandlers{
			CommonContentApp: params.CommonContentApp,
		},
	}
}

const ModuleName = "merge_tag_handlers"

var Module = fx.Module(ModuleName,
	fx.Provide(New),
)
