package mergetaghandlers

import (
	"context"
	"fmt"

	"github.com/AfterShip/library-heytea-go-common/http/server/apierrors"
	"github.com/AfterShip/pltf-nf-infra/pkg/version"
	commoncontentapplication "github.com/AfterShip/pltf-nf-message/internal/commoncontent/application"
	mergetagdeclarationdomain "github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/mergetagdeclaration"
)

type VersionedHandlers struct {
	CommonContentApp *commoncontentapplication.ApplicationService
}

// Input/Output types for Create Merge Tag Declaration.
type CreateMergeTagInput struct {
	HostProductCode string                `header:"Am-Host-Product-Code" doc:"Product code of the host product"         required:"true" maxLength:"64" pattern:"^[a-zA-Z0-9_-]+$" patternDescription:"alphanumeric, underscore, and dash only"`
	OperatorEmail   string                `header:"Am-Employee-Email"    doc:"Email of the user creating the merge tag" required:"true"                                                                                                        format:"email"`
	Body            CreateMergeTagRequest `                                                                                                                                                                                                                   json:"body"`
}

type CreateMergeTagRequest struct {
	Handle            string                                      `json:"handle"                       doc:"Internal unique identity for the merge tag" required:"true"  maxLength:"128"  pattern:"^[a-zA-Z0-9_-]+$" patternDescription:"alphanumeric, underscore, and dash only"`
	Name              string                                      `json:"name"                         doc:"Display name of the merge tag"              required:"true"  maxLength:"256"                                                                                          minLength:"1"`
	Description       string                                      `json:"description,omitempty"        doc:"Description of the merge tag"               required:"false" maxLength:"1000"`
	Category          string                                      `json:"category,omitempty"           doc:"Category of the merge tag"                  required:"false" maxLength:"128"`
	MergeTag          string                                      `json:"merge_tag"                    doc:"The merge tag placeholder (e.g., *|NAME|*)" required:"true"  maxLength:"128"                             patternDescription:"merge tag format like *|NAME|*"`
	MergeLogic        mergetagdeclarationdomain.MergeLogic        `json:"merge_logic"                  doc:"Logic for rendering the merge tag"          required:"true"`
	ApplicableSetting mergetagdeclarationdomain.ApplicableSetting `json:"applicable_setting,omitempty" doc:"Settings for where this merge tag applies"`
	OrderingWeight    int64                                       `json:"ordering_weight,omitempty"    doc:"Ordering weight for display"                required:"false"`
}

func (h *VersionedHandlers) CreateMergeTag(
	ctx context.Context,
	input CreateMergeTagInput,
) (commoncontentapplication.VersionManagedMergeTagDeclaration, error) {
	args := commoncontentapplication.CreateVersionManagedArgs{
		HostProductCode:   input.HostProductCode,
		Handle:            input.Body.Handle,
		Name:              input.Body.Name,
		Description:       input.Body.Description,
		Category:          input.Body.Category,
		MergeTag:          input.Body.MergeTag,
		MergeLogic:        input.Body.MergeLogic,
		ApplicableSetting: input.Body.ApplicableSetting,
		OrderingWeight:    input.Body.OrderingWeight,
		OperatorEmail:     input.OperatorEmail,
	}

	result, err := h.CommonContentApp.UseCaseVersionManaged.CreateMergeTag(ctx, args)
	if err != nil {
		return commoncontentapplication.VersionManagedMergeTagDeclaration{}, fmt.Errorf(
			"failed to create merge tag: %w",
			err,
		)
	}

	return result, nil
}

// Input/Output types for Get Merge Tag Declaration.
type GetMergeTagInput struct {
	ID              string `path:"id" doc:"Unique identifier of the merge tag" required:"true"  pattern:"^[a-zA-Z0-9_-]+$" patternDescription:"alphanumeric, underscore, and dash only"`
	HostProductCode string `          doc:"Product code of the host product"   required:"false" pattern:"^[a-zA-Z0-9_-]*$" patternDescription:"alphanumeric, underscore, and dash only" header:"Am-Host-Product-Code" maxLength:"64"`
}

func (h *VersionedHandlers) GetMergeTag(
	ctx context.Context,
	input GetMergeTagInput,
) (commoncontentapplication.VersionManagedMergeTagDeclaration, error) {
	args := commoncontentapplication.GetArgs{
		ID:              input.ID,
		HostProductCode: input.HostProductCode,
	}

	result, err := h.CommonContentApp.UseCaseVersionManaged.GetMergeTag(ctx, args)
	if err != nil {
		return commoncontentapplication.VersionManagedMergeTagDeclaration{}, fmt.Errorf(
			"failed to get merge tag: %w",
			err,
		)
	}

	return result, nil
}

// Input/Output types for List Merge Tag Declarations.
type ListMergeTagsInput struct {
	HostProductCode string `header:"Am-Host-Product-Code" doc:"Product code of the host product to filter merge tags" required:"false" maxLength:"64" pattern:"^[a-zA-Z0-9_-]*$" patternDescription:"alphanumeric, underscore, and dash only"`
}

type ListMergeTagsResponse struct {
	MergeTags []commoncontentapplication.VersionManagedMergeTagDeclaration `json:"merge_tags" doc:"List of merge tags"`
}

func (h *VersionedHandlers) ListMergeTags(
	ctx context.Context,
	input ListMergeTagsInput,
) (ListMergeTagsResponse, error) {
	args := commoncontentapplication.ListArgs{
		HostProductCode: input.HostProductCode,
	}

	mergeTags, err := h.CommonContentApp.UseCaseVersionManaged.ListMergeTags(ctx, args)
	if err != nil {
		return ListMergeTagsResponse{}, fmt.Errorf("failed to list merge tags: %w", err)
	}

	return ListMergeTagsResponse{
		MergeTags: mergeTags,
	}, nil
}

// Input/Output types for Update Merge Tag Declaration.
type UpdateMergeTagInput struct {
	ID              string                `path:"id" doc:"Unique identifier of the merge tag"`
	HostProductCode string                `          doc:"Product code of the host product"         header:"Am-Host-Product-Code" maxLength:"64"`
	UpdatedBy       string                `          doc:"Email of the user updating the merge tag" header:"Am-Employee-Email"                   format:"email"`
	Body            UpdateMergeTagRequest `                                                                                                                     json:"body"`
}

type UpdateMergeTagRequest struct {
	Name              string                                      `json:"name"                         doc:"Updated display name of the merge tag"             required:"true"  maxLength:"256"  minLength:"1"`
	Description       string                                      `json:"description,omitempty"        doc:"Updated description of the merge tag"              required:"false" maxLength:"1000"`
	Category          string                                      `json:"category,omitempty"           doc:"Updated category of the merge tag"                 required:"false" maxLength:"128"`
	MergeLogic        mergetagdeclarationdomain.MergeLogic        `json:"merge_logic"                  doc:"Updated logic for rendering the merge tag"         required:"true"`
	ApplicableSetting mergetagdeclarationdomain.ApplicableSetting `json:"applicable_setting,omitempty" doc:"Updated settings for where this merge tag applies"`
	OrderingWeight    int64                                       `json:"ordering_weight,omitempty"    doc:"Updated ordering weight for display"               required:"false"`
}

func (h *VersionedHandlers) UpdateMergeTag(
	ctx context.Context,
	input UpdateMergeTagInput,
) (commoncontentapplication.VersionManagedMergeTagDeclaration, error) {
	args := commoncontentapplication.UpdateArgs{
		ID:                input.ID,
		HostProductCode:   input.HostProductCode,
		Name:              input.Body.Name,
		Description:       input.Body.Description,
		Category:          input.Body.Category,
		MergeLogic:        input.Body.MergeLogic,
		ApplicableSetting: input.Body.ApplicableSetting,
		OrderingWeight:    input.Body.OrderingWeight,
		UpdatedBy:         input.UpdatedBy,
	}

	result, err := h.CommonContentApp.UseCaseVersionManaged.UpdateMergeTag(ctx, args)
	if err != nil {
		return commoncontentapplication.VersionManagedMergeTagDeclaration{}, fmt.Errorf(
			"failed to update merge tag '%s': %w",
			input.ID,
			err,
		)
	}

	return result, nil
}

// Input/Output types for Reset Merge Tag Declaration.
type ResetMergeTagInput struct {
	HostProductCode string `header:"Am-Host-Product-Code" doc:"Product code of the host product"          required:"true" maxLength:"64" pattern:"^[a-zA-Z0-9_-]+$" patternDescription:"alphanumeric, underscore, and dash only"`
	ID              string `                              doc:"Unique identifier of the merge tag"        required:"true"                pattern:"^[a-zA-Z0-9_-]+$" patternDescription:"alphanumeric, underscore, and dash only" path:"id"`
	ResetBy         string `header:"Am-Employee-Email"    doc:"Email of the user resetting the merge tag" required:"true"                                                                                                                  format:"email"`
}

func (h *VersionedHandlers) ResetMergeTag(ctx context.Context, input ResetMergeTagInput) (struct{}, error) {
	args := commoncontentapplication.ResetArgs{
		HostProductCode: input.HostProductCode,
		ID:              input.ID,
		ResetBy:         input.ResetBy,
	}

	err := h.CommonContentApp.UseCaseVersionManaged.ResetMergeTag(ctx, args)
	if err != nil {
		return struct{}{}, fmt.Errorf("failed to reset merge tag '%s': %w", input.ID, err)
	}
	return struct{}{}, nil
}

// Input/Output types for Release Merge Tag Declarations.
type ReleaseMergeTagsInput struct {
	HostProductCode string                  `header:"Am-Host-Product-Code" doc:"Product code of the host product" required:"true" maxLength:"64" pattern:"^[a-zA-Z0-9_-]+$" patternDescription:"alphanumeric, underscore, and dash only"`
	Body            ReleaseMergeTagsRequest `                                                                                                                                                                                            json:"body"`
}

type ReleaseMergeTagsRequest struct {
	Release version.Release[mergetagdeclarationdomain.MergeTagDeclarationValue] `json:"release" doc:"Release definition with merge tag changes" required:"true"`
}

func (h *VersionedHandlers) ReleaseMergeTags(
	ctx context.Context,
	input ReleaseMergeTagsInput,
) (struct{}, error) {
	args := commoncontentapplication.ReleaseArgs{
		HostProductCode: input.HostProductCode,
		Release:         input.Body.Release,
	}

	err := h.CommonContentApp.UseCaseVersionManaged.Release(ctx, args)
	if err != nil {
		return struct{}{}, fmt.Errorf("failed to release merge tags: %w", err)
	}

	return struct{}{}, nil
}

func (h *VersionedHandlers) HandlerError(err error) apierrors.APIError {
	return apierrors.ConvertToAPIError(err)
}
