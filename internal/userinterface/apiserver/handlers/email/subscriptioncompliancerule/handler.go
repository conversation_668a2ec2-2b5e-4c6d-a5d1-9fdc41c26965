package subscriptioncompliancerule

import (
	"context"

	"github.com/AfterShip/library-heytea-go-common/http/server/apierrors"
	email "github.com/AfterShip/pltf-nf-message/internal/email/application"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/recipientprofile"
)

type Handlers struct {
	*email.ApplicationService
}

func NewHandlers(appService *email.ApplicationService) *Handlers {
	return &Handlers{
		ApplicationService: appService,
	}
}

type CreateArgs struct {
	HostProductCode string `header:"Am-Host-Product-Code" validate:"required"`
	Body            struct {
		BusinessScenario string                                `json:"business_scenario" validate:"required"`
		RuleType         recipientprofile.RuleType             `json:"rule_type" validate:"required"`
		AllowStatus      []recipientprofile.SubscriptionStatus `json:"allow_status,omitempty"`
		DenyStatus       []recipientprofile.SubscriptionStatus `json:"deny_status,omitempty"`
		Description      string                                `json:"description,omitempty"`
	} `                                                  body:"body"`
}

func (h *Handlers) Create(ctx context.Context, args CreateArgs) (recipientprofile.SubscriptionComplianceRule, error) {
	return h.ApplicationService.UseCaseSubscriptionComplianceRuleManagement.Create(
		ctx,
		email.CreateSubscriptionComplianceRuleParams{
			HostProductCode:  args.HostProductCode,
			BusinessScenario: args.Body.BusinessScenario,
			RuleType:         args.Body.RuleType,
			AllowStatus:      args.Body.AllowStatus,
			DenyStatus:       args.Body.DenyStatus,
			Description:      args.Body.Description,
		},
	)
}

func (h *Handlers) CreateErrorHandler(err error) apierrors.APIError {
	return apierrors.CauseBy(apierrors.ErrInternalError, err)
}
