package subscriptioncompliancerule

import (
	"go.uber.org/fx"

	email "github.com/AfterShip/pltf-nf-message/internal/email/application"
)

type Params struct {
	fx.In

	ApplicationService *email.ApplicationService
}

type Result struct {
	fx.Out

	SubscriptionComplianceRuleHandler *Handlers
}

func New(p Params) Result {
	return Result{
		SubscriptionComplianceRuleHandler: NewHandlers(p.ApplicationService),
	}
}

const ModuleName = "subscription_compliance_rule_handlers"

var Module = fx.Module(ModuleName,
	fx.Provide(New))
