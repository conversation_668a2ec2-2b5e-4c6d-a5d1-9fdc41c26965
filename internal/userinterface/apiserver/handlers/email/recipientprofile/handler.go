package recipientprofile

import (
	"context"
	"errors"
	"time"

	"gopkg.in/go-playground/validator.v9"

	"github.com/AfterShip/library-heytea-go-common/http/server/apierrors"
	"github.com/AfterShip/library-heytea-go-common/xerrors"
	email "github.com/AfterShip/pltf-nf-message/internal/email/application"
)

type Handlers struct {
	*email.ApplicationService
}

func NewHandlers(appService *email.ApplicationService) *Handlers {
	return &Handlers{
		ApplicationService: appService,
	}
}

type UnsubscribeArgs struct {
	Token   string `query:"t" validate:"required"`
	Version string `query:"v" validate:"required"`
}

type UnsubscribeResponse struct {
}

func (h *Handlers) UnsubscribeHandler(ctx context.Context, args UnsubscribeArgs) (UnsubscribeResponse, error) {
	err := h.ApplicationService.GlobalUnsubscribeByToken(ctx, email.GlobalUnsubscribeByTokenParams{
		Token:          args.Token,
		Version:        args.Version,
		UnsubscribedAt: time.Now(),
	})
	return UnsubscribeResponse{}, err
}

func (h *Handlers) UnsubscribeErrorHandler(err error) apierrors.APIError {
	if errors.Is(err, xerrors.ErrBusinessRecordDuplicated) {
		return apierrors.Wrap(apierrors.CauseBy(apierrors.ErrConflict, err))
	}
	if errors.As(err, new(validator.ValidationErrors)) {
		return apierrors.Wrap(apierrors.CauseBy(apierrors.ErrUnprocessableEntity, err))
	}
	return apierrors.CauseBy(apierrors.ErrInternalError, err)
}
