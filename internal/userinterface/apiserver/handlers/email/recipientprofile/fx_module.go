package recipientprofile

import (
	"go.uber.org/fx"

	email "github.com/AfterShip/pltf-nf-message/internal/email/application"
)

type Params struct {
	fx.In

	ApplicationService *email.ApplicationService
}

type Result struct {
	fx.Out

	RecipientProfileHandler *Handlers
}

func New(p Params) Result {
	return Result{
		RecipientProfileHandler: NewHandlers(p.ApplicationService),
	}
}

const ModuleName = "recipient_profile_handlers"

var Module = fx.Module(ModuleName,
	fx.Provide(New))
