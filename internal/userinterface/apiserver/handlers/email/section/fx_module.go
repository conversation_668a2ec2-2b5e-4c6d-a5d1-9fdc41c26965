package emailsectionhandlers

import (
	"go.uber.org/fx"

	emailapplication "github.com/AfterShip/pltf-nf-message/internal/email/application"
)

type Params struct {
	fx.In

	EmailApp *emailapplication.ApplicationService
}

type Result struct {
	fx.Out

	VersionedHandlers *VersionedHandlers
}

func New(params Params) Result {
	return Result{
		VersionedHandlers: &VersionedHandlers{
			EmailApp: params.EmailApp,
		},
	}
}

const ModuleName = "email_section_handlers"

var Module = fx.Module(ModuleName,
	fx.Provide(New),
)
