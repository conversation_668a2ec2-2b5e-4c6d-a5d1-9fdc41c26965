package emailsectionhandlers

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/AfterShip/library-heytea-go-common/http/server/apierrors"
	"github.com/AfterShip/pltf-nf-infra/pkg/version"
	emailapplication "github.com/AfterShip/pltf-nf-message/internal/email/application"
	sectiondeclarationdomain "github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"
)

type VersionedHandlers struct {
	EmailApp *emailapplication.ApplicationService
}

// Input/Output types for Create Email Section Declaration.
type CreateEmailSectionInput struct {
	HostProductCode string                    `header:"Am-Host-Product-Code" doc:"Product code of the host product"             required:"true" maxLength:"64" pattern:"^[a-zA-Z0-9_-]+$" patternDescription:"alphanumeric, underscore, and dash only"`
	OperatorEmail   string                    `header:"Am-Employee-Email"    doc:"Email of the user creating the email section" required:"true"                                                                                                        format:"email"`
	Body            CreateEmailSectionRequest `                                                                                                                                                                                                                       json:"body"`
}

type CreateEmailSectionRequest struct {
	Handle                 string                                     `json:"handle"                    doc:"Internal unique identity for the email section" required:"true"  maxLength:"128"  pattern:"^[a-zA-Z0-9_-]+$" patternDescription:"alphanumeric, underscore, and dash only"`
	Type                   string                                     `json:"type"                      doc:"Type of the email section"                      required:"true"  maxLength:"128"  pattern:"^[a-z_]+$"        patternDescription:"lowercase letters and underscore only"`
	Name                   string                                     `json:"name"                      doc:"Display name of the email section"              required:"true"  maxLength:"256"                                                                                          minLength:"1"`
	Description            string                                     `json:"description,omitempty"     doc:"Description of the email section"               required:"false" maxLength:"1000"`
	Category               string                                     `json:"category,omitempty"        doc:"Category of the email section"                  required:"false" maxLength:"128"`
	OrderingWeight         int64                                      `json:"ordering_weight,omitempty" doc:"Ordering weight for display"                    required:"false"`
	IconURL                string                                     `json:"icon_url,omitempty"        doc:"Icon URL for the section"                       required:"false" maxLength:"500"`
	ApplicableSetting      sectiondeclarationdomain.ApplicableSetting `json:"applicable_setting"                                                                                                                                                                                                     body:"applicable_setting"`
	LimitationSetting      sectiondeclarationdomain.LimitationSetting `json:"limitation_setting"                                                                                                                                                                                                     body:"limitation_setting"`
	InputSetting           json.RawMessage                            `json:"input_setting"                                                                                                                                                                                                          body:"input_setting"`
	SectionTemplateSetting json.RawMessage                            `json:"section_template_setting"                                                                                                                                                                                               body:"section_template_setting"`
	PresentationSettings   json.RawMessage                            `json:"presentation_settings"                                                                                                                                                                                                  body:"presentation_settings"`
}

func (h *VersionedHandlers) CreateSection(
	ctx context.Context,
	input CreateEmailSectionInput,
) (emailapplication.VersionManagedEmailSectionDeclaration, error) {
	args := emailapplication.CreateSectionDeclarationVersionManagedArgs{
		HostProductCode:        input.HostProductCode,
		Handle:                 input.Body.Handle,
		Type:                   input.Body.Type,
		Name:                   input.Body.Name,
		Description:            input.Body.Description,
		Category:               input.Body.Category,
		OrderingWeight:         input.Body.OrderingWeight,
		IconURL:                input.Body.IconURL,
		ApplicableSetting:      input.Body.ApplicableSetting,
		LimitationSetting:      input.Body.LimitationSetting,
		InputSetting:           input.Body.InputSetting,
		SectionTemplateSetting: input.Body.SectionTemplateSetting,
		PresentationSettings:   input.Body.PresentationSettings,
		OperatorEmail:          input.OperatorEmail,
	}

	result, err := h.EmailApp.SectionDeclarationVersionManaged.CreateSectionDeclaration(ctx, args)
	if err != nil {
		return emailapplication.VersionManagedEmailSectionDeclaration{}, fmt.Errorf(
			"failed to create email section: %w",
			err,
		)
	}

	return result, nil
}

// Input/Output types for Get Email Section Declaration.
type GetEmailSectionInput struct {
	ID              string `path:"id" doc:"Unique identifier of the email section" required:"true"  pattern:"^[a-zA-Z0-9_-]+$" patternDescription:"alphanumeric, underscore, and dash only"`
	HostProductCode string `          doc:"Product code of the host product"       required:"false" pattern:"^[a-zA-Z0-9_-]*$" patternDescription:"alphanumeric, underscore, and dash only" header:"Am-Host-Product-Code" maxLength:"64"`
}

func (h *VersionedHandlers) GetSection(
	ctx context.Context,
	input GetEmailSectionInput,
) (emailapplication.VersionManagedEmailSectionDeclaration, error) {
	args := emailapplication.GetSectionDeclarationArgs{
		ID:              input.ID,
		HostProductCode: input.HostProductCode,
	}

	result, err := h.EmailApp.SectionDeclarationVersionManaged.GetSectionDeclaration(ctx, args)
	if err != nil {
		return emailapplication.VersionManagedEmailSectionDeclaration{}, fmt.Errorf(
			"failed to get email section: %w",
			err,
		)
	}

	return result, nil
}

// Input/Output types for List Email Section Declarations.
type ListEmailSectionsInput struct {
	HostProductCode string `header:"Am-Host-Product-Code" doc:"Product code of the host product to filter sections" required:"false" maxLength:"64" pattern:"^[a-zA-Z0-9_-]*$" patternDescription:"alphanumeric, underscore, and dash only"`
}

type ListEmailSectionsResponse struct {
	Sections []emailapplication.VersionManagedEmailSectionDeclaration `json:"sections" doc:"List of email sections"`
}

func (h *VersionedHandlers) ListSections(
	ctx context.Context,
	input ListEmailSectionsInput,
) (ListEmailSectionsResponse, error) {
	args := emailapplication.ListSectionDeclarationsArgs{
		HostProductCode: input.HostProductCode,
	}

	sections, err := h.EmailApp.SectionDeclarationVersionManaged.ListSectionDeclarations(ctx, args)
	if err != nil {
		return ListEmailSectionsResponse{}, fmt.Errorf("failed to list email sections: %w", err)
	}

	return ListEmailSectionsResponse{
		Sections: sections,
	}, nil
}

// Input/Output types for Update Email Section Declaration.
type UpdateEmailSectionInput struct {
	ID              string                    `path:"id" doc:"Unique identifier of the email section"       required:"true" pattern:"^[a-zA-Z0-9_-]+$" patternDescription:"alphanumeric, underscore, and dash only"`
	HostProductCode string                    `          doc:"Product code of the host product"             required:"true" pattern:"^[a-zA-Z0-9_-]+$" patternDescription:"alphanumeric, underscore, and dash only" header:"Am-Host-Product-Code" maxLength:"64"`
	UpdatedBy       string                    `          doc:"Email of the user updating the email section" required:"true"                                                                                         header:"Am-Employee-Email"                   format:"email"`
	Body            UpdateEmailSectionRequest `                                                                                                                                                                                                                                 json:"body"`
}

type UpdateEmailSectionRequest struct {
	Name                   string                                     `json:"name"                      doc:"Updated display name of the email section" required:"true"  maxLength:"256"  minLength:"1"`
	Description            string                                     `json:"description,omitempty"     doc:"Updated description of the email section"  required:"false" maxLength:"1000"`
	Category               string                                     `json:"category,omitempty"        doc:"Updated category of the email section"     required:"false" maxLength:"128"`
	OrderingWeight         int64                                      `json:"ordering_weight,omitempty" doc:"Updated ordering weight for display"       required:"false"`
	IconURL                string                                     `json:"icon_url,omitempty"        doc:"Updated icon URL for the section"          required:"false" maxLength:"500"`
	ApplicableSetting      sectiondeclarationdomain.ApplicableSetting `json:"applicable_setting"                                                                                                        body:"applicable_setting"`
	LimitationSetting      sectiondeclarationdomain.LimitationSetting `json:"limitation_setting"                                                                                                        body:"limitation_setting"`
	InputSetting           json.RawMessage                            `json:"input_setting"                                                                                                             body:"input_setting"`
	SectionTemplateSetting json.RawMessage                            `json:"section_template_setting"                                                                                                  body:"section_template_setting"`
	PresentationSettings   json.RawMessage                            `json:"presentation_settings"                                                                                                     body:"presentation_settings"`
}

func (h *VersionedHandlers) UpdateSection(
	ctx context.Context,
	input UpdateEmailSectionInput,
) (emailapplication.VersionManagedEmailSectionDeclaration, error) {
	args := emailapplication.UpdateSectionDeclarationArgs{
		ID:                     input.ID,
		HostProductCode:        input.HostProductCode,
		Name:                   input.Body.Name,
		Description:            input.Body.Description,
		Category:               input.Body.Category,
		OrderingWeight:         input.Body.OrderingWeight,
		IconURL:                input.Body.IconURL,
		ApplicableSetting:      input.Body.ApplicableSetting,
		LimitationSetting:      input.Body.LimitationSetting,
		InputSetting:           input.Body.InputSetting,
		SectionTemplateSetting: input.Body.SectionTemplateSetting,
		PresentationSettings:   input.Body.PresentationSettings,
		UpdatedBy:              input.UpdatedBy,
	}

	result, err := h.EmailApp.SectionDeclarationVersionManaged.UpdateSectionDeclaration(ctx, args)
	if err != nil {
		return emailapplication.VersionManagedEmailSectionDeclaration{}, fmt.Errorf(
			"failed to update email section '%s': %w",
			input.ID,
			err,
		)
	}

	return result, nil
}

// Input/Output types for Reset Email Section Declaration.
type ResetEmailSectionInput struct {
	HostProductCode string `header:"Am-Host-Product-Code" doc:"Product code of the host product"       required:"true" maxLength:"64" pattern:"^[a-zA-Z0-9_-]+$" patternDescription:"alphanumeric, underscore, and dash only"`
	ID              string `                              doc:"Unique identifier of the email section" required:"true"                pattern:"^[a-zA-Z0-9_-]+$" patternDescription:"alphanumeric, underscore, and dash only" path:"id"`
}

func (h *VersionedHandlers) ResetSection(ctx context.Context, input ResetEmailSectionInput) (struct{}, error) {
	args := emailapplication.ResetSectionDeclarationArgs{
		HostProductCode: input.HostProductCode,
		ID:              input.ID,
	}

	err := h.EmailApp.SectionDeclarationVersionManaged.ResetSectionDeclaration(ctx, args)
	if err != nil {
		return struct{}{}, fmt.Errorf("failed to reset email section '%s': %w", input.ID, err)
	}
	return struct{}{}, nil
}

// Input/Output types for Release Email Section Declarations.
type ReleaseEmailSectionsInput struct {
	HostProductCode string                      `header:"Am-Host-Product-Code" doc:"Product code of the host product" required:"true" maxLength:"64" pattern:"^[a-zA-Z0-9_-]+$" patternDescription:"alphanumeric, underscore, and dash only"`
	Body            ReleaseEmailSectionsRequest `                                                                                                                                                                                            json:"body"`
}

type ReleaseEmailSectionsRequest struct {
	Release version.Release[sectiondeclarationdomain.EmailSectionDeclarationValue] `json:"release" doc:"Release definition with section changes" required:"true"`
}

func (h *VersionedHandlers) ReleaseSections(
	ctx context.Context,
	input ReleaseEmailSectionsInput,
) (struct{}, error) {
	args := emailapplication.SectionDeclarationReleaseArgs{
		HostProductCode: input.HostProductCode,
		Release:         input.Body.Release,
	}

	err := h.EmailApp.SectionDeclarationVersionManaged.Release(ctx, args)
	if err != nil {
		return struct{}{}, fmt.Errorf("failed to release email sections: %w", err)
	}

	return struct{}{}, nil
}

func (h *VersionedHandlers) HandlerError(err error) apierrors.APIError {
	return apierrors.ConvertToAPIError(err)
}
