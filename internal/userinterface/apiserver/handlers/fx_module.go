package handlers

import (
	"go.uber.org/fx"

	mergetaghandlers "github.com/AfterShip/pltf-nf-message/internal/userinterface/apiserver/handlers/email/mergetag"
	"github.com/AfterShip/pltf-nf-message/internal/userinterface/apiserver/handlers/email/message"
	"github.com/AfterShip/pltf-nf-message/internal/userinterface/apiserver/handlers/email/recipientprofile"
	emailsectionhandlers "github.com/AfterShip/pltf-nf-message/internal/userinterface/apiserver/handlers/email/section"
	"github.com/AfterShip/pltf-nf-message/internal/userinterface/apiserver/handlers/email/subscriptioncompliancerule"
)

type Params struct {
	fx.In

	EmailHandlers                     *message.Handlers
	VersionedEmailSectionHandler      *emailsectionhandlers.VersionedHandlers
	VersionedMergeTagHandler          *mergetaghandlers.VersionedHandlers
	RecipientProfileHandler           *recipientprofile.Handlers
	SubscriptionComplianceRuleHandler *subscriptioncompliancerule.Handlers
}

type Result struct {
	fx.Out

	Handlers Handlers
}

func New(params Params) Result {
	return Result{
		Handlers: NewHandlers(Dependencies{
			EmailHandlers:                     params.EmailHandlers,
			VersionedEmailSectionHandler:      params.VersionedEmailSectionHandler,
			VersionedMergeTagHandler:          params.VersionedMergeTagHandler,
			RecipientProfileHandler:           params.RecipientProfileHandler,
			SubscriptionComplianceRuleHandler: params.SubscriptionComplianceRuleHandler,
		}),
	}
}

const ModuleName = "api_handlers"

var Module = fx.Module(ModuleName,
	message.Module,
	emailsectionhandlers.Module,
	mergetaghandlers.Module,
	recipientprofile.Module,
	subscriptioncompliancerule.Module,
	fx.Provide(New))
