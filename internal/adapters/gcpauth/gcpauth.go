package gcpauth

import (
	"context"
	"os"

	"github.com/coreos/etcd/pkg/ioutil"
	"go.uber.org/zap"
	"golang.org/x/oauth2/google"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-nf-message/internal/adapters/configcenter"
)

// InitGCPAuth sets GCP service account JSON key into to well-known place and sets
// GOOGLE_APPLICATION_CREDENTIALS environment variable accordingly such that all GCP
// clients or services can be initialized properly.
func InitGCPAuth(saKey configcenter.GCPServiceAccountKey, logger *log.Logger) {
	var ctx = context.TODO()
	var err error

	// We assume in local environment, the credential file is configured according to
	// GCP's default method, i.e., nothing to do.
	if os.Getenv("APP_ENV") == "local" {
		if _, err = google.FindDefaultCredentials(ctx); err != nil {
			panic(err)
		}
		logger.InfoCtx(ctx, "load gcp service account from default locations successfully.")
		return
	}

	homePath := os.Getenv("HOME")
	if len(homePath) == 0 {
		homePath, err = os.Executable()
		if err != nil {
			panic(err)
		}
	}
	filePath := homePath + "/gcp-service-account.json"
	logger.InfoCtx(ctx, "service account loaded", zap.String("accountPath", filePath))
	if err = ioutil.WriteAndSyncFile(filePath, []byte(saKey), os.ModePerm); err != nil {
		panic(err)
	}

	if err = os.Setenv("GOOGLE_APPLICATION_CREDENTIALS", filePath); err != nil {
		panic(err)
	}
}
