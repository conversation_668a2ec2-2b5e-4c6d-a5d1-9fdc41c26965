package rendersetting

import (
	"go.uber.org/fx"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/rendersetting"
)

type Params struct {
	fx.In

	Spanner spannerx.Client
}

type Result struct {
	fx.Out

	RenderSettingRepo rendersetting.RenderSettingRepository
}

func New(p Params) Result {
	return Result{
		RenderSettingRepo: NewRepository(p.Spanner),
	}
}

const moduleName = "rendersettings"

var Module = fx.Module(moduleName,
	fx.Provide(New),
)
