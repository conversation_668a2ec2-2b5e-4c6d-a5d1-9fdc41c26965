package rendersetting

import (
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx/repositorytpl"
	"github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/rendersetting"
)

type RenderSettingQuery struct {
	HostProductCode string `json:"host_product_code" sql:"omitempty,column=host_product_code,op=eq"`
	Type            string `json:"type"              sql:"omitempty,column=type,op=eq"`
	OrganizationID  string `json:"organization_id"   sql:"omitempty,column=organization_id,op=eq"`
	AppKey          string `json:"app_key"           sql:"omitempty,column=app_key,op=eq"`
	AppPlatform     string `json:"app_platform"      sql:"omitempty,column=app_platform,op=eq"`
	EffectiveScope  string `json:"effective_scope"   sql:"omitempty,column=effective_scope,op=eq"`
}

type repositoryTemplate repositorytpl.Repository[rendersetting.RenderSetting, RenderSettingQuery]

func newRepoTemplate(cli spannerx.Client) repositoryTemplate {
	return repositorytpl.New[RenderSettings, rendersetting.RenderSetting, RenderSettingQuery](
		repositorytpl.Params[RenderSettings, rendersetting.RenderSetting]{
			Spanner:    cli,
			SoftDelete: false,
			Converter:  &ConvertImpl{},
		},
	)
}
