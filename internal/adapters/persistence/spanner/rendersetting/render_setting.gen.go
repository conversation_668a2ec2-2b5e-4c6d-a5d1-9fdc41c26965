// Code generated by https://github.com/AfterShip/go-sql-relay; DO NOT EDIT.
package rendersetting

import (
	"time"

	"cloud.google.com/go/spanner"
)

type RenderSettings struct {
	RenderSettingID string             `spanner:"render_setting_id" json:"id"`
	OrganizationID  string             `spanner:"organization_id" json:"organization_id"`
	AppKey          spanner.NullString `spanner:"app_key" json:"app_key"`
	AppPlatform     spanner.NullString `spanner:"app_platform" json:"app_platform"`
	EffectiveScope  string             `spanner:"effective_scope" json:"effective_scope"`
	Type            string             `spanner:"type" json:"type"`
	Value           spanner.NullString `spanner:"value" json:"value"`
	UpdatedAt       spanner.NullTime   `spanner:"updated_at" json:"updated_at"`
	CreatedAt       time.Time          `spanner:"created_at" json:"created_at"`
	HostProductCode spanner.NullString `spanner:"host_product_code" json:"host_product_code"`
}
