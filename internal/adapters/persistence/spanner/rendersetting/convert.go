package rendersetting

import (
	"encoding/json"

	"cloud.google.com/go/spanner"

	"github.com/AfterShip/library-marketing-go/tenantscope"
	"github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/rendersetting"
)

type ConvertImpl struct{}

func (c *ConvertImpl) ConvertToDBModel(source rendersetting.RenderSetting) (RenderSettings, error) {
	return RenderSettings{
		RenderSettingID: source.Id,
		HostProductCode: spanner.NullString{
			StringVal: source.HostProductCode,
			Valid:     source.HostProductCode != "",
		},
		Type:           source.Type,
		OrganizationID: source.TenantScope.Organization().Id,
		AppKey: spanner.NullString{
			StringVal: source.TenantScope.Store().Key,
			Valid:     source.TenantScope.Store().Key != "",
		},
		AppPlatform: spanner.NullString{
			StringVal: source.TenantScope.Store().Platform,
			Valid:     source.TenantScope.Store().Platform != "",
		},
		EffectiveScope: source.TenantScope.EffectiveScope(),
		Value: spanner.NullString{
			StringVal: string(source.Value),
			Valid:     len(source.Value) > 0,
		},
		UpdatedAt: spanner.NullTime{
			Time:  source.UpdatedAt,
			Valid: !source.UpdatedAt.IsZero(),
		},
		CreatedAt: source.CreatedAt,
	}, nil
}

func (c *ConvertImpl) ConvertToDomainModel(source RenderSettings) (rendersetting.RenderSetting, error) {
	type dummy struct {
		OrganizationID string
		AppKey         string
		AppPlatform    string
		EffectiveScope string
	}
	ts, err := tenantscope.ExtractFromSpannerModel(&dummy{
		OrganizationID: source.OrganizationID,
		AppKey:         source.AppKey.StringVal,
		AppPlatform:    source.AppPlatform.StringVal,
		EffectiveScope: source.EffectiveScope,
	})
	if err != nil {
		return rendersetting.RenderSetting{}, err
	}
	return rendersetting.RenderSetting{
		TenantScope:     ts,
		HostProductCode: source.HostProductCode.StringVal,
		Id:              source.RenderSettingID,
		Type:            source.Type,
		Value:           json.RawMessage(source.Value.StringVal),
		CreatedAt:       source.CreatedAt,
		UpdatedAt:       source.UpdatedAt.Time,
	}, nil
}
