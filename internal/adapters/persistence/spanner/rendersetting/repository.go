package rendersetting

import (
	"context"
	"errors"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/library-marketing-go/tenantscope"
	"github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/rendersetting"
)

type repositoryImpl struct {
	repo repositoryTemplate
}

func NewRepository(cli spannerx.Client) rendersetting.RenderSettingRepository {
	return &repositoryImpl{
		repo: newRepoTemplate(cli),
	}
}

func (r *repositoryImpl) FindByTenantAndHostProduct(
	ctx context.Context,
	tenantScope *tenantscope.TenantScope,
	hostProductCode string,
) (rendersetting.RenderSettingAggregate, error) {
	query := RenderSettingQuery{
		HostProductCode: hostProductCode,
		OrganizationID:  tenantScope.Organization().Id,
		AppKey:          tenantScope.Store().Key,
		AppPlatform:     tenantScope.Store().Platform,
		EffectiveScope:  tenantScope.EffectiveScope(),
	}

	settings, err := r.repo.Find(ctx, query)
	if err != nil {
		return rendersetting.RenderSettingAggregate{}, err
	}

	if len(settings) == 0 {
		return rendersetting.RenderSettingAggregate{}, errors.New("render setting not found")
	}

	res := rendersetting.RenderSettingAggregate{
		RenderSettings: map[string]rendersetting.RenderSetting{},
	}
	for _, setting := range settings {
		res.RenderSettings[setting.Type] = setting
	}
	return res, nil
}

func (r *repositoryImpl) FindByID(ctx context.Context, id string) (rendersetting.RenderSetting, error) {
	return r.repo.FindByID(ctx, id)
}
