// Package transaction provides transaction implementations
package transaction

import (
	"context"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx/txnmanager"
	"github.com/AfterShip/pltf-nf-message/internal/ports"
)

type TransactionAdapter struct {
	txn txnmanager.TransactionBeginner
}

func NewTransactionAdapter(cli spannerx.Client) ports.TransactionPort {
	return &TransactionAdapter{
		txn: txnmanager.NewTxnManager(cli),
	}
}

func (s *TransactionAdapter) RunInTransaction(ctx context.Context, fn func(context.Context) error) error {
	return s.txn.RunInTransaction(ctx, fn)
}

type ReturningTransactionAdapter[T any] struct {
	txn txnmanager.TransactionBeginner
}

func NewReturningTransactionAdapter[T any](cli spannerx.Client) ports.ReturningTransactionPort[T] {
	return &ReturningTransactionAdapter[T]{
		txn: txnmanager.NewTxnManager(cli),
	}
}

func (s *ReturningTransactionAdapter[T]) RunInTransaction(
	ctx context.Context,
	fn func(context.Context) (T, error),
) (T, error) {
	var result T
	err := s.txn.RunInTransaction(ctx, func(ctx context.Context) error {
		ret, err := fn(ctx)
		if err != nil {
			return err
		}
		result = ret
		return nil
	})
	if err != nil {
		return result, err
	}
	return result, nil
}
