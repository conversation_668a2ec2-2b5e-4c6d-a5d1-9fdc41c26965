package transaction

import (
	"go.uber.org/fx"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/pltf-nf-message/internal/ports"
)

type Params struct {
	fx.In
	SpannerClient spannerx.Client
}

type Result struct {
	fx.Out
	TransactionPort ports.TransactionPort
}

func New(p Params) Result {
	return Result{
		TransactionPort: NewTransactionAdapter(p.SpannerClient),
	}
}

const moduleName = "spanner_transaction"

var Module = fx.Module(moduleName, fx.Provide(New))
