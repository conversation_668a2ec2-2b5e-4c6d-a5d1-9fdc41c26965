package templategroup

import (
	"encoding/json"

	"cloud.google.com/go/spanner"
	"github.com/samber/lo"

	"github.com/AfterShip/library-marketing-go/tenantscope"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/template"
)

type ConvertImpl struct{}

func (c *ConvertImpl) ConvertToDBModel(source template.TemplateGroup) (ContentGroup, error) {
	cgv, err := json.Marshal(source.TemplateGroupVariants)
	if err != nil {
		return ContentGroup{}, err
	}

	res := ContentGroup{
		ContentGroupID:  source.Id,
		OrganizationID:  source.TenantScope.Organization().Id,
		EffectiveScope:  source.TenantScope.EffectiveScope(),
		AppPlatform:     source.TenantScope.Store().Platform,
		AppKey:          source.TenantScope.Store().Key,
		HostProductCode: source.HostProductCode,
		MessageChannel:  "email",
		PresentationSettings: spanner.NullString{
			StringVal: string(source.PresentationSettings),
			Valid:     source.PresentationSettings != nil,
		},
		DefaultVariantID: spanner.NullString{
			StringVal: source.DefaultVariantId,
			Valid:     source.DefaultVariantId != "",
		},
		ContentGroupVariants: spanner.NullString{
			StringVal: string(cgv),
		},
		SpamReviewID: spanner.NullString{
			StringVal: source.SpamReviewId,
			Valid:     source.SpamReviewId != "",
		},
		CreatedAt: source.CreatedAt,
		UpdatedAt: spanner.NullTime{
			Time:  source.UpdatedAt,
			Valid: !source.UpdatedAt.IsZero(),
		},
	}

	return res, nil
}

func (c *ConvertImpl) ConvertToDomainModel(source ContentGroup) (template.TemplateGroup, error) {
	var templateGroupVariants []template.TemplateGroupVariant
	if source.ContentGroupVariants.Valid {
		err := json.Unmarshal([]byte(source.ContentGroupVariants.StringVal), &templateGroupVariants)
		if err != nil {
			return template.TemplateGroup{}, err
		}
	}

	ts, err := tenantscope.ExtractFromSpannerModel(source)
	if err != nil {
		return template.TemplateGroup{}, err
	}

	res := template.TemplateGroup{
		Id:              source.ContentGroupID,
		TenantScope:     ts,
		HostProductCode: source.HostProductCode,
		PresentationSettings: lo.Ternary(
			source.PresentationSettings.Valid,
			json.RawMessage(source.PresentationSettings.StringVal),
			nil,
		),
		DefaultVariantId:      source.DefaultVariantID.StringVal,
		TemplateGroupVariants: templateGroupVariants,
		SpamReviewId:          source.SpamReviewID.StringVal,
		CreatedAt:             source.CreatedAt,
		UpdatedAt:             source.UpdatedAt.Time,
	}

	return res, nil
}
