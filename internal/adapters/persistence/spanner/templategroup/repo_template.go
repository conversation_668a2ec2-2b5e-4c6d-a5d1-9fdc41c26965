package templategroup

import (
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx/repositorytpl"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/template"
)

type ContentGroupQuery struct {
	IDs []string `json:"ids" sql:"omitempty,column=content_group_id,op=in"`
}

type repositoryTemplate repositorytpl.Repository[template.TemplateGroup, ContentGroupQuery]

func newRepoTemplate(cli spannerx.Client) repositoryTemplate {
	return repositorytpl.New[ContentGroup, template.TemplateGroup, ContentGroupQuery](
		repositorytpl.Params[ContentGroup, template.TemplateGroup]{
			Spanner:    cli,
			SoftDelete: false,
			Converter:  &ConvertImpl{},
		},
	)
}
