// Code generated by https://github.com/AfterShip/go-sql-relay; DO NOT EDIT.
package templategroup

import (
	"time"

	"cloud.google.com/go/spanner"
)

type ContentGroup struct {
	ContentGroupID       string             `spanner:"content_group_id" json:"id"`
	OrganizationID       string             `spanner:"organization_id" json:"organization_id"`
	EffectiveScope       string             `spanner:"effective_scope" json:"effective_scope"`
	AppPlatform          string             `spanner:"app_platform" json:"app_platform"`
	AppKey               string             `spanner:"app_key" json:"app_key"`
	HostProductCode      string             `spanner:"host_product_code" json:"host_product_code"`
	MessageChannel       string             `spanner:"message_channel" json:"message_channel"`
	PresentationSettings spanner.NullString `spanner:"presentation_settings" json:"presentation_settings"`
	DefaultVariantID     spanner.NullString `spanner:"default_variant_id" json:"default_variant_id"`
	ContentGroupVariants spanner.NullString `spanner:"content_group_variants" json:"content_group_variants"`
	SpamReviewID         spanner.NullString `spanner:"spam_review_id" json:"spam_review_id"`
	CreatedAt            time.Time          `spanner:"created_at" json:"created_at"`
	UpdatedAt            spanner.NullTime   `spanner:"updated_at" json:"updated_at"`
}
