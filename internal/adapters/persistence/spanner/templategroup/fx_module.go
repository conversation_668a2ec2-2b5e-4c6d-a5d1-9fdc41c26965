package templategroup

import (
	"go.uber.org/fx"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/template"
)

type Params struct {
	fx.In
	SpannerClient spannerx.Client
}

type Result struct {
	fx.Out
	TemplateGroupRepository template.TemplateGroupRepository
}

func New(p Params) Result {
	return Result{
		TemplateGroupRepository: newRepositoryImpl(p.SpannerClient),
	}
}

const moduleName = "template_group_repository"

var Module = fx.Module(moduleName, fx.Provide(New))
