package templategroup

import (
	"context"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/template"
)

type repositoryImpl struct {
	repositoryTemplate
}

func newRepositoryImpl(client spannerx.Client) template.TemplateGroupRepository {
	return &repositoryImpl{
		repositoryTemplate: newRepoTemplate(client),
	}
}

func (r *repositoryImpl) FindByID(ctx context.Context, id string) (template.TemplateGroup, error) {
	return r.repositoryTemplate.FindByID(ctx, id)
}
