package spanner

import (
	"context"
	"fmt"

	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	confspanner "github.com/AfterShip/library-marketing-go/config/spanner"
)

var dBHandles = map[string]confspanner.DatabaseConfig{
	"local": {
		Project:       "aftership-test",
		Instance:      "aftership-test-asea1",
		Database:      "cv-t-core",
		InstanceCCKey: "db.spanner.common-00",
	},
	"development": {
		Project:       "aftership-test",
		Instance:      "aftership-test-asea1",
		Database:      "cv-t-core",
		InstanceCCKey: "db.spanner.common-00",
	},
	"testing": {
		Project:       "aftership-test",
		Instance:      "aftership-test-asea1",
		Database:      "cv-t-core",
		InstanceCCKey: "db.spanner.common-00",
	},
	"staging": {
		Project:       "aftership-pro",
		Instance:      "aftership-pro-1",
		Database:      "cv-p-core",
		InstanceCCKey: "db.spanner.common-00",
	},
	"production": {
		Project:       "aftership-pro",
		Instance:      "aftership-pro-1",
		Database:      "cv-p-core",
		InstanceCCKey: "db.spanner.common-00",
	},
}

// NewDefaultSpannerClient creates a spannerx.Client with team's default settings.
// The initialization follows the spec of native spanner package where environment
// variables like GOOGLE_APPLICATION_CREDENTIALS and SPANNER and SPANNER_EMULATOR_HOST
// are used. See Spanner go client's document for details.
// This function will try to get spanner instance config from config center,
// if it fails in production, it will use the input config;
// if it fails in test environment, it will panic.
func NewDefaultSpannerClient() spannerx.Client {
	ctx := context.Background()
	config := dBHandles[cfg.GetCurrentStage()]
	dbName := config.FullyQualifiedName(confspanner.WithConfigCenterInstancesConfig())
	cli, err := spannerx.NewDefaultClient(ctx, dbName)
	if err != nil {
		panic(fmt.Errorf("init spannerx client to %s failed, %w", dbName, err))
	}

	return cli
}
