package mergetagdeclaration

import (
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx/repositorytpl"
	"github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/mergetagdeclaration"
)

type repositoryTemplate repositorytpl.Repository[mergetagdeclaration.MergeTagDeclaration, mergetagdeclaration.Query]

func newRepoTemplate(cli spannerx.Client) repositoryTemplate {
	return repositorytpl.New[MergeTags, mergetagdeclaration.MergeTagDeclaration, mergetagdeclaration.Query](
		repositorytpl.Params[MergeTags, mergetagdeclaration.MergeTagDeclaration]{
			Spanner:    cli,
			SoftDelete: true,
			Converter:  &ConvertImpl{},
		},
	)
}
