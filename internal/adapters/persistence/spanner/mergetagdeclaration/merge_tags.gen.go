package mergetagdeclaration

import (
	"time"

	"cloud.google.com/go/spanner"
)

type MergeTags struct {
	MergeTagID          string             `spanner:"merge_tag_id"          json:"id"`
	HostProductCode     string             `spanner:"host_product_code"     json:"host_product_code"`
	Handle              string             `spanner:"handle"                json:"handle"`
	Name                string             `spanner:"name"                  json:"name"`
	Description         spanner.NullString `spanner:"description"           json:"description"`
	Category            spanner.NullString `spanner:"category"              json:"category"`
	OrderingWeight      spanner.NullInt64  `spanner:"ordering_weight"       json:"ordering_weight"`
	Status              string             `spanner:"status"                json:"status"`
	Hidden              bool               `spanner:"hidden"                json:"hidden"`
	ApplicableSetting   string             `spanner:"applicable_setting"    json:"applicable_setting"`
	MergeTag            spanner.NullString `spanner:"merge_tag"             json:"merge_tag"`
	MergeLogic          spanner.NullString `spanner:"merge_logic"           json:"merge_logic"`
	CreatedAt           time.Time          `spanner:"created_at"            json:"created_at"`
	CreatedBy           string             `spanner:"created_by"            json:"created_by"`
	UpdatedAt           spanner.NullTime   `spanner:"updated_at"            json:"updated_at"`
	UpdatedBy           spanner.NullString `spanner:"updated_by"            json:"updated_by"`
	DeletedAt           spanner.NullTime   `spanner:"deleted_at"            json:"deleted_at"`
	ReleasedAt          spanner.NullTime   `spanner:"released_at"           json:"released_at"`
	ReleasedBy          spanner.NullString `spanner:"released_by"           json:"released_by"`
	ReleasedVersionID   spanner.NullString `spanner:"released_version_id"   json:"released_version_id"`
	RollbackedAt        spanner.NullTime   `spanner:"rollbacked_at"         json:"rollbacked_at"`
	RollbackedBy        spanner.NullString `spanner:"rollbacked_by"         json:"rollbacked_by"`
	RollbackedVersionID spanner.NullString `spanner:"rollbacked_version_id" json:"rollbacked_version_id"`
	PreviousMergeTagID  spanner.NullString `spanner:"previous_merge_tag_id" json:"previous_merge_tag_id"`
}
