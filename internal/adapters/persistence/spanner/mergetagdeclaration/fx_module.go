package mergetagdeclaration

import (
	"go.uber.org/fx"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/mergetagdeclaration"
)

type Params struct {
	fx.In
	SpannerClient spannerx.Client
}

type Result struct {
	fx.Out
	MergeTagRepository mergetagdeclaration.MergeTagDeclarationRepository
}

func New(p Params) Result {
	return Result{
		MergeTagRepository: NewRepositoryImpl(p.SpannerClient),
	}
}

const moduleName = "merge_tag_repository"

var Module = fx.Module(moduleName, fx.Provide(New))
