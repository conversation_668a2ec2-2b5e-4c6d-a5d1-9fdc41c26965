package mergetagdeclaration

import (
	"encoding/json"
	"fmt"

	"cloud.google.com/go/spanner"
	"github.com/samber/lo"

	"github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/mergetagdeclaration"
)

type ConvertImpl struct{}

func (c *ConvertImpl) ConvertToDBModel(source mergetagdeclaration.MergeTagDeclaration) (MergeTags, error) {
	applicableSetting, err := json.Marshal(source.ApplicableSetting)
	if err != nil {
		return MergeTags{}, err
	}
	return MergeTags{
		MergeTagID:      source.ID,
		HostProductCode: source.HostProductCode,
		Handle:          source.Handle,
		Name:            source.Name,
		Description: spanner.NullString{
			StringVal: source.Description,
			Valid:     source.Description != "",
		},
		Category: spanner.NullString{
			StringVal: source.Category,
			Valid:     source.Category != "",
		},
		OrderingWeight: spanner.NullInt64{
			Int64: source.OrderingWeight,
			Valid: source.OrderingWeight != 0,
		},
		Status:            "released",
		Hidden:            source.Hidden,
		ApplicableSetting: string(applicableSetting),
		MergeTag: spanner.NullString{
			StringVal: source.MergeTag,
			Valid:     source.MergeTag != "",
		},
		MergeLogic: convertMergeLogicToSpannerNullString(source.MergeLogic),
		CreatedAt:  source.CreatedAt,
		UpdatedAt: spanner.NullTime{
			Time:  lo.FromPtr(source.UpdatedAt),
			Valid: !source.UpdatedAt.IsZero(),
		},
		DeletedAt: spanner.NullTime{
			Time:  lo.FromPtr(source.DeletedAt),
			Valid: !source.DeletedAt.IsZero(),
		},
		// Set default values for removed fields that still exist in DB
		CreatedBy:           "system",
		UpdatedBy:           spanner.NullString{Valid: false},
		ReleasedAt:          spanner.NullTime{Valid: false},
		ReleasedBy:          spanner.NullString{Valid: false},
		ReleasedVersionID:   spanner.NullString{Valid: false},
		RollbackedAt:        spanner.NullTime{Valid: false},
		RollbackedBy:        spanner.NullString{Valid: false},
		RollbackedVersionID: spanner.NullString{Valid: false},
		PreviousMergeTagID:  spanner.NullString{Valid: false},
	}, nil
}

func (c *ConvertImpl) ConvertToDomainModel(source MergeTags) (mergetagdeclaration.MergeTagDeclaration, error) {
	applicableSetting := mergetagdeclaration.ApplicableSetting{}
	err := json.Unmarshal([]byte(source.ApplicableSetting), &applicableSetting)
	if err != nil {
		return mergetagdeclaration.MergeTagDeclaration{}, err
	}
	return mergetagdeclaration.MergeTagDeclaration{
		ID:                source.MergeTagID,
		HostProductCode:   source.HostProductCode,
		Handle:            source.Handle,
		Name:              source.Name,
		Description:       source.Description.StringVal,
		Category:          source.Category.StringVal,
		OrderingWeight:    source.OrderingWeight.Int64,
		Hidden:            source.Hidden,
		ApplicableSetting: applicableSetting,
		MergeTag:          source.MergeTag.StringVal,
		MergeLogic:        convertNullStringToMergeLogic(source.MergeLogic),
		CreatedAt:         source.CreatedAt,
		UpdatedAt:         lo.Ternary(source.UpdatedAt.Valid, &source.UpdatedAt.Time, nil),
		DeletedAt:         lo.Ternary(source.DeletedAt.Valid, &source.DeletedAt.Time, nil),
	}, nil
}

func convertMergeLogicToSpannerNullString(mergeLogic mergetagdeclaration.MergeLogic) spanner.NullString {
	data, err := json.Marshal(mergeLogic)
	if err != nil {
		panic(fmt.Errorf("failed to marshal MergeLogic: %w", err))
	}
	return spanner.NullString{
		StringVal: string(data),
		Valid:     true,
	}
}

func convertNullStringToMergeLogic(source spanner.NullString) mergetagdeclaration.MergeLogic {
	if !source.Valid {
		return mergetagdeclaration.MergeLogic{}
	}
	var mergeLogic mergetagdeclaration.MergeLogic
	err := json.Unmarshal([]byte(source.StringVal), &mergeLogic)
	if err != nil {
		panic(fmt.Errorf("failed to unmarshal MergeLogic: %w", err))
	}
	return mergeLogic
}
