package mergetagdeclaration

import (
	"context"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/pltf-nf-message/internal/commoncontent/domain/mergetagdeclaration"
)

type repositoryImpl struct {
	repositoryTemplate
}

func NewRepositoryImpl(cli spannerx.Client) mergetagdeclaration.MergeTagDeclarationRepository {
	return &repositoryImpl{
		repositoryTemplate: newRepoTemplate(cli),
	}
}

func (r *repositoryImpl) FindByHostProductCode(
	ctx context.Context,
	hostProductCode string,
) ([]mergetagdeclaration.MergeTagDeclaration, error) {
	return r.repositoryTemplate.Find(ctx, mergetagdeclaration.Query{
		HostProductCode: hostProductCode,
	})
}
