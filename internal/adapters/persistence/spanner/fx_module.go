package spanner

import (
	"go.uber.org/fx"

	"github.com/AfterShip/pltf-nf-message/internal/adapters/persistence/spanner/emailsectiondeclaration"
	"github.com/AfterShip/pltf-nf-message/internal/adapters/persistence/spanner/templategroup"
	"github.com/AfterShip/pltf-nf-message/internal/adapters/persistence/spanner/templatevariant"
	"github.com/AfterShip/pltf-nf-message/internal/adapters/persistence/spanner/transaction"
)

const ModuleName = "spanner"

var Module = fx.Module(ModuleName,
	fx.Provide(NewDefaultSpannerClient),
	emailsectiondeclaration.Module,
	templategroup.Module,
	templatevariant.Module,
	transaction.Module,
)
