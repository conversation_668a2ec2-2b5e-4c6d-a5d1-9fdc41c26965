package emailsectiondeclaration

import (
	"encoding/json"
	"fmt"

	"cloud.google.com/go/spanner"
	"github.com/samber/lo"

	"github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"
)

type ConvertImpl struct{}

func (c *ConvertImpl) ConvertToDBModel(source sectiondeclaration.EmailSectionDeclaration) (EmailSection, error) {
	res := EmailSection{
		ContentSectionID: source.ID,
		HostProductCode:  source.HostProductCode,
		Handle:           source.Handle,
		Type:             source.Type,
		Name:             source.Name,
		Description: spanner.NullString{
			StringVal: source.Description,
			Valid:     source.Description != "",
		},
		Category: spanner.NullString{
			StringVal: source.Category,
			Valid:     source.Category != "",
		},
		OrderingWeight: spanner.NullInt64{
			Int64: source.OrderingWeight,
			Valid: source.OrderingWeight != 0,
		},
		IconURL:                source.IconURL,
		ApplicableSetting:      convertApplicableSettingToString(source.ApplicableSetting),
		LimitationSetting:      convertLimitationSettingToString(source.LimitationSetting),
		InputSetting:           string(source.InputSetting),
		SectionTemplateSetting: string(source.SectionTemplateSetting),
		PresentationSettings:   string(source.PresentationSettings),
		Status:                 "released",
		Hidden:                 source.Hidden,
		CreatedAt:              source.CreatedAt,
		UpdatedAt: spanner.NullTime{
			Time:  lo.FromPtr(source.UpdatedAt),
			Valid: !source.UpdatedAt.IsZero(),
		},
		DeletedAt: spanner.NullTime{
			Time:  lo.FromPtr(source.DeletedAt),
			Valid: !source.DeletedAt.IsZero(),
		},
		// Set default values for removed fields that still exist in DB
		CreatedBy:                "system",
		UpdatedBy:                spanner.NullString{Valid: false},
		ReleasedAt:               spanner.NullTime{Valid: false},
		ReleasedBy:               spanner.NullString{Valid: false},
		ReleasedVersionID:        spanner.NullString{Valid: false},
		RollbackedAt:             spanner.NullTime{Valid: false},
		RollbackedBy:             spanner.NullString{Valid: false},
		RollbackedVersionID:      spanner.NullString{Valid: false},
		PreviousContentSectionID: spanner.NullString{Valid: false},
	}
	return res, nil
}

func (c *ConvertImpl) ConvertToDomainModel(source EmailSection) (sectiondeclaration.EmailSectionDeclaration, error) {
	applicableSetting := sectiondeclaration.ApplicableSetting{}
	err := json.Unmarshal([]byte(source.ApplicableSetting), &applicableSetting)
	if err != nil {
		return sectiondeclaration.EmailSectionDeclaration{}, err
	}

	limitationSetting := sectiondeclaration.LimitationSetting{}
	err = json.Unmarshal([]byte(source.LimitationSetting), &limitationSetting)
	if err != nil {
		return sectiondeclaration.EmailSectionDeclaration{}, err
	}

	return sectiondeclaration.EmailSectionDeclaration{
		ID:                     source.ContentSectionID,
		HostProductCode:        source.HostProductCode,
		Handle:                 source.Handle,
		Type:                   source.Type,
		Name:                   source.Name,
		Description:            source.Description.StringVal,
		Category:               source.Category.StringVal,
		OrderingWeight:         source.OrderingWeight.Int64,
		IconURL:                source.IconURL,
		ApplicableSetting:      applicableSetting,
		LimitationSetting:      limitationSetting,
		InputSetting:           json.RawMessage(source.InputSetting),
		SectionTemplateSetting: json.RawMessage(source.SectionTemplateSetting),
		PresentationSettings:   json.RawMessage(source.PresentationSettings),
		Hidden:                 source.Hidden,
		CreatedAt:              source.CreatedAt,
		UpdatedAt:              lo.Ternary(source.UpdatedAt.Valid, &source.UpdatedAt.Time, nil),
		DeletedAt:              lo.Ternary(source.DeletedAt.Valid, &source.DeletedAt.Time, nil),
	}, nil
}

func convertApplicableSettingToString(source sectiondeclaration.ApplicableSetting) string {
	data, err := json.Marshal(source)
	if err != nil {
		panic(fmt.Errorf("failed to marshal ApplicableSetting: %w", err))
	}
	return string(data)
}

func convertLimitationSettingToString(source sectiondeclaration.LimitationSetting) string {
	data, err := json.Marshal(source)
	if err != nil {
		panic(fmt.Errorf("failed to marshal LimitationSetting: %w", err))
	}
	return string(data)
}
