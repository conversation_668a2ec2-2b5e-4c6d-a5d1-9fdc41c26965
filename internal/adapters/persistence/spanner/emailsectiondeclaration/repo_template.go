package emailsectiondeclaration

import (
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx/repositorytpl"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"
)

type repositoryTemplate repositorytpl.Repository[sectiondeclaration.EmailSectionDeclaration, sectiondeclaration.Query]

func newRepoTemplate(cli spannerx.Client) repositoryTemplate {
	return repositorytpl.New[EmailSection, sectiondeclaration.EmailSectionDeclaration, sectiondeclaration.Query](
		repositorytpl.Params[EmailSection, sectiondeclaration.EmailSectionDeclaration]{
			Spanner:    cli,
			SoftDelete: true,
			Converter:  &ConvertImpl{},
		},
	)
}
