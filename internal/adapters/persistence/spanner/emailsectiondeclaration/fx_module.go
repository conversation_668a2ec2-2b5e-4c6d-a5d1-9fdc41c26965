package emailsectiondeclaration

import (
	"go.uber.org/fx"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"
)

type Params struct {
	fx.In
	SpannerClient spannerx.Client
}

type Result struct {
	fx.Out
	EmailSectionDeclarationRepository sectiondeclaration.EmailSectionDeclarationRepository
}

func New(p Params) Result {
	return Result{
		EmailSectionDeclarationRepository: NewRepositoryImpl(p.SpannerClient),
	}
}

const moduleName = "email_section_declaration_repository"

var Module = fx.Module(moduleName, fx.Provide(New))
