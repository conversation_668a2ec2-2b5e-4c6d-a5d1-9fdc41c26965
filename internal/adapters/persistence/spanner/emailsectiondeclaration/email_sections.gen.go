package emailsectiondeclaration

import (
	"time"

	"cloud.google.com/go/spanner"
)

type EmailSection struct {
	ContentSectionID         string             `spanner:"content_section_id"          json:"content_section_id"`
	HostProductCode          string             `spanner:"host_product_code"           json:"host_product_code"`
	Handle                   string             `spanner:"handle"                      json:"handle"`
	Type                     string             `spanner:"type"                        json:"type"`
	Name                     string             `spanner:"name"                        json:"name"`
	Description              spanner.NullString `spanner:"description"                 json:"description"`
	Category                 spanner.NullString `spanner:"category"                    json:"category"`
	OrderingWeight           spanner.NullInt64  `spanner:"ordering_weight"             json:"ordering_weight"`
	Status                   string             `spanner:"status"                      json:"status"`
	IconURL                  string             `spanner:"icon_url"                    json:"icon_url"`
	ApplicableSetting        string             `spanner:"applicable_setting"          json:"applicable_setting"`
	LimitationSetting        string             `spanner:"limitation_setting"          json:"limitation_setting"`
	InputSetting             string             `spanner:"input_setting"               json:"input_setting"`
	SectionTemplateSetting   string             `spanner:"section_template_setting"    json:"section_template_setting"`
	PresentationSettings     string             `spanner:"presentation_settings"       json:"presentation_settings"`
	Hidden                   bool               `spanner:"hidden"                      json:"hidden"`
	CreatedAt                time.Time          `spanner:"created_at"                  json:"created_at"`
	CreatedBy                string             `spanner:"created_by"                  json:"created_by"`
	UpdatedAt                spanner.NullTime   `spanner:"updated_at"                  json:"updated_at"`
	UpdatedBy                spanner.NullString `spanner:"updated_by"                  json:"updated_by"`
	DeletedAt                spanner.NullTime   `spanner:"deleted_at"                  json:"deleted_at"`
	ReleasedAt               spanner.NullTime   `spanner:"released_at"                 json:"released_at"`
	ReleasedBy               spanner.NullString `spanner:"released_by"                 json:"released_by"`
	ReleasedVersionID        spanner.NullString `spanner:"released_version_id"         json:"released_version_id"`
	RollbackedAt             spanner.NullTime   `spanner:"rollbacked_at"               json:"rollbacked_at"`
	RollbackedBy             spanner.NullString `spanner:"rollbacked_by"               json:"rollbacked_by"`
	RollbackedVersionID      spanner.NullString `spanner:"rollbacked_version_id"       json:"rollbacked_version_id"`
	PreviousContentSectionID spanner.NullString `spanner:"previous_content_section_id" json:"previous_content_section_id"`
}
