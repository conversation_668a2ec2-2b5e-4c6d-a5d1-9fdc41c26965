package emailsectiondeclaration

import (
	"context"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/sectiondeclaration"
)

type repositoryImpl struct {
	repositoryTemplate
}

func NewRepositoryImpl(cli spannerx.Client) sectiondeclaration.EmailSectionDeclarationRepository {
	return &repositoryImpl{
		repositoryTemplate: newRepoTemplate(cli),
	}
}

func (r *repositoryImpl) FindByHostProductCode(
	ctx context.Context,
	hostProductCode string,
) ([]sectiondeclaration.EmailSectionDeclaration, error) {
	return r.repositoryTemplate.Find(ctx, sectiondeclaration.Query{
		HostProductCode: hostProductCode,
	})
}
