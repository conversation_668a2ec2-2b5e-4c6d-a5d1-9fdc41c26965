// Code generated by https://github.com/AfterShip/go-sql-relay; DO NOT EDIT.
package templatevariant

import (
	"time"

	"cloud.google.com/go/spanner"
)

type ContentVariant struct {
	ContentVariantID     string             `spanner:"content_variant_id" json:"id"`
	OrganizationID       string             `spanner:"organization_id" json:"organization_id"`
	EffectiveScope       string             `spanner:"effective_scope" json:"effective_scope"`
	AppPlatform          string             `spanner:"app_platform" json:"app_platform"`
	AppKey               string             `spanner:"app_key" json:"app_key"`
	HostProductCode      string             `spanner:"host_product_code" json:"host_product_code"`
	MessageChannel       string             `spanner:"message_channel" json:"message_channel"`
	ContentSource        string             `spanner:"content_source" json:"content_source"`
	Name                 spanner.NullString `spanner:"name" json:"name"`
	LanguageTag          spanner.NullString `spanner:"language_tag" json:"language_tag"`
	PresentationSettings spanner.NullString `spanner:"presentation_settings" json:"presentation_settings"`
	SmsTemplate          spanner.NullString `spanner:"sms_template" json:"sms_template"`
	EmailTemplate        spanner.NullString `spanner:"email_template" json:"email_template"`
	Checksum             string             `spanner:"checksum" json:"checksum"`
	CreatedAt            time.Time          `spanner:"created_at" json:"created_at"`
	UpdatedAt            spanner.NullTime   `spanner:"updated_at" json:"updated_at"`
}
