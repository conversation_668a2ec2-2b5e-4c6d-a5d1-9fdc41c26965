package templatevariant

import (
	"encoding/json"

	"cloud.google.com/go/spanner"
	"github.com/samber/lo"

	"github.com/AfterShip/library-marketing-go/tenantscope"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/template"
)

type ConvertImpl struct{}

func (c *ConvertImpl) ConvertToDBModel(source template.TemplateVariant) (ContentVariant, error) {
	emailTmpl, err := json.Marshal(source.EmailTemplate)
	if err != nil {
		return ContentVariant{}, err
	}

	res := ContentVariant{
		ContentVariantID: source.Id,
		OrganizationID:   source.TenantScope.Organization().Id,
		EffectiveScope:   source.TenantScope.EffectiveScope(),
		AppPlatform:      source.TenantScope.Store().Platform,
		AppKey:           source.TenantScope.Store().Key,
		HostProductCode:  source.HostProductCode,
		MessageChannel:   "email",
		ContentSource:    source.ContentSource,
		Name: spanner.NullString{
			StringVal: source.Name,
			Valid:     source.Name != "",
		},
		LanguageTag: spanner.NullString{
			StringVal: source.LanguageTag,
			Valid:     source.LanguageTag != "",
		},
		PresentationSettings: spanner.NullString{
			StringVal: string(source.PresentationSettings),
			Valid:     source.PresentationSettings != nil,
		},
		SmsTemplate: spanner.NullString{},
		EmailTemplate: spanner.NullString{
			StringVal: string(emailTmpl),
			Valid:     string(emailTmpl) != "",
		},
		Checksum:  source.Checksum,
		CreatedAt: source.CreatedAt,
		UpdatedAt: spanner.NullTime{
			Time:  source.UpdatedAt,
			Valid: source.UpdatedAt.IsZero(),
		},
	}

	return res, nil
}

func (c *ConvertImpl) ConvertToDomainModel(source ContentVariant) (template.TemplateVariant, error) {
	var emailTemplate template.EmailTemplate
	if source.EmailTemplate.Valid {
		if err := json.Unmarshal([]byte(source.EmailTemplate.StringVal), &emailTemplate); err != nil {
			return template.TemplateVariant{}, err
		}
	}

	ts, err := tenantscope.ExtractFromSpannerModel(source)
	if err != nil {
		return template.TemplateVariant{}, err
	}

	res := template.TemplateVariant{
		Id:              source.ContentVariantID,
		TenantScope:     ts,
		HostProductCode: source.HostProductCode,
		ContentSource:   source.ContentSource,
		Name:            source.Name.StringVal,
		LanguageTag:     source.LanguageTag.StringVal,
		PresentationSettings: lo.Ternary(
			source.PresentationSettings.Valid,
			json.RawMessage(source.PresentationSettings.StringVal),
			nil,
		),
		EmailTemplate: emailTemplate,
		Checksum:      source.Checksum,
		CreatedAt:     source.CreatedAt,
		UpdatedAt:     source.UpdatedAt.Time,
	}

	return res, nil
}
