package templatevariant

import (
	"go.uber.org/fx"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/template"
)

type Params struct {
	fx.In

	Client spannerx.Client
}

type Result struct {
	fx.Out

	TemplateVariantRepository template.TemplateVariantRepository
}

func New(params Params) Result {
	return Result{
		TemplateVariantRepository: newRepositoryImpl(params.Client),
	}
}

const ModuleName = "template_variant_repository"

var Module = fx.Module(ModuleName,
	fx.Provide(New))
