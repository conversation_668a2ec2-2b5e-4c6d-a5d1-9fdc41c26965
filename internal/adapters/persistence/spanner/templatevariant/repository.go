package templatevariant

import (
	"context"

	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/template"
)

func newRepositoryImpl(cli spannerx.Client) template.TemplateVariantRepository {
	return &repositoryImpl{
		repositoryTemplate: newRepoTemplate(cli),
	}
}

type repositoryImpl struct {
	repositoryTemplate
}

func (r *repositoryImpl) FindByID(ctx context.Context, id string) (template.TemplateVariant, error) {
	return r.repositoryTemplate.FindByID(ctx, id)
}
