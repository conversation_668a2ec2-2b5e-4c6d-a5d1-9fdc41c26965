package templatevariant

import (
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx"
	"github.com/AfterShip/library-heytea-go-common/storage/spannerx/repositorytpl"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/template"
)

type ContentVariantQuery struct {
	IDs []string `json:"ids" sql:"omitempty,column=content_variant_id,op=in"`
}

type repositoryTemplate repositorytpl.Repository[template.TemplateVariant, ContentVariantQuery]

func newRepoTemplate(cli spannerx.Client) repositoryTemplate {
	return repositorytpl.New[ContentVariant, template.TemplateVariant, ContentVariantQuery](
		repositorytpl.Params[ContentVariant, template.TemplateVariant]{
			Spanner:    cli,
			SoftDelete: false,
			Converter:  &ConvertImpl{},
		},
	)
}
