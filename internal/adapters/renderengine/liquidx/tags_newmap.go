package liquidx

import (
	"errors"

	"github.com/carlosxl/pongo2/v6"
)

//nolint:gochecknoinits // Init built-in tags in init is a common pattern.
func init() {
	err := pongo2.RegisterTag("newmap", tagNewMapParser)
	if err != nil {
		panic(err)
	}
}

type tagNewMapNode struct {
	name       string
	expression pongo2.IEvaluator
}

func (node *tagNewMapNode) Execute(ctx *pongo2.ExecutionContext, writer pongo2.TemplateWriter) *pongo2.Error {
	newmap, evaluateErr := node.evaluateToMap(ctx)
	if evaluateErr != nil {
		return ctx.Error(evaluateErr.Error(), node.expression.GetPositionToken())
	}
	ctx.Private[node.name] = newmap
	return nil
}

func (node *tagNewMapNode) evaluateToMap(ctx *pongo2.ExecutionContext) (map[string]any, error) {
	// Evaluate expression
	value, err := node.expression.Evaluate(ctx)
	if err != nil {
		return nil, err
	}
	if value == nil || value.IsNil() {
		return map[string]any{}, nil
	}
	if !value.CanSlice() {
		return nil, errors.New("newmap arguments must be a slice")
	}
	if value.Len() == 0 {
		return make(map[string]any), nil
	}
	values, ok := value.Interface().([]*pongo2.Value)
	if !ok {
		return nil, errors.New("newmap arguments must be a slice")
	}
	return node.newmap(values...)
}

func (node *tagNewMapNode) newmap(entries ...*pongo2.Value) (map[string]any, error) {
	var obj = map[string]any{}
	for _, entry := range entries {
		if entry == nil {
			continue
		}
		entry, ok := entry.Interface().([]*pongo2.Value)
		if !ok {
			return nil, errors.New("newmap entry must be a slice")
		}
		if len(entry) != 2 { //nolint:mnd // This is a valid check.
			return nil, errors.New("newmap entry must be a slice with length 2")
		}
		if !entry[0].IsString() {
			return nil, errors.New("newmap entry key must be a string")
		}
		obj[entry[0].String()] = entry[1].Interface()
	}
	return obj, nil
}

func tagNewMapParser(
	doc *pongo2.Parser,
	start *pongo2.Token,
	arguments *pongo2.Parser,
) (pongo2.INodeTag, *pongo2.Error) {
	node := &tagNewMapNode{}

	// Parse variable name
	typeToken := arguments.MatchType(pongo2.TokenIdentifier)
	if typeToken == nil {
		return nil, arguments.Error("Expected an identifier.", nil)
	}
	node.name = typeToken.Val

	if arguments.Match(pongo2.TokenSymbol, "=") == nil {
		return nil, arguments.Error("Expected '='.", nil)
	}

	// Variable expression
	keyExpression, err := arguments.ParseExpression()
	if err != nil {
		return nil, err
	}
	node.expression = keyExpression

	// Remaining arguments
	if arguments.Remaining() > 0 {
		return nil, arguments.Error("Malformed 'set'-tag arguments.", nil)
	}

	return node, nil
}
