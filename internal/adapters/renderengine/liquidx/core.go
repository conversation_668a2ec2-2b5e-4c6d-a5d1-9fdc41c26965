package liquidx

import (
	"github.com/carlosxl/pongo2/v6"

	"github.com/AfterShip/library-heytea-go-common/xerrors"
)

type Context map[string]any

type LiquidContextConvertable interface {
	ToLiquidContext() Context
}

type Engine interface {
	RenderTemplateString(tmpl string, ctx Context) (string, error)
}

var (
	// DefaultEngine is the renderer created for you for convenience reasons.
	DefaultEngine = newPongo2Renderer()
)

type pongo2Renderer struct {
	templateSet *pongo2.TemplateSet
}

func newPongo2Renderer() *pongo2Renderer {
	renderer := &pongo2Renderer{
		templateSet: pongo2.NewSet("liquidx", pongo2.DefaultLoader),
	}
	// include/import/extends/ssi can read any file on the server, so we ban them
	// future work include sanitizing `{` and `}` in the template
	banTags := []string{"include", "import", "extends", "ssi"}
	for _, tag := range banTags {
		err := renderer.templateSet.BanTag(tag)
		if err != nil {
			panic("pongo2 renderer: cannot ban include tag")
		}
	}

	// We allow access struct fields by their json tags.
	// E.g.:
	// type user struct {
	//     FirstName string `json:"first_name"`
	// }
	// {{ user.first_name }} will be rendered as the value of `user.FirstName`.
	pongo2.SetFieldAliasTag("json")
	return renderer
}

func (p *pongo2Renderer) RenderTemplateString(tmpl string, ctx Context) (string, error) {
	t, err := p.templateSet.FromString(tmpl)
	if err != nil {
		return "", xerrors.Wrap(err)
	}
	return t.Execute(pongo2.Context(ctx))
}
