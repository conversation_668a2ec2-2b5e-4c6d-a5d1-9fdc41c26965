package liquidx

import (
	"strings"

	"github.com/carlosxl/pongo2/v6"
	"github.com/fatih/camelcase"
	"github.com/samber/lo"

	"github.com/AfterShip/library-heytea-go-common/utils/currencyformatter"
)

//nolint:gochecknoinits // Init built-in filters in init is a common pattern.
func init() {
	// We ignore the error here because we know that the filter name is unique.
	_ = pongo2.RegisterFilter("chunks", filterChunks)
	_ = pongo2.RegisterFilter("decamelize", filterDecamelize)
	_ = pongo2.RegisterFilter("currency_format", filterCurrencyFormat)
}

// filterChunks splits a list into chunks of size k.
func filterChunks(in *pongo2.Value, k *pongo2.Value) (*pongo2.Value, *pongo2.Error) {
	if !in.CanSlice() {
		return pongo2.AsValue([]string{}), nil
	}
	if !k.<PERSON>ger() {
		return pongo2.AsValue([]string{}), nil
	}

	var out [][]any
	var i, j = 0, 0 // [i][j]any
	in.Iterate(func(_, _ int, key, _ *pongo2.Value) bool {
		if j == 0 {
			out = append(
				out,
				make([]any, 0, k.Integer()),
			) // we always start from 0 element, in case this chunk is incomplete
		}
		out[i] = append(out[i], key.Interface())
		j++
		if j == k.Integer() {
			i++
			j = 0
		}
		return true
	}, func() {}) // empty func will be called if `in` is not iterable

	return pongo2.AsValue(out), nil
}

// filterDecamelize decamelizes a string.
func filterDecamelize(in *pongo2.Value, _ *pongo2.Value) (*pongo2.Value, *pongo2.Error) {
	if !in.IsString() {
		return pongo2.AsValue(""), nil
	}
	words := lo.Map(camelcase.Split(in.String()), func(item string, _ int) string {
		return strings.ReplaceAll(item, " ", "")
	})
	words = lo.Filter(words, func(item string, _ int) bool {
		return item != ""
	})
	return pongo2.AsValue(strings.Join(words, " ")), nil
}

// filterCurrencyFormat formats a number as a currency.
// Deprecated: use the function equivalent instead because this filter assumes the output currency string is always in
// the en-US locale. The function equivalent will retrieve the locale from the context.
func filterCurrencyFormat(in *pongo2.Value, k *pongo2.Value) (*pongo2.Value, *pongo2.Error) {
	if !in.IsNumber() && !in.IsString() {
		return pongo2.AsValue(""), nil
	}
	if !k.IsString() {
		return pongo2.AsValue(""), nil
	}
	amount := in.Float()
	currency := k.String()

	return pongo2.AsValue(currencyformatter.DefaultCurrencyFormatter.ShortFormat(currency, amount, false)), nil
}
