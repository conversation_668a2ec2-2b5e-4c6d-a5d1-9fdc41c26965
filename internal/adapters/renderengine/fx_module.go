package renderengine

import (
	"go.uber.org/fx"

	"github.com/AfterShip/pltf-nf-message/internal/email/domain/template"
)

type Params struct {
	fx.In
}

type Result struct {
	fx.Out

	DjangoEngine template.RenderEngine
}

func New(params Params) Result {
	return Result{
		DjangoEngine: NewDjangoEngine(),
	}
}

const moduleName = "render_engine_adapter"

var Module = fx.Module(moduleName,
	fx.Provide(New))
