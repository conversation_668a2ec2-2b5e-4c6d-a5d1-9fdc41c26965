package renderengine

import (
	"context"
	"reflect"

	"github.com/AfterShip/pltf-nf-message/internal/adapters/renderengine/liquidx"
	"github.com/AfterShip/pltf-nf-message/internal/email/domain/template"
)

func NewDjangoEngine() template.RenderEngine {
	return &djangoEngine{
		engine: liquidx.DefaultEngine,
	}
}

type djangoEngine struct {
	engine liquidx.Engine
}

func (e *djangoEngine) RenderToHTML(
	ctx context.Context,
	template string,
	renderContext template.RenderContext,
) (string, error) {
	if template == "" {
		return "", nil
	}
	liquidCtx := convertRenderContextToLiquidContext(renderContext)
	result, err := e.engine.RenderTemplateString(template, liquidCtx)
	if err != nil {
		return "", err
	}
	return result, nil
}

func (e *djangoEngine) RenderToText(
	ctx context.Context,
	template string,
	renderContext template.RenderContext,
) (string, error) {
	if template == "" {
		return "", nil
	}
	liquidCtx := convertRenderContextToLiquidContext(renderContext)
	template = "{% autoescape off -%}" + template + "{%- endautoescape %}"
	result, err := e.engine.RenderTemplateString(template, liquidCtx)
	if err != nil {
		return "", err
	}
	return result, nil
}

func convertRenderContextToLiquidContext(renderContext template.RenderContext) liquidx.Context {
	liquidCtx := make(liquidx.Context)
	for i := range reflect.TypeOf(renderContext).NumField() {
		field := reflect.TypeOf(renderContext).Field(i)
		val := reflect.ValueOf(renderContext).Field(i)
		tga := field.Tag.Get("template")
		if tga == "-" {
			continue
		}
		if val.IsZero() {
			continue
		}
		if field.Type.Kind() == reflect.Ptr {
			val = val.Elem()
		}
		liquidCtx[tga] = val.Interface()
	}

	// Tiling RenderToHtml data
	// For the legacy merge tag and function, it accesses the data in the root level directly.
	// So to keep the backward compatibility, we need to tile the data to the root level.
	for key, value := range renderContext.Data {
		liquidCtx[key] = value
	}

	for key, value := range renderContext.TemplateFunctions {
		liquidCtx[key] = value
	}
	return liquidCtx
}
