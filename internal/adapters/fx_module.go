package adapters

import (
	"go.uber.org/fx"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-nf-message/internal/adapters/configcenter"
	"github.com/AfterShip/pltf-nf-message/internal/adapters/gcpauth"
	"github.com/AfterShip/pltf-nf-message/internal/adapters/logging"
	"github.com/AfterShip/pltf-nf-message/internal/adapters/messaging/pubsub"
	"github.com/AfterShip/pltf-nf-message/internal/adapters/persistence/spanner"
	"github.com/AfterShip/pltf-nf-message/internal/adapters/renderengine"
)

const moduleName = "adapters"

var Module = fx.Module(moduleName,
	fx.Decorate(func(logger *log.Logger) *log.Logger { return logger.Named(moduleName) }),

	configcenter.Module,
	gcpauth.Module,
	logging.Module,
	pubsub.Module,
	renderengine.Module,
	spanner.Module)
