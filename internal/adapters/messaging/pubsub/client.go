package pubsub

import (
	"os"
	"strings"

	"golang.org/x/net/context"

	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/gopkg/pubsubx"
)

const (
	projectIDTest       = "aftership-test"
	projectIDProduction = "aftership-pro"
)

var projectIDMap = map[string]string{
	"local":       projectIDTest,
	"development": projectIDTest,
	"testing":     projectIDTest,
	"staging":     projectIDProduction,
	"production":  projectIDProduction,
}

func NewPubsubxClient() *pubsubx.Client {
	if strings.ToLower(os.Getenv("IS_GITHUB_ACTION")) == "true" {
		return nil // skip pubsubx client creation in GitHub Actions
	}

	projID := projectIDMap[cfg.GetCurrentStage()]
	if projID == "" {
		panic("app is running in an unrecognized environment")
	}

	cli, err := pubsubx.New(context.Background(), projID)
	if err != nil {
		panic(err)
	}

	return cli
}
