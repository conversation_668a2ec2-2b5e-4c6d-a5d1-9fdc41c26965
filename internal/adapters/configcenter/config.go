package configcenter

import (
	cc "github.com/AfterShip/config-center-sdk-go"
	"github.com/AfterShip/config-center-sdk-go/mapper"
)

type Config struct {
	GCPServiceAccountKey GCPServiceAccountKey `config:"gcp-auth.service-account"`
	AESEncryptionKey     string               `config:"aes-encryption-key"`
}

// LoadFromCC loads configs from config center.
func LoadFromCC() (*Config, cc.StopFunc, error) {
	// Following are the default values to be load in local environment because cc.Once
	// will do nothing in local environment.
	config := &Config{}

	if err := cc.Once(config); err != nil {
		return nil, nil, err
	}
	stop, err := cc.Watch(config, func(conf interface{}, meta mapper.Result) {
		// ctx := context.Background()
		// Add your specific logic to handle configuration changes
		// Example: refreshConfig(ctx, params.MyConfigService, config.(conf.Config).MyConfig)
	})
	if err != nil {
		return nil, nil, err
	}
	return config, stop, nil
}

/* List all the custom config types below. Custom types are used for the ease of DI. */

// GCPServiceAccountKey is a JSON secret key provided by GCP for each service account.
// Ref: https://cloud.google.com/docs/authentication/production#cloud-console
type GCPServiceAccountKey string
