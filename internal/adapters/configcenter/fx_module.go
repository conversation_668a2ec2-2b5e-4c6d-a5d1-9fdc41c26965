package configcenter

import (
	"context"

	"go.uber.org/fx"

	cc "github.com/AfterShip/config-center-sdk-go"
)

type Params struct {
	fx.In
}

type Result struct {
	fx.Out

	StopFunc             cc.StopFunc
	GCPServiceAccountKey GCPServiceAccountKey
	AESEncryptionKey     string
}

func New(_ Params) Result {
	config, stopFunc, err := LoadFromCC()
	if err != nil {
		panic(err)
	}

	res := Result{
		StopFunc:             stopFunc,
		GCPServiceAccountKey: config.GCPServiceAccountKey,
		AESEncryptionKey:     config.AESEncryptionKey,
	}
	return res
}

const ModuleName = "config_center_config"

var Module = fx.Module(ModuleName,
	fx.Provide(New),
	fx.Invoke(func(lc fx.Lifecycle, stop cc.StopFunc) {
		lc.Append(fx.Hook{
			OnStop: func(context.Context) error {
				stop()
				return nil
			},
		})
	}),
)
