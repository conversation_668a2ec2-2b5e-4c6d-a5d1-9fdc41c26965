package logging

import (
	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/gopkg/log"
)

const (
	debug   = "debug"
	info    = "info"
	console = "console"
	json    = "json"
)

var loggerConfigs = map[string][2]string{
	"local":       {debug, console},
	"development": {debug, json},
	"testing":     {debug, json},
	"staging":     {info, json},
	"production":  {info, json},
}

func NewLogger() (*log.Logger, error) {
	config := loggerConfigs[cfg.GetCurrentStage()]
	return log.NewLogger(config[0], config[1], log.DefaultSamplingConfig())
}
