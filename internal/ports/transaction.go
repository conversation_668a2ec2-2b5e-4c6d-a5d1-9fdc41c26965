package ports

import (
	"context"
)

// TransactionPort defines the contract for transaction management.
type TransactionPort interface {
	// RunInTransaction executes the given function within a transaction.
	// If the function returns an error, the transaction will be rolled back.
	// If the function returns nil, the transaction will be committed.
	// Please note the fn should use the context passed to it for any operations
	RunInTransaction(ctx context.Context, fn func(context.Context) error) error
}

// ReturningTransactionPort defines the contract for transaction management for the cases needing a return value.
type ReturningTransactionPort[T any] interface {
	// RunInTransactionWithReturn executes the given function within a transaction
	// and returns a value along with an error.
	// If the function returns an error, the transaction will be rolled back.
	// If the function returns nil, the transaction will be committed.
	// The function should use the context passed to it for any operations.
	RunInTransaction(ctx context.Context, fn func(context.Context) (T, error)) (T, error)
}
