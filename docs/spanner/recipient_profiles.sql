CREATE TABLE recipient_profiles_v2 (
recipient_profile_id STRING(32) NOT NULL,
organization_id STRING(32) NOT NULL,
app_platform STRING(64) NOT NULL,
app_key STRING(256) NOT NULL,
email STRING(256) NOT NULL,
email_hash STRING(256) NOT NULL,
global_unsubscribed BOOL NOT NULL,
global_unsubscribed_effective_at TIMESTAMP,
global_unsubscribed_update_trigger STRING(64),
notification_preferences string(max),
suppressions string(max),
created_at TIMESTAMP NOT NULL,
updated_at TIMESTAMP,
) PRIMARY KEY (recipient_profile_id);

CREATE UNIQUE INDEX recipient_profiles_v2_by_organization_id_a_email_hash_a_app_platform_a_app_key
ON recipient_profiles (organization_id, email_hash, app_platform, app_key);