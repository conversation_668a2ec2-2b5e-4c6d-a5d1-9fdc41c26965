CREATE TABLE content_sections_v2
(
    content_section_id          STRING(32)  NOT NULL,
    host_product_code           STRING(32)  NOT NULL,
    handle                      STRING(128) NOT NULL,
    type                        STRING(128) NOT NULL,
    name                        STRING(128) NOT NULL,
    description                 STRING(1024),
    category                    STRING(128),
    ordering_weight             INT64,
    status                      STRING(32)  NOT NULL,
    hidden                      BOOL      NOT NULL,

    icon_url                    STRING(4096),
    applicable_setting          STRING(4096) NOT NULL,
    limitation_setting          STRING(4096) NOT NULL,
    input_setting               STRING(MAX),
    section_template_setting    STRING(MAX),
    presentation_settings       STRING(MAX),

    created_at                  TIMESTAMP NOT NULL,
    created_by                  STRING(64)  NOT NULL,
    updated_at                  TIMESTAMP,
    updated_by                  STRING(64),
    deleted_at                  TIMESTAMP,
    released_at                 TIMESTAMP,
    released_by                 STRING(64),
    released_version_id         STRING(32),
    rollbacked_at               TIMESTAMP,
    rollbacked_by               <PERSON>RING(64),
    rollbacked_version_id       STRING(32),
    previous_content_section_id STRING(32)
) PRIMARY KEY (content_section_id);

CREATE INDEX content_sections_by_host_product_code_a_handle_a ON content_sections_v2 (host_product_code, handle);


CREATE TABLE context_objects_v2
(
    context_object_id          STRING(32)  NOT NULL,
    host_product_code          STRING(32)  NOT NULL,
    handle                     STRING(128) NOT NULL,
    name                       STRING(128) NOT NULL,
    description                STRING(1024),
    category                   STRING(128),
    ordering_weight            INT64,
    status                     STRING(32)  NOT NULL,
    hidden                     BOOL      NOT NULL,

    context_key                STRING(128),
    applicable_setting         STRING(4096) NOT NULL,
    examples                   STRING(MAX) NOT NULL,

    created_at                 TIMESTAMP NOT NULL,
    created_by                 STRING(64)  NOT NULL,
    updated_at                 TIMESTAMP,
    updated_by                 STRING(64),
    deleted_at                 TIMESTAMP,
    released_at                TIMESTAMP,
    released_by                STRING(64),
    released_version_id        STRING(32),
    rollbacked_at               TIMESTAMP,
    rollbacked_by               STRING(64),
    rollbacked_version_id       STRING(32),
    previous_context_object_id STRING(32)
) PRIMARY KEY (context_object_id);

CREATE INDEX context_objects_by_host_product_code_a_handle_a ON context_objects_v2 (host_product_code, handle);

CREATE TABLE merge_tags_v2
(
    merge_tag_id          STRING(32)  NOT NULL,
    host_product_code     STRING(32)  NOT NULL,
    handle                STRING(128) NOT NULL,
    name                  STRING(128) NOT NULL,
    description           STRING(1024),
    category              STRING(128),
    ordering_weight       INT64,
    status                STRING(32)  NOT NULL,
    hidden                BOOL      NOT NULL,

    applicable_setting    STRING(4096) NOT NULL,
    merge_tag             STRING(128),
    merge_logic           STRING(4096),

    created_at            TIMESTAMP NOT NULL,
    created_by            STRING(64)  NOT NULL,
    updated_at            TIMESTAMP,
    updated_by            STRING(64),
    deleted_at            TIMESTAMP,
    released_at           TIMESTAMP,
    released_by           STRING(64),
    released_version_id   STRING(32),
    rollbacked_at               TIMESTAMP,
    rollbacked_by               STRING(64),
    rollbacked_version_id       STRING(32),
    previous_merge_tag_id STRING(32)
) PRIMARY KEY (merge_tag_id);

CREATE INDEX merge_tags_by_host_product_code_a_handle_a ON merge_tags_v2 (host_product_code, handle);


CREATE TABLE system_content_templates_v2
(
    system_content_template_id          STRING(32)  NOT NULL,
    host_product_code                   STRING(32)  NOT NULL,
    handle                              STRING(128) NOT NULL,
    name                                STRING(128) NOT NULL,
    description                         STRING(1024),
    category                            STRING(128),
    ordering_weight                     INT64,
    status                              STRING(32)  NOT NULL,
    hidden                              BOOL      NOT NULL,

    applicable_setting                  STRING(4096) NOT NULL,
    presentation_settings               STRING(MAX),
    preview_image_url                   STRING(4096),
    sms_template                        STRING(MAX),
    email_template                      STRING(MAX),
    checksums                           ARRAY<STRING(512)>,

    created_at                          TIMESTAMP NOT NULL,
    created_by                          STRING(64)  NOT NULL,
    updated_at                          TIMESTAMP,
    updated_by                          STRING(64),
    deleted_at                          TIMESTAMP,
    released_at                         TIMESTAMP,
    released_by                         STRING(64),
    released_version_id                 STRING(32),
    rollbacked_at               TIMESTAMP,
    rollbacked_by               STRING(64),
    rollbacked_version_id       STRING(32),
    previous_system_content_template_id STRING(32)
) PRIMARY KEY (system_content_template_id);

CREATE INDEX system_content_templates_by_host_product_code_a_handle_a ON system_content_templates_v2 (host_product_code, handle);
