CREATE TABLE email_messages_v2 (
email_message_id STRING(128) NOT NULL,
organization_id STRING(128) NOT NULL,
host_product_code STRING(64) NOT NULL,
sender STRING(1024) NOT NULL,
reply_to STRING(1024) NOT NULL,
send_to STRING(1024) NOT NULL,
unique_key STRING(1024) NOT NULL,
email_content STRING(MAX) NOT NULL,
data_tracking STRING(MAX) NOT NULL,
message_id STRING(128),
status STRING(64) NOT NULL,
created_at TIMESTAMP NOT NULL,
updated_at TIMESTAMP,
) PRIMARY KEY (email_message_id);

CREATE INDEX email_messages_v2_by_organization_id_a_host_product_code_a_unique_key
ON email_messages_v2(organization_id, host_product_code, unique_key);