CREATE TABLE subscription_compliance_rules_v2 (
subscription_compliance_rule_id STRING(32) NOT NULL,
host_product_code STRING(64) NOT NULL,
business_scenario STRING(64) NOT NULL,
rule_type STRING(64) NOT NULL,
allow_status string(1024),
deny_status string(1024),
description STRING(256),
created_at TIMESTAMP NOT NULL,
updated_at TIMESTAMP,
) PRIMARY KEY (subscription_compliance_rule_id);

CREATE INDEX subscription_compliance_rules_v2_by_host_product_code_a_business_scenario
ON subscription_compliance_rules_v2(host_product_code, business_scenario);