event_handlers:
#  - name: push-marketing-{{ env }}-flowsteps-cnt-checkouts-produce-checkout-trigger-event-v1
#    topic: connectors-{{ env }}-checkouts-automizely-v1
#    push_endpoint: {{ env_base_url }}/private-external-callback/eventhandlers/marketing-flows/trigger-event-producers/checkout/handle-cnt-checkout-event.action
#    filter: attributes.type="checkout" AND (attributes.event="create" OR attributes.event="update") AND attributes.x_app_name="automizely"

task_handlers:
#  - name: push-marketing-{{ env }}-flowsteps-start-contact-birthday-incoming-produce-job-v1
#    topic: marketing-{{ env }}-tasks-v1
#    push_endpoint: {{ env_base_url }}/private-external-callback/eventhandlers/trigger-event-producers/contact-birthday-incoming/handle-birthday-incoming-cronjob-event.action
#    filter: attributes.task = "produce_contact_birthday_incoming_trigger_event"