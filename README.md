##  1. <a name=''></a>运行环境

###  1.1. <a name='gcloudinit'>

按照 [GCP 本地环境配置](https://www.notion.so/automizely/GCP-97d40a492d224b629ef4135eb7ece39d) 配置好 GCP 开发环境

###  1.2. <a name='environments'></a>环境变量配置

* 设置环境变量 APP_ENV = local 用于加载 local 环境配置文件。
* 设置用于上报监控数据到 newrelic 的环境变量，NEW_RELIC_LICENSE_KEY，NEW_RELIC_INSERT_API_KEY
  从 1pwd 的 team vault 里获取 newrelic development 环境的 keys。
* 设置环境变量 NEW_RELIC_APP_NAME = pltf-nf-service 用于表示哪个项目做数据上报等。
* 设置 GOOGLE_APPLICATION_CREDENTIALS=Credentials 为生成 Credentials 文件路径。
* 设置私有仓库环境变量 GOPRIVATE = "github.com/AfterShip"


###  1.3. <a name='config-center'></a>Config Center 配置

部分配置接入了公司统一 config center，需要配置才能启动。
按照 [Config Center](https://www.notion.so/be4412a10af348e2a4f5592f565091fb) 初始化本地开发环境配置
1. 在配置中心找到 pltf-nf-service 项目
2. 找到 项目 owner，申请权限
3. 生成 development API key
4. 配置 Config Center 环境变量
```shell
   export CONFIG_CENTER_DEBUG=true
   export CONFIG_CENTER_ENV=development
   export CONFIG_CENTER_PROJECT_NAME=pltf-nf-service
   export CONFIG_CENTER_API_KEY=<dev-api-key>
```


###  1.4. <a name='-1'></a>编译运行
* 在 cmd/apiserver 目录下运行 sh ./script/build_bin.sh，产生 apiserver 二进制可执行文件
* 在 cmd/apiserver 目录下运行 ./apiserver 启动服务，会加载配置文件 conf/config-{APP_ENV}.toml


##  2. <a name='-1'></a>项目代码

###  2.1. <a name='-1'></a>主要项目结构说明

```
+-- cmd
|   +-- apiserver
|     +-- main.go
+-- docs
|   +-- spanner
|   +-- reference
+-- internal
|   +-- adapters
|   +-- application
|   +-- domain
|   +-- ports
|   +-- userinterface
+-- test
+-- tools
```

* cmd/apiserver: 主要包括项目入口函数。
* docs: 用于存储软件设计，数据库设计，api 文档等等的设计和方案。
* internal: 核心的业务代码，按照领域驱动设计进行划分。
* tools: 存放一些工具脚本文件。
