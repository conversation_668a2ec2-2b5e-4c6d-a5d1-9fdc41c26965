FROM asia-east1-docker.pkg.dev/aftership-admin/ci-artifacts/golang-onbuild:golang-1.23.0 as builder
WORKDIR  ${WORK_DIR}
RUN sh ./scripts/build.sh

FROM asia-east1-docker.pkg.dev/aftership-admin/ci-artifacts/other:ubuntu-22.04-v0.0.12

COPY --from=builder /deploy/pltf-nf-message/internal/userinterface/apiserver/conf/*.toml conf/
COPY --from=builder /deploy/pltf-nf-message/apiserver /usr/local/bin/
ENTRYPOINT ["apiserver"]